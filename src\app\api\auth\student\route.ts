import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON>pi } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

// POST /api/auth/student - Student authentication endpoint
export const POST = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      const { studentId, password } = body

      if (!studentId || !password) {
        return NextResponse.json(
          { error: 'Student ID and password are required' },
          { status: 400 }
        )
      }

      // Find student email account by student ID
      const account = await prisma.emailAccount.findUnique({
        where: { 
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true 
        },
        select: {
          id: true,
          email: true,
          password: true,
          displayName: true,
          studentId: true,
          rollNumber: true,
          course: true,
          batch: true,
          storageUsed: true,
          storageLimit: true,
          imapEnabled: true,
          pop3Enabled: true,
          smtpEnabled: true,
          createdAt: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        )
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, account.password)
      if (!isValidPassword) {
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        )
      }

      // Create student session
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      const session = await prisma.emailSession.create({
        data: {
          accountId: account.id,
          sessionType: 'STUDENT_PORTAL',
          clientInfo: context.request.headers.get('user-agent') || 'Unknown',
          ipAddress: context.clientIP,
          expiresAt,
          isActive: true
        }
      })

      // Generate JWT token for student session
      const token = jwt.sign(
        {
          sessionId: session.id,
          accountId: account.id,
          studentId: account.studentId,
          email: account.email,
          type: 'student'
        },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '24h' }
      )

      // Return student account data (without password)
      const { password: _, ...studentData } = account

      return NextResponse.json({
        success: true,
        token,
        sessionId: session.id,
        student: studentData,
        expiresAt: expiresAt.toISOString()
      })

    } catch (error) {
      console.error('Student authentication error:', error)
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // This endpoint handles its own authentication
    logAudit: true,
    sanitizeInput: true
  }
)

// GET /api/auth/student - Verify student session
export const GET = createSecureApi(
  async (context) => {
    try {
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)

      // Verify JWT token
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      // Check if session is still active
      const session = await prisma.emailSession.findUnique({
        where: {
          id: decoded.sessionId,
          isActive: true,
          expiresAt: { gt: new Date() }
        },
        include: {
          account: {
            select: {
              id: true,
              email: true,
              displayName: true,
              studentId: true,
              rollNumber: true,
              course: true,
              batch: true,
              storageUsed: true,
              storageLimit: true,
              imapEnabled: true,
              pop3Enabled: true,
              smtpEnabled: true,
              isActive: true
            }
          }
        }
      })

      if (!session || !session.account.isActive) {
        return NextResponse.json(
          { error: 'Session expired or invalid' },
          { status: 401 }
        )
      }

      // Update last activity
      await prisma.emailSession.update({
        where: { id: session.id },
        data: { lastActivity: new Date() }
      })

      return NextResponse.json({
        success: true,
        valid: true,
        student: session.account,
        sessionId: session.id,
        expiresAt: session.expiresAt.toISOString()
      })

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        return NextResponse.json(
          { error: 'Invalid token' },
          { status: 401 }
        )
      }

      console.error('Student session verification error:', error)
      return NextResponse.json(
        { error: 'Session verification failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false,
    logAudit: false
  }
)

// DELETE /api/auth/student - Student logout
export const DELETE = createSecureApi(
  async (context) => {
    try {
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)

      // Verify JWT token
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      // Deactivate session
      await prisma.emailSession.update({
        where: { id: decoded.sessionId },
        data: { isActive: false }
      })

      return NextResponse.json({
        success: true,
        message: 'Logged out successfully'
      })

    } catch (error) {
      console.error('Student logout error:', error)
      return NextResponse.json(
        { error: 'Logout failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false,
    logAudit: true
  }
)
