import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { sendEmail } from '@/lib/email-delivery'
import { processEmailQueue } from '@/lib/email-delivery'
import { EmailMessage, isInternalEmail } from '@/lib/email-server'
import { prisma } from '@/lib/prisma'

// POST /api/email/routing - Route email internally or externally
export const POST = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      const { 
        from, 
        to, 
        cc, 
        bcc, 
        subject, 
        body: emailBody, 
        bodyText,
        attachments,
        priority = 'NORMAL',
        routingType = 'auto' // 'auto', 'internal', 'external'
      } = body

      // Validate required fields
      if (!from || !to || !subject || !emailBody) {
        return NextResponse.json(
          { error: 'From, to, subject, and body are required' },
          { status: 400 }
        )
      }

      // Find sender account
      const senderAccount = await prisma.emailAccount.findUnique({
        where: { email: from, isActive: true }
      })

      if (!senderAccount) {
        return NextResponse.json(
          { error: 'Sender account not found or inactive' },
          { status: 404 }
        )
      }

      // Prepare recipients
      const allRecipients = [
        ...(Array.isArray(to) ? to : [to]),
        ...(cc ? (Array.isArray(cc) ? cc : [cc]) : []),
        ...(bcc ? (Array.isArray(bcc) ? bcc : [bcc]) : [])
      ]

      // Categorize recipients
      const internalRecipients = allRecipients.filter(email => isInternalEmail(email))
      const externalRecipients = allRecipients.filter(email => !isInternalEmail(email))

      const routingResults = {
        internal: { success: 0, failed: 0, errors: [] as string[] },
        external: { success: 0, failed: 0, errors: [] as string[] },
        messageIds: [] as string[]
      }

      // Handle internal routing
      if (internalRecipients.length > 0 && (routingType === 'auto' || routingType === 'internal')) {
        try {
          const internalEmailMessage: EmailMessage = {
            subject,
            body: emailBody,
            bodyText,
            fromEmail: from,
            fromName: senderAccount.displayName || undefined,
            toEmails: internalRecipients.filter(email => 
              (Array.isArray(to) ? to : [to]).includes(email)
            ),
            ccEmails: cc ? internalRecipients.filter(email => 
              (Array.isArray(cc) ? cc : [cc]).includes(email)
            ) : [],
            bccEmails: bcc ? internalRecipients.filter(email => 
              (Array.isArray(bcc) ? bcc : [bcc]).includes(email)
            ) : [],
            priority: priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT',
            attachments: attachments || []
          }

          const result = await sendEmail(senderAccount.id, internalEmailMessage)
          
          if (result.success) {
            routingResults.internal.success = internalRecipients.length
            routingResults.messageIds.push(result.messageId)
          } else {
            routingResults.internal.failed = internalRecipients.length
            routingResults.internal.errors.push(result.error || 'Internal routing failed')
          }
        } catch (error) {
          routingResults.internal.failed = internalRecipients.length
          routingResults.internal.errors.push('Internal routing error: ' + (error as Error).message)
        }
      }

      // Handle external routing
      if (externalRecipients.length > 0 && (routingType === 'auto' || routingType === 'external')) {
        try {
          const externalEmailMessage: EmailMessage = {
            subject,
            body: emailBody,
            bodyText,
            fromEmail: from,
            fromName: senderAccount.displayName || undefined,
            toEmails: externalRecipients.filter(email => 
              (Array.isArray(to) ? to : [to]).includes(email)
            ),
            ccEmails: cc ? externalRecipients.filter(email => 
              (Array.isArray(cc) ? cc : [cc]).includes(email)
            ) : [],
            bccEmails: bcc ? externalRecipients.filter(email => 
              (Array.isArray(bcc) ? bcc : [bcc]).includes(email)
            ) : [],
            priority: priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT',
            attachments: attachments || []
          }

          const result = await sendEmail(senderAccount.id, externalEmailMessage)
          
          if (result.success) {
            routingResults.external.success = externalRecipients.length
            routingResults.messageIds.push(result.messageId)
          } else {
            routingResults.external.failed = externalRecipients.length
            routingResults.external.errors.push(result.error || 'External routing failed')
          }
        } catch (error) {
          routingResults.external.failed = externalRecipients.length
          routingResults.external.errors.push('External routing error: ' + (error as Error).message)
        }
      }

      // Determine overall success
      const totalSuccess = routingResults.internal.success + routingResults.external.success
      const totalFailed = routingResults.internal.failed + routingResults.external.failed
      const overallSuccess = totalSuccess > 0 && totalFailed === 0

      return NextResponse.json({
        success: overallSuccess,
        routing: {
          internal: {
            recipients: internalRecipients,
            ...routingResults.internal
          },
          external: {
            recipients: externalRecipients,
            ...routingResults.external
          }
        },
        messageIds: routingResults.messageIds,
        summary: {
          totalRecipients: allRecipients.length,
          successfulDeliveries: totalSuccess,
          failedDeliveries: totalFailed
        }
      })

    } catch (error) {
      console.error('Email routing error:', error)
      return NextResponse.json(
        { error: 'Email routing failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: true,
    sanitizeInput: true
  }
)

// GET /api/email/routing - Get routing statistics and status
export const GET = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const timeframe = searchParams.get('timeframe') || '24h' // 1h, 24h, 7d, 30d

      // Calculate time range
      let startDate: Date
      const now = new Date()

      switch (timeframe) {
        case '1h':
          startDate = new Date(now.getTime() - 60 * 60 * 1000)
          break
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '24h':
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
      }

      // Get email statistics
      const emailStats = await prisma.email.groupBy({
        by: ['sentAt'],
        where: {
          sentAt: {
            gte: startDate
          }
        },
        _count: {
          id: true
        }
      })

      // Get queue statistics
      const queueStats = await prisma.emailQueue.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      })

      // Get delivery statistics
      const deliveryStats = await prisma.emailDeliveryLog.groupBy({
        by: ['status'],
        where: {
          createdAt: {
            gte: startDate
          }
        },
        _count: {
          status: true
        }
      })

      // Get internal vs external routing stats
      const internalEmails = await prisma.emailRecipient.count({
        where: {
          email: {
            sentAt: {
              gte: startDate
            }
          },
          account: {
            email: {
              endsWith: process.env.EMAIL_DOMAIN || 'institute.edu'
            }
          }
        }
      })

      const totalEmails = await prisma.email.count({
        where: {
          sentAt: {
            gte: startDate
          }
        }
      })

      // Calculate routing efficiency
      const queueStatsMap = queueStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status
        return acc
      }, {} as Record<string, number>)

      const deliveryStatsMap = deliveryStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status
        return acc
      }, {} as Record<string, number>)

      return NextResponse.json({
        success: true,
        timeframe,
        statistics: {
          emails: {
            total: totalEmails,
            internal: internalEmails,
            external: totalEmails - internalEmails,
            internalPercentage: totalEmails > 0 ? (internalEmails / totalEmails) * 100 : 0
          },
          queue: {
            pending: queueStatsMap.PENDING || 0,
            processing: queueStatsMap.PROCESSING || 0,
            sent: queueStatsMap.SENT || 0,
            failed: queueStatsMap.FAILED || 0,
            cancelled: queueStatsMap.CANCELLED || 0
          },
          delivery: {
            sent: deliveryStatsMap.SENT || 0,
            delivered: deliveryStatsMap.DELIVERED || 0,
            bounced: deliveryStatsMap.BOUNCED || 0,
            failed: deliveryStatsMap.FAILED || 0,
            deferred: deliveryStatsMap.DEFERRED || 0
          },
          efficiency: {
            queueProcessingRate: queueStatsMap.SENT && (queueStatsMap.SENT + queueStatsMap.FAILED) ? 
              (queueStatsMap.SENT / (queueStatsMap.SENT + queueStatsMap.FAILED)) * 100 : 0,
            deliverySuccessRate: deliveryStatsMap.SENT && (deliveryStatsMap.SENT + deliveryStatsMap.FAILED) ?
              (deliveryStatsMap.SENT / (deliveryStatsMap.SENT + deliveryStatsMap.FAILED)) * 100 : 0
          }
        }
      })

    } catch (error) {
      console.error('Routing statistics error:', error)
      return NextResponse.json(
        { error: 'Failed to get routing statistics' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: false
  }
)
