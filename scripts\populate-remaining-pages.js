const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function populateRemainingPages() {
  console.log('🌱 Populating remaining content pages with authentic SNPITC data...')

  try {
    // Get the first admin user to assign as creator
    const adminUser = await prisma.user.findFirst({
      where: {
        role: 'ADMIN'
      }
    })

    if (!adminUser) {
      console.error('❌ No admin user found. Please create an admin user first.')
      return
    }

    console.log(`👤 Using admin user: ${adminUser.email}`)

    const pages = [
      {
        title: 'Electric Power Supply',
        slug: 'electric-power',
        description: 'Details about electric power supply and electrical infrastructure at our institute.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Electric Power Supply</h2>
            <p class="mb-6">Complete information about our electrical infrastructure and power supply details.</p>
            
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scanned Document / Image</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Present Electric Load</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">11 KW</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                      <a href="/images/electricity/electricity.jpg" target="_blank" class="hover:underline">View Image</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Date of Connection</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">16-03-2009</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                      <a href="/images/electricity/electricity.jpg" target="_blank" class="hover:underline">View Image</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Connection in the name of</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Head Master S.N. Industries Prashikshan Kendra</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                      <a href="/images/electricity/electricity.jpg" target="_blank" class="hover:underline">View Image</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Meter No.</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">523679</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                      <a href="/images/electricity/electricity.jpg" target="_blank" class="hover:underline">View Image</a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
              <div class="bg-yellow-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-yellow-900 mb-4">Power Specifications</h3>
                <ul class="text-yellow-800 space-y-2">
                  <li>• Total Load Capacity: 11 KW</li>
                  <li>• Connection Date: March 16, 2009</li>
                  <li>• Meter Number: 523679</li>
                  <li>• Registered Name: Head Master S.N. Industries Prashikshan Kendra</li>
                </ul>
              </div>
              <div class="bg-green-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-green-900 mb-4">Power Usage</h3>
                <ul class="text-green-800 space-y-2">
                  <li>• Workshop Equipment</li>
                  <li>• Classroom Lighting</li>
                  <li>• Computer Lab</li>
                  <li>• Administrative Offices</li>
                  <li>• Library Facilities</li>
                </ul>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Electric Power Supply - S.N. ITI',
        metaDesc: 'Details about electric power supply and electrical infrastructure at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Library',
        slug: 'library',
        description: 'Complete information about our library facilities and available books.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Library</h2>
            <p class="mb-6">Our library is equipped with comprehensive technical books and resources to support student learning.</p>
            
            <h3 class="text-xl font-semibold mb-4">List of Available Books</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No.</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trade</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Book</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Publisher</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr><td class="px-6 py-4 text-sm text-gray-500">1</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician trade theory 1st semester</td><td class="px-6 py-4 text-sm text-gray-500">10</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">2</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician trade theory 2nd semester</td><td class="px-6 py-4 text-sm text-gray-500">10</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">3</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician trade theory 3rd semester</td><td class="px-6 py-4 text-sm text-gray-500">10</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">4</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician trade theory 4th semester</td><td class="px-6 py-4 text-sm text-gray-500">10</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">5</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Engineering drawing</td><td class="px-6 py-4 text-sm text-gray-500">13</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">6</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Workshop calculation</td><td class="px-6 py-4 text-sm text-gray-500">15</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">7</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician practical 1st semester</td><td class="px-6 py-4 text-sm text-gray-500">09</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">8</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician practical 2nd semester</td><td class="px-6 py-4 text-sm text-gray-500">09</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">9</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician practical 3rd semester</td><td class="px-6 py-4 text-sm text-gray-500">06</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">10</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician practical 4th semester</td><td class="px-6 py-4 text-sm text-gray-500">02</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">11</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Electrician assignment/test</td><td class="px-6 py-4 text-sm text-gray-500">11</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">12</td><td class="px-6 py-4 text-sm text-gray-900">Electrician</td><td class="px-6 py-4 text-sm text-gray-500">Instructor guide</td><td class="px-6 py-4 text-sm text-gray-500">03</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">13</td><td class="px-6 py-4 text-sm text-gray-900">Insurance agent</td><td class="px-6 py-4 text-sm text-gray-500">Insurance I-33</td><td class="px-6 py-4 text-sm text-gray-500">03</td><td class="px-6 py-4 text-sm text-gray-500">NIMI</td></tr>
                </tbody>
              </table>
            </div>
            
            <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-blue-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Total Books</h3>
                <p class="text-3xl font-bold text-blue-600">118</p>
                <p class="text-sm text-blue-700">Available Books</p>
              </div>
              <div class="bg-green-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-green-900 mb-2">Library Area</h3>
                <p class="text-3xl font-bold text-green-600">27.5</p>
                <p class="text-sm text-green-700">Square Meters</p>
              </div>
              <div class="bg-purple-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-purple-900 mb-2">Publisher</h3>
                <p class="text-3xl font-bold text-purple-600">NIMI</p>
                <p class="text-sm text-purple-700">Primary Publisher</p>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Library - S.N. ITI',
        metaDesc: 'Complete information about library facilities and available books at S.N. Private Industrial Training Institute.'
      }
    ]

    // Create pages
    for (const pageData of pages) {
      // Check if page already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: pageData.slug }
      })

      if (existingPage) {
        console.log(`📄 Updating existing page: ${pageData.title}`)
        await prisma.page.update({
          where: { slug: pageData.slug },
          data: {
            ...pageData,
            updatedAt: new Date()
          }
        })
      } else {
        console.log(`📄 Creating new page: ${pageData.title}`)
        await prisma.page.create({
          data: {
            ...pageData,
            createdById: adminUser.id,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
    }

    console.log('🎉 Remaining content pages populated successfully!')
    console.log(`📊 Total pages processed: ${pages.length}`)

  } catch (error) {
    console.error('❌ Error populating remaining content pages:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the population function
populateRemainingPages()
  .catch((error) => {
    console.error('❌ Population failed:', error)
    process.exit(1)
  })
