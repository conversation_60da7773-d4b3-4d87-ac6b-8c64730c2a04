# 🚀 EMAIL SERVER STARTUP VERIFICATION GUIDE

## 📋 **PRE-STARTUP CHECKLIST**

Before starting the development server, ensure the following:

### ✅ **Environment Setup**
- [ ] `.env` file exists in the root directory
- [ ] All required environment variables are configured
- [ ] Node.js (v18+) and npm are installed
- [ ] Project dependencies are installed (`npm install`)

### ✅ **Database Preparation**
- [ ] Prisma client is generated (`npx prisma generate`)
- [ ] Database schema is created (`npx prisma db push`)
- [ ] Database connection is working

---

## 🔧 **STARTUP COMMANDS**

### **Option 1: Automated Startup (Recommended)**

**For Windows:**
```bash
scripts\startup-verification.bat
```

**For Unix/Linux/macOS:**
```bash
chmod +x scripts/startup-verification.sh
./scripts/startup-verification.sh
```

### **Option 2: Manual Step-by-Step Startup**

1. **Generate Prisma Client:**
   ```bash
   npx prisma generate
   ```

2. **Setup Database:**
   ```bash
   npx prisma db push
   ```

3. **Start Development Server:**
   ```bash
   npm run dev
   ```

---

## 🌐 **ACCESS POINTS**

Once the server starts successfully, access these URLs:

### **Main Application**
- **Homepage**: http://localhost:3000
- **Health Check**: http://localhost:3000/api/system/health

### **Student Portal**
- **Login**: http://localhost:3000/student/login
- **Dashboard**: http://localhost:3000/student/dashboard
- **Email Inbox**: http://localhost:3000/student/email/inbox
- **Email Compose**: http://localhost:3000/student/email/compose

### **Admin Portal**
- **Login**: http://localhost:3000/admin/login
- **Dashboard**: http://localhost:3000/admin
- **User Management**: http://localhost:3000/admin/users
- **Email Management**: http://localhost:3000/admin/email/accounts

---

## 🧪 **SYSTEM VERIFICATION TESTS**

### **1. Server Startup Verification**
- [ ] Server starts without compilation errors
- [ ] No TypeScript errors in console
- [ ] Database connection established
- [ ] All environment variables loaded

### **2. Student Portal Tests**
- [ ] Student login page loads (`/student/login`)
- [ ] Can access student dashboard after login
- [ ] Email inbox page loads without errors
- [ ] Email compose page is functional
- [ ] Payment page loads correctly

### **3. Admin Portal Tests**
- [ ] Admin login page loads (`/admin/login`)
- [ ] Admin dashboard accessible after login
- [ ] User management page loads
- [ ] Email oversight page functional
- [ ] System settings accessible

### **4. API Endpoint Tests**
- [ ] Health check API responds (`/api/system/health`)
- [ ] Student authentication API works (`/api/auth/student`)
- [ ] Admin authentication API works (`/api/auth/[...nextauth]`)
- [ ] Email APIs respond correctly

---

## 🔑 **TEST CREDENTIALS**

### **Default Admin Account**
```
Email: <EMAIL>
Password: admin123
```

### **Test Student Account**
```
Student ID: TEST001
Password: student123
```

*Note: These are default test credentials. In production, use secure passwords.*

---

## 🐛 **TROUBLESHOOTING**

### **Common Issues and Solutions**

#### **Issue: "Prisma Client not found"**
**Solution:**
```bash
npx prisma generate
```

#### **Issue: "Database connection failed"**
**Solution:**
1. Check `.env` file has correct `DATABASE_URL`
2. Run: `npx prisma db push`
3. Verify database file exists (for SQLite)

#### **Issue: "Port 3000 already in use"**
**Solution:**
```bash
# Kill process on port 3000
npx kill-port 3000

# Or start on different port
npm run dev -- --port 3001
```

#### **Issue: "Environment variables not loaded"**
**Solution:**
1. Ensure `.env` file exists in root directory
2. Check file has no syntax errors
3. Restart the development server

#### **Issue: "TypeScript compilation errors"**
**Solution:**
```bash
# Check for errors
npm run type-check

# Fix and restart server
npm run dev
```

---

## 📊 **STARTUP SUCCESS INDICATORS**

### **✅ Successful Startup Should Show:**

```
✅ Environment file found
✅ Prisma client generated successfully
✅ Database schema created successfully
✅ Dependencies verified
✅ Server starting on http://localhost:3000

Ready - started server on 0.0.0.0:3000, url: http://localhost:3000
```

### **✅ Browser Tests Should Pass:**
- [ ] Homepage loads without errors
- [ ] Student login page displays correctly
- [ ] Admin login page displays correctly
- [ ] No console errors in browser developer tools
- [ ] All CSS styles load properly

---

## 🎯 **VERIFICATION COMPLETION**

Once all tests pass, the email server development environment is ready for:

- ✅ **Student Portal Development**
- ✅ **Admin Portal Development**
- ✅ **Email System Testing**
- ✅ **Payment Integration Testing**
- ✅ **API Development and Testing**

---

## 📞 **SUPPORT**

If you encounter issues during startup:

1. **Check the console output** for specific error messages
2. **Verify all prerequisites** are installed and configured
3. **Review the troubleshooting section** above
4. **Check the logs** in the terminal for detailed error information

---

## 🎉 **SUCCESS!**

When the development server starts successfully, you'll have:

- **Full Email Server Functionality**
- **Student Portal Access**
- **Admin Portal Access**
- **Payment Processing**
- **Database Integration**
- **API Endpoints**

**Ready for development and testing!** 🚀
