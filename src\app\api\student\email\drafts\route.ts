import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import { saveDraft } from '@/lib/email-server'
import jwt from 'jsonwebtoken'

// GET /api/student/email/drafts - Get drafts for student
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Get drafts folder
      const draftsFolder = await prisma.emailFolder.findFirst({
        where: {
          accountId: account.id,
          folderType: 'DRAFTS'
        }
      })

      if (!draftsFolder) {
        return NextResponse.json({
          success: true,
          drafts: [],
          totalCount: 0
        })
      }

      // Get pagination parameters
      const { searchParams } = new URL(context.request.url)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
      const skip = (page - 1) * limit

      // Get drafts
      const drafts = await prisma.email.findMany({
        where: {
          fromAccountId: account.id,
          sentAt: null, // Only drafts (unsent emails)
          isDeleted: false,
          recipients: {
            some: {
              accountId: account.id,
              folderId: draftsFolder.id
            }
          }
        },
        include: {
          attachments: {
            select: {
              id: true,
              filename: true,
              originalName: true,
              size: true,
              mimeType: true
            }
          }
        },
        orderBy: {
          updatedAt: 'desc'
        },
        skip,
        take: limit
      })

      // Get total count
      const totalCount = await prisma.email.count({
        where: {
          fromAccountId: account.id,
          sentAt: null, // Only drafts (unsent emails)
          isDeleted: false,
          recipients: {
            some: {
              accountId: account.id,
              folderId: draftsFolder.id
            }
          }
        }
      })

      // Format drafts
      const formattedDrafts = drafts.map(draft => ({
        id: draft.id,
        subject: draft.subject,
        body: draft.body,
        bodyText: draft.bodyText,
        toEmails: draft.toEmails || [],
        ccEmails: draft.ccEmails || [],
        bccEmails: draft.bccEmails || [],
        priority: draft.priority,
        attachments: draft.attachments,
        createdAt: draft.createdAt,
        updatedAt: draft.updatedAt
      }))

      return NextResponse.json({
        success: true,
        drafts: formattedDrafts,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasMore: skip + drafts.length < totalCount
        }
      })

    } catch (error) {
      console.error('Student drafts get error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve drafts' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)

// POST /api/student/email/drafts - Save draft for student
export const POST = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId

      // Get request body
      const body = await context.request.json()
      const {
        to = [],
        cc = [],
        bcc = [],
        subject = '',
        body: emailBody = '',
        priority = 'NORMAL',
        attachments = []
      } = body

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Prepare draft message
      const draftMessage = {
        subject: subject.trim(),
        body: emailBody,
        bodyText: emailBody.replace(/<[^>]*>/g, ''), // Strip HTML for text version
        fromEmail: account.email,
        fromName: account.displayName || account.studentId,
        toEmails: to.map((email: string) => email.trim()).filter(Boolean),
        ccEmails: cc.map((email: string) => email.trim()).filter(Boolean),
        bccEmails: bcc.map((email: string) => email.trim()).filter(Boolean),
        attachments: attachments || [],
        priority: priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT',
        isDraft: true
      }

      // Save draft using the email server
      const result = await saveDraft(account.id, draftMessage)

      if (result.success) {
        return NextResponse.json({
          success: true,
          draftId: result.draftId,
          message: 'Draft saved successfully'
        })
      } else {
        return NextResponse.json(
          { error: result.error || 'Failed to save draft' },
          { status: 500 }
        )
      }

    } catch (error) {
      console.error('Student draft save error:', error)
      return NextResponse.json(
        { error: 'Failed to save draft' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)
