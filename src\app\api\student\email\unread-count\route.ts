import { NextRequest, NextResponse } from 'next/server'
import { createS<PERSON>ure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/email/unread-count - Get unread email count for student
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const accountId = decoded.accountId

      // Get unread email count
      const unreadCount = await prisma.emailRecipient.count({
        where: {
          accountId,
          isDeleted: false,
          isRead: false,
          folder: {
            folderType: 'INBOX'
          }
        }
      })

      return NextResponse.json({
        success: true,
        count: unreadCount
      })

    } catch (error) {
      console.error('Student unread count error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve unread count' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)
