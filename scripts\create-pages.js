const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const pagesData = [
  // About Us Section
  {
    title: 'About Institute',
    slug: 'about-institute',
    description: 'Complete information about S.N. Pvt. Industrial Training Institute including establishment details, contact information, and institutional overview.',
    content: `
      <h2>About Institute</h2>
      <table class="table table-bordered">
        <tbody>
          <tr>
            <td><strong>Sl. No.</strong></td>
            <td><strong>Field Name</strong></td>
            <td><strong>Details</strong></td>
          </tr>
          <tr>
            <td>a</td>
            <td>Name of The Institute</td>
            <td>S.N. Pvt. Industrial Training Institute</td>
          </tr>
          <tr>
            <td>b</td>
            <td>Address of the Institute</td>
            <td>D-117, Kaka Colony, Gandhi Vidhya Mandir, Teh.-<PERSON><PERSON><PERSON>, Dist. Chu<PERSON></td>
          </tr>
          <tr>
            <td>c</td>
            <td>Date of Establishment</td>
            <td>17-08-2009</td>
          </tr>
          <tr>
            <td>d</td>
            <td>DGET File Reference No.</td>
            <td>DGET-6/20/35/2009-TC(New Pvt)</td>
          </tr>
          <tr>
            <td>e</td>
            <td>Code Allotted by DGET</td>
            <td>P-574</td>
          </tr>
          <tr>
            <td>f</td>
            <td>Contact No.</td>
            <td>9571075067</td>
          </tr>
          <tr>
            <td>g</td>
            <td>Mobile No.</td>
            <td>9414947801</td>
          </tr>
          <tr>
            <td>h</td>
            <td>Fax No.</td>
            <td></td>
          </tr>
          <tr>
            <td>i</td>
            <td>E mail ID</td>
            <td><EMAIL></td>
          </tr>
          <tr>
            <td>j</td>
            <td>Location-Rural/urban</td>
            <td>Urban</td>
          </tr>
          <tr>
            <td>k</td>
            <td>Approach / How to Reach</td>
            <td></td>
          </tr>
        </tbody>
      </table>
    `,
    metaTitle: 'About Institute - S.N. Pvt. Industrial Training Institute',
    metaDesc: 'Learn about S.N. Pvt. Industrial Training Institute, established in 2009, affiliated to NCVT (DGE&T) Govt. of India, located in Churu, Rajasthan.',
    status: 'PUBLISHED'
  },
  {
    title: 'Introduction of Institute',
    slug: 'introduction-institute',
    description: 'Detailed introduction of S.N. Pvt. Industrial Training Institute, its objectives, mission, vision, and managing society information.',
    content: `
      <h2>Introduction</h2>
      <p>SN Private Industrial Training Institute a constituent activity of Nav Chetana Shikhshan Sansthan was established in the year 2009 to fulfil the first of the objective of its managing society i.e. preparing employable youth through technical education.</p>
      
      <p>Nav Chetana Shikhshan Sansthan the managing society of this institute is a society registered under Rajasthan societies registration act. 1958 in the office of Assistant Registrar (societies) vide registration no. 104/Churu/2008-2009 0n 15 Jan 2009.</p>
      
      <p>This institute was founded by a retired police officer Shri RamiLal Saharan with an objective of overall development of the society with special emphasis on educating and imparting employable skills to the youth so that they can earn their livelihood and serve the nation. The other targeted areas of work of the institute include development of animal husbandry, agriculture, health and hygiene of the people.</p>
      
      <p>SN Private Industrial Training Institute is category I- institute where trades/ units have already been accorded affiliation to NCVT and it has applied for the extension of the units in the electrician trade.</p>
      
      <h3>Objectives</h3>
      <ul>
        <li>To introduce at least three most sought trades of the nearby industries viz. electrician, fitter, mechanic (diesel) in next 2-3 years.</li>
        <li>To expose students to minimum of three companies as industry inter phase program.</li>
        <li>To achieve 100 per cent placement for the passed out trainees.</li>
      </ul>
      
      <h3>Mission And Vision</h3>
      <p>We at SN Private Industrial Training Institute are committed to develop skill sets suitable to the advancement of manufacturing and service sector so that</p>
      <ol>
        <li>We can be recognized as an Excellent Organization Providing World Class Technical Education at all Levels.</li>
        <li>Striving for Excellence in Technical Education</li>
      </ol>
      
      <h3>Managing Society/Trust/Company</h3>
      <table class="table table-bordered">
        <tbody>
          <tr>
            <td><strong>Detail</strong></td>
            <td><strong>Info.</strong></td>
          </tr>
          <tr>
            <td>Name of The Society/Trust/Company</td>
            <td>Nav Chetana Shikshan Sansthan, Sardarshahar</td>
          </tr>
          <tr>
            <td>Registration Number and its Validity</td>
            <td>S.N. - 104/Churu/ 2008-2009</td>
          </tr>
          <tr>
            <td>Scanned Copy Of related Certificates Hyperlinked</td>
            <td>Available on request</td>
          </tr>
          <tr>
            <td>Remarks</td>
            <td></td>
          </tr>
        </tbody>
      </table>
    `,
    metaTitle: 'Introduction of Institute - S.N. Pvt. ITI',
    metaDesc: 'Introduction to S.N. Pvt. Industrial Training Institute, its objectives, mission, vision, and managing society Nav Chetana Shikshan Sansthan.',
    status: 'PUBLISHED'
  },
  {
    title: 'Scheme Running in The Institute',
    slug: 'scheme-running',
    description: 'Information about various schemes running in the institute including CTS and MES under NCVT.',
    content: `
      <h2>Schemes running in the institute (CTS and MES etc. under NCVT)</h2>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Name of the Scheme</th>
            <th>Total Capacity</th>
            <th>Duration</th>
            <th>Admitted in Current Financial Year</th>
            <th>Current Status</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Craftsmen Training Scheme</td>
            <td>Electrician 126</td>
            <td>2 Years</td>
            <td>126</td>
            <td>Training continuing</td>
          </tr>
          <tr>
            <td>Craftsmen Training Scheme</td>
            <td>Nil</td>
            <td>Nil</td>
            <td>Nil</td>
            <td>Nil</td>
          </tr>
          <tr>
            <td>Modular Employable Scheme</td>
            <td>Nil</td>
            <td>Nil</td>
            <td>Nil</td>
            <td>Nil</td>
          </tr>
          <tr>
            <td>Other Schemes</td>
            <td>Nil</td>
            <td>Nil</td>
            <td>Nil</td>
            <td>Nil</td>
          </tr>
        </tbody>
      </table>
    `,
    metaTitle: 'Schemes Running in Institute - S.N. Pvt. ITI',
    metaDesc: 'Details of various training schemes running in S.N. Pvt. ITI including Craftsmen Training Scheme and Modular Employable Scheme under NCVT.',
    status: 'PUBLISHED'
  },
  {
    title: 'Admission Criteria',
    slug: 'admission-criteria',
    description: 'Detailed admission criteria, eligibility requirements, and admission procedure for various trades offered at the institute.',
    content: `
      <h2>Admission Criteria</h2>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Name of Trade under NCVT</th>
            <th>Duration of Training</th>
            <th>Eligibility of Qualification</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Electrician</td>
            <td>2 Years</td>
            <td>10th Pass</td>
          </tr>
        </tbody>
      </table>
      
      <h3>Admission Procedure</h3>
      <p>We strictly follow the admission policy and procedure given by the Rajasthan Directorate of technical education Jodhpur. We shall admit the students purely on the merit based on the marks secured by the candidate in the public examinations based on the minimum qualifications prescribed for the respective trade only as per the maximum number of students allowed for each trade.</p>
      
      <p>We maintain safe custody of the documents submitted by the students including birth certificates, certificates of previous Institution and deposits, if any. The following is the procedure for the admission:</p>
      
      <ol>
        <li>The dates for different trades to be announced.</li>
        <li>Advertisement shall be carried in the local areas through paper media, miking, and one to one canvassing etc.</li>
        <li>All the enquiries personnel and telephonic shall be recorded in the register.</li>
        <li>A date for interview to be announced and informed to the potential candidates</li>
        <li>The faculties shall conduct interview and select requisite no. of candidates based on merit.</li>
        <li>While selecting the candidates the reservation criteria as per NCVT /DTE Jodhpur guidelines shall be adhered.</li>
        <li>A first list of selected candidates will be display and cut-off date shall be announced.</li>
        <li>After cut-off first list second is released, if necessary third and fourth to be continued.</li>
        <li>All the selected candidates shall be registered and sent to their respective classes.</li>
      </ol>
      
      <p>We shall reserves seats for Schedule Caste, Schedule Tribe, and OBC as per the policy of Rajasthan / and Central Government.</p>
    `,
    metaTitle: 'Admission Criteria - S.N. Pvt. ITI',
    metaDesc: 'Admission criteria, eligibility requirements, and detailed admission procedure for trades offered at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Trades Affilated To NCVT and SCVT',
    slug: 'ncvt-scvt-affilated',
    description: 'Information about trades affiliated to NCVT and SCVT with seating capacity and syllabus details.',
    content: `
      <h2>Trades Affilated To NCVT And SCVT</h2>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Trades linked with Syllabus</th>
            <th>No. of Units</th>
            <th>Shifts Running</th>
            <th>Seating Capacity per Unit</th>
            <th>Total Seating Capacity</th>
            <th>Copy of DGET order link</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><a href="https://www.ncvtonline.com/2022/10/iti-electrician-syllabus-download-in.html" target="_blank">ELECTRICIAN</a></td>
            <td>6</td>
            <td>2</td>
            <td>20</td>
            <td>80</td>
            <td>Available on request</td>
          </tr>
        </tbody>
      </table>
    `,
    metaTitle: 'Trades Affiliated to NCVT and SCVT - S.N. Pvt. ITI',
    metaDesc: 'Details of trades affiliated to NCVT and SCVT at S.N. Pvt. ITI including Electrician trade with seating capacity and syllabus information.',
    status: 'PUBLISHED'
  },
  {
    title: 'Summary of Trades Affilated To NCVT',
    slug: 'ncvt-affilated',
    description: 'Summary of trades affiliated to NCVT (National Council for Vocational Training) at the institute.',
    content: `
      <h2>Summary of Trades Affilated To NCVT</h2>
      <p>S.N. Pvt. Industrial Training Institute is affiliated to NCVT (National Council for Vocational Training) under the Directorate General of Employment & Training (DGE&T), Government of India.</p>

      <h3>NCVT Affiliated Trade:</h3>
      <ul>
        <li><strong>Electrician Trade</strong>
          <ul>
            <li>Duration: 2 Years</li>
            <li>Total Capacity: 126 seats</li>
            <li>Eligibility: 10th Pass</li>
            <li>Affiliation Year: 2009</li>
          </ul>
        </li>
      </ul>

      <p>The institute follows NCVT curriculum and examination pattern. Upon successful completion, students receive NCVT certificates which are recognized nationwide and provide better employment opportunities.</p>
    `,
    metaTitle: 'NCVT Affiliated Trades - S.N. Pvt. ITI',
    metaDesc: 'Summary of trades affiliated to NCVT at S.N. Pvt. ITI including Electrician trade with duration, capacity, and eligibility details.',
    status: 'PUBLISHED'
  },
  {
    title: 'Summary of Trades Affilated To SCVT',
    slug: 'scvt-affilated',
    description: 'Summary of trades affiliated to SCVT (State Council for Vocational Training) at the institute.',
    content: `
      <h2>Summary of Trades Affilated To SCVT</h2>
      <p>Currently, S.N. Pvt. Industrial Training Institute does not have any trades affiliated to SCVT (State Council for Vocational Training).</p>

      <p>The institute is primarily focused on NCVT affiliated trades which provide national level recognition and better employment opportunities across India.</p>

      <p>For any future SCVT affiliations, the institute will update this information accordingly.</p>
    `,
    metaTitle: 'SCVT Affiliated Trades - S.N. Pvt. ITI',
    metaDesc: 'Information about SCVT affiliated trades at S.N. Pvt. ITI. Currently focused on NCVT trades for national recognition.',
    status: 'PUBLISHED'
  },
  {
    title: 'Application Format',
    slug: 'application-format',
    description: 'Application format and procedure for admission to various trades at the institute.',
    content: `
      <h2>Application Format</h2>
      <p>Students seeking admission to S.N. Pvt. Industrial Training Institute must follow the prescribed application format and procedure.</p>

      <h3>Required Documents:</h3>
      <ul>
        <li>Duly filled application form</li>
        <li>10th class mark sheet and certificate</li>
        <li>Birth certificate</li>
        <li>Caste certificate (if applicable)</li>
        <li>Income certificate (if applicable)</li>
        <li>Passport size photographs</li>
        <li>Aadhar card copy</li>
      </ul>

      <h3>Application Procedure:</h3>
      <ol>
        <li>Obtain application form from the institute office</li>
        <li>Fill the form completely with accurate information</li>
        <li>Attach all required documents</li>
        <li>Submit the form within the specified deadline</li>
        <li>Pay the application fee as prescribed</li>
        <li>Attend the interview/counseling session</li>
      </ol>

      <p>For more information about application forms and admission process, please contact the institute office.</p>

      <p><strong>Contact:</strong> 9414947801, 9571075067<br>
      <strong>Email:</strong> <EMAIL></p>
    `,
    metaTitle: 'Application Format - S.N. Pvt. ITI',
    metaDesc: 'Application format, required documents, and admission procedure for S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Fee Structure',
    slug: 'fee-structure',
    description: 'Detailed fee structure for various trades and courses offered at the institute.',
    content: `
      <h2>Fee Structure</h2>
      <p>S.N. Pvt. Industrial Training Institute maintains an affordable fee structure to ensure quality technical education is accessible to all sections of society.</p>

      <h3>Electrician Trade (2 Years):</h3>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Particulars</th>
            <th>Amount (Rs.)</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Admission Fee</td>
            <td>As per DTE guidelines</td>
          </tr>
          <tr>
            <td>Tuition Fee (Annual)</td>
            <td>As per DTE guidelines</td>
          </tr>
          <tr>
            <td>Examination Fee</td>
            <td>As per NCVT guidelines</td>
          </tr>
          <tr>
            <td>Other Charges</td>
            <td>As applicable</td>
          </tr>
        </tbody>
      </table>

      <h3>Fee Concessions:</h3>
      <ul>
        <li>SC/ST students: As per government guidelines</li>
        <li>OBC students: As per government guidelines</li>
        <li>Economically weaker sections: Special consideration</li>
        <li>Merit-based scholarships: Available for deserving students</li>
      </ul>

      <p><strong>Note:</strong> Fee structure is subject to change as per government guidelines and DTE notifications.</p>

      <p>For current fee details, please contact the institute office:</p>
      <p><strong>Contact:</strong> 9414947801, 9571075067<br>
      <strong>Email:</strong> <EMAIL></p>
    `,
    metaTitle: 'Fee Structure - S.N. Pvt. ITI',
    metaDesc: 'Detailed fee structure for trades offered at S.N. Pvt. ITI including concessions and scholarships for various categories.',
    status: 'PUBLISHED'
  }
]

async function createPages() {
  try {
    console.log('🚀 Starting page creation...')
    
    for (const pageData of pagesData) {
      console.log(`Creating page: ${pageData.title}`)
      
      // Check if page already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: pageData.slug }
      })
      
      if (existingPage) {
        console.log(`⚠️  Page ${pageData.slug} already exists, skipping...`)
        continue
      }
      
      // Create the page
      await prisma.page.create({
        data: {
          title: pageData.title,
          slug: pageData.slug,
          description: pageData.description,
          content: pageData.content,
          metaTitle: pageData.metaTitle,
          metaDesc: pageData.metaDesc,
          status: pageData.status,
          order: 0,
          createdById: 'cmc1p577i0000fcy4iafh42k2' // <EMAIL>
        }
      })
      
      console.log(`✅ Created: ${pageData.title}`)
    }
    
    console.log('🎉 Page creation completed successfully!')
    
  } catch (error) {
    console.error('❌ Error creating pages:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createPages()
