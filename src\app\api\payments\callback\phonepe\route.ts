import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import { getPaymentGatewayManager } from '@/lib/payment-gateways'
import { createReceiptGenerator } from '@/lib/receipt-generator'
import { createEmailNotificationService } from '@/lib/email-notifications'

// POST /api/payments/callback/phonepe - PhonePe payment callback
export const POST = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      const checksum = context.request.headers.get('X-VERIFY')

      if (!checksum) {
        return NextResponse.json(
          { error: 'Checksum header is required' },
          { status: 400 }
        )
      }

      // Process callback with PhonePe gateway
      const paymentGatewayManager = getPaymentGatewayManager()
      const callbackResult = await paymentGatewayManager.processCallback(
        'PHONEPE',
        body,
        { checksum }
      )

      const { transactionId } = callbackResult

      // Get payment transaction
      const transaction = await prisma.paymentTransaction.findUnique({
        where: { id: transactionId }
      })

      if (!transaction) {
        return NextResponse.json(
          { error: 'Transaction not found' },
          { status: 404 }
        )
      }

      // Update transaction status
      await prisma.paymentTransaction.update({
        where: { id: transactionId },
        data: {
          status: callbackResult.status as any,
          gatewayTransactionId: callbackResult.gatewayTransactionId,
          gatewayResponse: callbackResult.rawResponse,
          paidAt: callbackResult.isSuccess ? new Date() : null,
          updatedAt: new Date()
        }
      })

      // Get student details
      const student = await prisma.emailAccount.findFirst({
        where: { studentId: transaction.studentId }
      })

      if (!student) {
        console.error('Student not found for transaction:', transactionId)
        return NextResponse.json({ success: true, status: callbackResult.status })
      }

      if (callbackResult.isSuccess) {
        // Generate receipt
        const receiptGenerator = createReceiptGenerator()
        const receiptData = {
          transactionId,
          studentName: student.displayName || student.studentId,
          studentId: student.studentId,
          studentEmail: student.email,
          amount: transaction.baseAmount,
          gatewayFee: transaction.gatewayFee,
          totalAmount: transaction.totalAmount,
          paymentDate: new Date(),
          paymentMethod: 'PhonePe',
          gateway: 'PhonePe',
          gatewayTransactionId: callbackResult.gatewayTransactionId,
          description: transaction.description,
          instituteName: process.env.INSTITUTE_NAME || 'Institute',
          instituteAddress: process.env.INSTITUTE_ADDRESS || 'Institute Address',
          instituteEmail: process.env.INSTITUTE_EMAIL || '<EMAIL>',
          institutePhone: process.env.INSTITUTE_PHONE || '+91-XXXXXXXXXX'
        }

        // Generate PDF receipt
        const receiptPDF = receiptGenerator.generatePDFReceipt(receiptData)
        
        // Save receipt (in a real implementation, you'd save to cloud storage)
        const receiptUrl = `/receipts/${transactionId}.pdf`
        
        // Update transaction with receipt URL
        await prisma.paymentTransaction.update({
          where: { id: transactionId },
          data: { receiptUrl }
        })

        // Send payment confirmation email
        const emailService = createEmailNotificationService()
        await emailService.sendPaymentConfirmation({
          studentName: receiptData.studentName,
          studentEmail: receiptData.studentEmail,
          transactionId,
          amount: transaction.totalAmount,
          description: transaction.description,
          paymentDate: new Date(),
          receiptUrl
        })

        console.log(`PhonePe payment successful for transaction: ${transactionId}`)
      } else {
        // Send payment failure notification
        const emailService = createEmailNotificationService()
        await emailService.sendPaymentFailureNotification({
          studentName: student.displayName || student.studentId,
          studentEmail: student.email,
          transactionId,
          amount: transaction.totalAmount,
          description: transaction.description,
          paymentDate: new Date()
        })

        console.log(`PhonePe payment failed for transaction: ${transactionId}`)
      }

      return NextResponse.json({
        success: true,
        status: callbackResult.status,
        transactionId
      })

    } catch (error) {
      console.error('PhonePe callback processing error:', error)
      return NextResponse.json(
        { error: 'Callback processing failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // Payment gateway callbacks don't have auth
    logAudit: true,
    sanitizeInput: false // Don't sanitize payment gateway data
  }
)
