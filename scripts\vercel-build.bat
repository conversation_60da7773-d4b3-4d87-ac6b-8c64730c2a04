@echo off
REM Vercel build script for SNPITC website
REM This script ensures Prisma Client is properly generated before building

echo 🔧 Starting Vercel build process...

REM Generate Prisma Client
echo 📦 Generating Prisma Client...
call npx prisma generate

if %errorlevel% neq 0 (
    echo ❌ Failed to generate Prisma Client
    exit /b 1
)

echo ✅ Prisma Client generated successfully

REM Build the Next.js application
echo 🏗️ Building Next.js application...
call npm run build

if %errorlevel% neq 0 (
    echo ❌ Build failed
    exit /b 1
)

echo ✅ Build completed successfully
echo 🎉 Vercel build process completed!
