-- Migration to fix email schema issues
-- This migration addresses critical database schema problems

-- 1. Rename senderId to fromAccountId in emails table
ALTER TABLE "emails" RENAME COLUMN "senderId" TO "fromAccountId";

-- 2. Update index name
DROP INDEX IF EXISTS "emails_senderId_idx";
CREATE INDEX "emails_fromAccountId_idx" ON "emails"("fromAccountId");

-- 3. Add path field to email_attachments table (make url optional)
ALTER TABLE "email_attachments" ADD COLUMN "path" TEXT;
ALTER TABLE "email_attachments" ALTER COLUMN "url" DROP NOT NULL;

-- 4. Create system folders for existing email accounts that don't have them
INSERT INTO "email_folders" (id, "accountId", name, "folderType", "isSystem", "createdAt", "updatedAt")
SELECT 
  gen_random_uuid(),
  ea.id,
  'Inbox',
  'INBOX',
  true,
  NOW(),
  NOW()
FROM "email_accounts" ea
WHERE NOT EXISTS (
  SELECT 1 FROM "email_folders" ef 
  WHERE ef."accountId" = ea.id AND ef."folderType" = 'INBOX'
);

INSERT INTO "email_folders" (id, "accountId", name, "folderType", "isSystem", "createdAt", "updatedAt")
SELECT 
  gen_random_uuid(),
  ea.id,
  'Sent',
  'SENT',
  true,
  NOW(),
  NOW()
FROM "email_accounts" ea
WHERE NOT EXISTS (
  SELECT 1 FROM "email_folders" ef 
  WHERE ef."accountId" = ea.id AND ef."folderType" = 'SENT'
);

INSERT INTO "email_folders" (id, "accountId", name, "folderType", "isSystem", "createdAt", "updatedAt")
SELECT 
  gen_random_uuid(),
  ea.id,
  'Drafts',
  'DRAFTS',
  true,
  NOW(),
  NOW()
FROM "email_accounts" ea
WHERE NOT EXISTS (
  SELECT 1 FROM "email_folders" ef 
  WHERE ef."accountId" = ea.id AND ef."folderType" = 'DRAFTS'
);

INSERT INTO "email_folders" (id, "accountId", name, "folderType", "isSystem", "createdAt", "updatedAt")
SELECT 
  gen_random_uuid(),
  ea.id,
  'Trash',
  'TRASH',
  true,
  NOW(),
  NOW()
FROM "email_accounts" ea
WHERE NOT EXISTS (
  SELECT 1 FROM "email_folders" ef 
  WHERE ef."accountId" = ea.id AND ef."folderType" = 'TRASH'
);

INSERT INTO "email_folders" (id, "accountId", name, "folderType", "isSystem", "createdAt", "updatedAt")
SELECT 
  gen_random_uuid(),
  ea.id,
  'Archive',
  'ARCHIVE',
  true,
  NOW(),
  NOW()
FROM "email_accounts" ea
WHERE NOT EXISTS (
  SELECT 1 FROM "email_folders" ef 
  WHERE ef."accountId" = ea.id AND ef."folderType" = 'ARCHIVE'
);

INSERT INTO "email_folders" (id, "accountId", name, "folderType", "isSystem", "createdAt", "updatedAt")
SELECT 
  gen_random_uuid(),
  ea.id,
  'Spam',
  'SPAM',
  true,
  NOW(),
  NOW()
FROM "email_accounts" ea
WHERE NOT EXISTS (
  SELECT 1 FROM "email_folders" ef 
  WHERE ef."accountId" = ea.id AND ef."folderType" = 'SPAM'
);

-- 5. Ensure all email recipients have proper folder assignments
-- Move emails without folder assignments to appropriate folders
UPDATE "email_recipients" 
SET "folderId" = (
  SELECT ef.id FROM "email_folders" ef 
  WHERE ef."accountId" = "email_recipients"."accountId" 
  AND ef."folderType" = 'INBOX'
  LIMIT 1
)
WHERE "folderId" IS NULL 
AND EXISTS (
  SELECT 1 FROM "emails" e 
  WHERE e.id = "email_recipients"."emailId" 
  AND e."sentAt" IS NOT NULL
);

-- 6. Add missing indexes for performance
CREATE INDEX IF NOT EXISTS "email_recipients_folderId_idx" ON "email_recipients"("folderId");
CREATE INDEX IF NOT EXISTS "email_attachments_emailId_idx" ON "email_attachments"("emailId");
CREATE INDEX IF NOT EXISTS "email_folders_accountId_folderType_idx" ON "email_folders"("accountId", "folderType");

-- 7. Add constraints to ensure data integrity
ALTER TABLE "email_folders" ADD CONSTRAINT "unique_system_folder_per_account" 
UNIQUE ("accountId", "folderType") DEFERRABLE INITIALLY DEFERRED;
