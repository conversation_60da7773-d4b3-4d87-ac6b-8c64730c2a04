import { NextRequest, NextResponse } from 'next/server'
import { createEmailClientCompatibilityService } from '@/lib/email-client-compatibility'

// GET /api/email-config/thunderbird/[email] - Thunderbird autoconfig
export async function GET(
  request: NextRequest,
  { params }: { params: { email: string } }
) {
  try {
    const email = decodeURIComponent(params.email)
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    const compatibilityService = createEmailClientCompatibilityService()
    
    // Validate that this email has client access enabled
    const hasImapAccess = await compatibilityService.validateClientAccess(email, 'IMAP')
    const hasSmtpAccess = await compatibilityService.validateClientAccess(email, 'SMTP')
    
    if (!hasImapAccess || !hasSmtpAccess) {
      return NextResponse.json(
        { error: 'Email client access not enabled for this account' },
        { status: 403 }
      )
    }

    // Generate Thunderbird autoconfig XML
    const autoconfigXml = compatibilityService.generateThunderbirdAutoconfig(email)

    return new NextResponse(autoconfigXml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
      }
    })

  } catch (error) {
    console.error('Thunderbird autoconfig error:', error)
    return NextResponse.json(
      { error: 'Failed to generate configuration' },
      { status: 500 }
    )
  }
}
