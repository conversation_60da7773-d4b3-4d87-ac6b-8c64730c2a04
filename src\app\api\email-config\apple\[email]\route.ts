import { NextRequest, NextResponse } from 'next/server'
import { createEmailClientCompatibilityService } from '@/lib/email-client-compatibility'
import { prisma } from '@/lib/prisma'

// GET /api/email-config/apple/[email] - Apple iOS/macOS configuration profile
export async function GET(
  request: NextRequest,
  { params }: { params: { email: string } }
) {
  try {
    const email = decodeURIComponent(params.email)
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    const compatibilityService = createEmailClientCompatibilityService()
    
    // Validate that this email has client access enabled
    const hasImapAccess = await compatibilityService.validateClientAccess(email, 'IMAP')
    const hasSmtpAccess = await compatibilityService.validateClientAccess(email, 'SMTP')
    
    if (!hasImapAccess || !hasSmtpAccess) {
      return NextResponse.json(
        { error: 'Email client access not enabled for this account' },
        { status: 403 }
      )
    }

    // Get account details for display name
    const account = await prisma.emailAccount.findFirst({
      where: { email },
      select: { displayName: true, studentId: true }
    })

    const displayName = account?.displayName || account?.studentId || email.split('@')[0]

    // Generate Apple mobile configuration profile
    const mobileConfigXml = compatibilityService.generateAppleMobileConfig(email, displayName)

    return new NextResponse(mobileConfigXml, {
      status: 200,
      headers: {
        'Content-Type': 'application/x-apple-aspen-config',
        'Content-Disposition': `attachment; filename="institute-email-${email}.mobileconfig"`,
        'Cache-Control': 'private, no-cache'
      }
    })

  } catch (error) {
    console.error('Apple mobile config error:', error)
    return NextResponse.json(
      { error: 'Failed to generate configuration' },
      { status: 500 }
    )
  }
}
