const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function populateFinalPages() {
  console.log('🌱 Populating final content pages with authentic SNPITC data...')

  try {
    // Get the first admin user to assign as creator
    const adminUser = await prisma.user.findFirst({
      where: {
        role: 'ADMIN'
      }
    })

    if (!adminUser) {
      console.error('❌ No admin user found. Please create an admin user first.')
      return
    }

    console.log(`👤 Using admin user: ${adminUser.email}`)

    const pages = [
      {
        title: 'Computer Lab',
        slug: 'computer-lab',
        description: 'Information about our computer lab facilities and equipment.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Computer Lab</h2>
            <p class="mb-6">Our computer lab is equipped with modern computers and software to provide students with essential computer skills and digital literacy.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div class="bg-blue-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">Lab Specifications</h3>
                <ul class="text-blue-800 space-y-2">
                  <li>• Area: 380 Sq. Mt.</li>
                  <li>• Modern computer systems</li>
                  <li>• High-speed internet connectivity</li>
                  <li>• Latest software applications</li>
                  <li>• Multimedia capabilities</li>
                </ul>
              </div>
              <div class="bg-green-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-green-900 mb-4">Available Software</h3>
                <ul class="text-green-800 space-y-2">
                  <li>• Microsoft Office Suite</li>
                  <li>• CAD Software</li>
                  <li>• Programming Languages</li>
                  <li>• Educational Software</li>
                  <li>• Internet Browsers</li>
                </ul>
              </div>
            </div>
            
            <div class="bg-gray-50 p-6 rounded-lg">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Computer Lab Gallery</h3>
              <p class="text-gray-700 mb-4">View our modern computer lab facilities:</p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <img src="/images/computer-lab/lab1.jpg" alt="Computer Lab View 1" class="rounded-lg shadow-sm" />
                <img src="/images/computer-lab/lab2.jpg" alt="Computer Lab View 2" class="rounded-lg shadow-sm" />
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Computer Lab - S.N. ITI',
        metaDesc: 'Information about computer lab facilities and equipment at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Sports Activities',
        slug: 'sports',
        description: 'Information about sports facilities and activities available at our institute.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Sports Activities</h2>
            <p class="mb-6">We believe in the overall development of our students, which includes physical fitness and sports activities.</p>
            
            <div class="overflow-x-auto mb-8">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No.</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of item available</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity(no.)</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr><td class="px-6 py-4 text-sm text-gray-500">1</td><td class="px-6 py-4 text-sm text-gray-900">Carom board</td><td class="px-6 py-4 text-sm text-gray-500">1</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">2</td><td class="px-6 py-4 text-sm text-gray-900">Chess</td><td class="px-6 py-4 text-sm text-gray-500">2</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">3</td><td class="px-6 py-4 text-sm text-gray-900">Badminton shuttles and net etc.</td><td class="px-6 py-4 text-sm text-gray-500">2 sets</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">4</td><td class="px-6 py-4 text-sm text-gray-900">Cricket bat, stumps, balls etc.</td><td class="px-6 py-4 text-sm text-gray-500">1 set</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">5</td><td class="px-6 py-4 text-sm text-gray-900">Volley ball</td><td class="px-6 py-4 text-sm text-gray-500">2</td></tr>
                  <tr><td class="px-6 py-4 text-sm text-gray-500">6</td><td class="px-6 py-4 text-sm text-gray-900">Net</td><td class="px-6 py-4 text-sm text-gray-500">1</td></tr>
                </tbody>
              </table>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-orange-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-orange-900 mb-2">Indoor Games</h3>
                <p class="text-2xl font-bold text-orange-600">3</p>
                <p class="text-sm text-orange-700">Carom, Chess, Table Tennis</p>
              </div>
              <div class="bg-blue-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Outdoor Games</h3>
                <p class="text-2xl font-bold text-blue-600">3</p>
                <p class="text-sm text-blue-700">Cricket, Badminton, Volleyball</p>
              </div>
              <div class="bg-green-50 p-6 rounded-lg text-center">
                <h3 class="text-lg font-semibold text-green-900 mb-2">Total Equipment</h3>
                <p class="text-2xl font-bold text-green-600">9</p>
                <p class="text-sm text-green-700">Sports Items Available</p>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Sports Activities - S.N. ITI',
        metaDesc: 'Information about sports facilities and activities available at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Achievements by Trainees',
        slug: 'achievements',
        description: 'Student and institutional achievements and recognitions.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Achievements by Trainees</h2>
            <p class="mb-6">Our students have consistently achieved excellence in various fields and competitions.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div class="bg-yellow-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-yellow-900 mb-4">Academic Achievements</h3>
                <ul class="text-yellow-800 space-y-2">
                  <li>• High pass percentage in NCVT examinations</li>
                  <li>• Excellence in practical assessments</li>
                  <li>• Outstanding performance in trade competitions</li>
                  <li>• Recognition in skill development programs</li>
                </ul>
              </div>
              <div class="bg-green-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-green-900 mb-4">Employment Success</h3>
                <ul class="text-green-800 space-y-2">
                  <li>• High placement rate in industries</li>
                  <li>• Successful entrepreneurship ventures</li>
                  <li>• Recognition from employer organizations</li>
                  <li>• Career advancement of alumni</li>
                </ul>
              </div>
            </div>
            
            <div class="bg-blue-50 p-6 rounded-lg">
              <h3 class="text-lg font-semibold text-blue-900 mb-4">Institutional Recognition</h3>
              <p class="text-blue-800 mb-4">Our institute has been recognized for:</p>
              <ul class="text-blue-800 space-y-2">
                <li>• Quality technical education delivery</li>
                <li>• Excellent infrastructure and facilities</li>
                <li>• Industry-institute collaboration</li>
                <li>• Student placement success</li>
                <li>• Compliance with NCVT standards</li>
              </ul>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Student Achievements - S.N. ITI',
        metaDesc: 'Student and institutional achievements and recognitions at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Progress Card',
        slug: 'progress-card',
        description: 'Student progress tracking and report cards.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Progress Card</h2>
            <p class="mb-6">Track student progress and download progress reports for academic sessions.</p>

            <div class="bg-blue-50 p-6 rounded-lg mb-8">
              <h3 class="text-lg font-semibold text-blue-900 mb-4">Available Progress Cards</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
                  <div>
                    <h4 class="font-semibold text-gray-900">Sep. 2024 to March 2025</h4>
                    <p class="text-sm text-gray-600">Latest progress card for current academic session</p>
                  </div>
                  <a href="/downloads/Progress card.pdf" target="_blank"
                     class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download PDF
                  </a>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div class="bg-green-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-green-900 mb-4">Progress Tracking</h3>
                <ul class="text-green-800 space-y-2">
                  <li>• Monthly assessment reports</li>
                  <li>• Practical skill evaluation</li>
                  <li>• Attendance monitoring</li>
                  <li>• Theory examination results</li>
                  <li>• Overall performance analysis</li>
                </ul>
              </div>
              <div class="bg-yellow-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-yellow-900 mb-4">Report Features</h3>
                <ul class="text-yellow-800 space-y-2">
                  <li>• Detailed subject-wise marks</li>
                  <li>• Skill development progress</li>
                  <li>• Instructor feedback</li>
                  <li>• Improvement recommendations</li>
                  <li>• Parent-teacher communication</li>
                </ul>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Progress Card - S.N. ITI',
        metaDesc: 'Student progress tracking and report cards at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Administrative Staff',
        slug: 'administrative-staff',
        description: 'Information about our administrative staff members.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Administrative Staff</h2>
            <p class="mb-6">Meet our dedicated administrative staff who ensure smooth operations of the institute.</p>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Staff</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Joining</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Surendra Kumar</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Superintendent</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">01-04-2017</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Amin Kaji</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Pion</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">01/10/2018</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
              <div class="bg-blue-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">Administrative Functions</h3>
                <ul class="text-blue-800 space-y-2">
                  <li>• Student admission and records</li>
                  <li>• Academic administration</li>
                  <li>• Financial management</li>
                  <li>• Facility maintenance</li>
                  <li>• Government compliance</li>
                </ul>
              </div>
              <div class="bg-green-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-green-900 mb-4">Support Services</h3>
                <ul class="text-green-800 space-y-2">
                  <li>• Student counseling</li>
                  <li>• Career guidance</li>
                  <li>• Placement assistance</li>
                  <li>• Document verification</li>
                  <li>• General administration</li>
                </ul>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Administrative Staff - S.N. ITI',
        metaDesc: 'Information about administrative staff members at S.N. Private Industrial Training Institute.'
      },
      {
        title: 'Feedback',
        slug: 'feedback',
        description: 'Send your feedback, enquiries and grievances to us.',
        content: `
          <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6">Feedback</h2>
            <p class="mb-6">We value your feedback and suggestions. Please use the form below to send us your enquiries and grievances.</p>

            <div class="bg-blue-50 p-6 rounded-lg mb-8">
              <h3 class="text-lg font-semibold text-blue-900 mb-4">Send Enquiry & Grievances Form</h3>
              <form class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                    <input type="text" id="name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                  </div>
                  <div>
                    <label for="contact" class="block text-sm font-medium text-gray-700 mb-2">Contact no.</label>
                    <input type="tel" id="contact" name="contact"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                  </div>
                </div>

                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                  <input type="email" id="email" name="email" required
                         class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                </div>

                <div>
                  <label for="comments" class="block text-sm font-medium text-gray-700 mb-2">Comments</label>
                  <textarea id="comments" name="comments" rows="6"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>

                <div>
                  <button type="submit"
                          class="w-full md:w-auto px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors">
                    Submit Feedback
                  </button>
                </div>
              </form>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div class="bg-green-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-green-900 mb-4">Contact Information</h3>
                <ul class="text-green-800 space-y-2">
                  <li>• Phone: 01564-275628</li>
                  <li>• Mobile: 9414947801</li>
                  <li>• Email: <EMAIL></li>
                  <li>• Address: D-117, Kaka Colony, Gandhi Vidhya Mandir</li>
                  <li>• Teh.-Sardar Shahar, Dist. Churu</li>
                </ul>
              </div>
              <div class="bg-yellow-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-yellow-900 mb-4">Feedback Categories</h3>
                <ul class="text-yellow-800 space-y-2">
                  <li>• Academic queries</li>
                  <li>• Admission information</li>
                  <li>• Infrastructure feedback</li>
                  <li>• Placement assistance</li>
                  <li>• General suggestions</li>
                </ul>
              </div>
            </div>
          </div>
        `,
        status: 'PUBLISHED',
        metaTitle: 'Feedback - S.N. ITI',
        metaDesc: 'Send your feedback, enquiries and grievances to S.N. Private Industrial Training Institute.'
      }
    ]

    // Create pages
    for (const pageData of pages) {
      // Check if page already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: pageData.slug }
      })

      if (existingPage) {
        console.log(`📄 Updating existing page: ${pageData.title}`)
        await prisma.page.update({
          where: { slug: pageData.slug },
          data: {
            ...pageData,
            updatedAt: new Date()
          }
        })
      } else {
        console.log(`📄 Creating new page: ${pageData.title}`)
        await prisma.page.create({
          data: {
            ...pageData,
            createdById: adminUser.id,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
    }

    console.log('🎉 Final content pages populated successfully!')
    console.log(`📊 Total pages processed: ${pages.length}`)

  } catch (error) {
    console.error('❌ Error populating final content pages:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the population function
populateFinalPages()
  .catch((error) => {
    console.error('❌ Population failed:', error)
    process.exit(1)
  })
