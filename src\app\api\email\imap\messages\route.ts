import { NextRequest, NextResponse } from 'next/server'
import { createSecureA<PERSON> } from '@/lib/secure-api'
import { getEmails, getEmailById, markEmailAsRead, starEmail, moveEmailToFolder } from '@/lib/email-retrieval'
import { prisma } from '@/lib/prisma'

// GET /api/email/imap/messages - Get messages from a folder
export const GET = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const accountId = searchParams.get('accountId')
      const folderId = searchParams.get('folderId')
      const folderType = searchParams.get('folderType')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '50')
      const search = searchParams.get('search')
      const unreadOnly = searchParams.get('unreadOnly') === 'true'
      const starredOnly = searchParams.get('starredOnly') === 'true'
      const sortBy = searchParams.get('sortBy') as 'date' | 'subject' | 'sender' || 'date'
      const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc'

      if (!accountId) {
        return NextResponse.json(
          { error: 'Account ID is required' },
          { status: 400 }
        )
      }

      // Verify account exists and user has access
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or inactive' },
          { status: 404 }
        )
      }

      // Get emails
      const result = await getEmails(accountId, {
        folderId: folderId || undefined,
        folderType: folderType || undefined,
        page,
        limit,
        search: search || undefined,
        unreadOnly,
        starredOnly,
        sortBy,
        sortOrder
      })

      // Format for IMAP-like response
      const imapMessages = result.emails.map((email, index) => ({
        uid: parseInt(email.id.slice(-8), 16), // Simplified UID generation
        seq: (page - 1) * limit + index + 1,
        messageId: email.messageId,
        subject: email.subject,
        from: {
          email: email.fromEmail,
          name: email.fromName
        },
        to: email.toEmails.map(e => ({ email: e })),
        cc: email.ccEmails.map(e => ({ email: e })),
        bcc: email.bccEmails.map(e => ({ email: e })),
        date: email.sentAt || email.createdAt,
        size: Buffer.byteLength(email.body, 'utf8'),
        flags: [
          email.isRead ? '\\Seen' : '',
          email.isStarred ? '\\Flagged' : '',
          email.isSpam ? '\\Junk' : ''
        ].filter(Boolean),
        hasAttachments: email.attachments.length > 0,
        attachmentCount: email.attachments.length,
        priority: email.priority,
        threadId: email.threadId,
        folder: email.folder,
        preview: email.bodyText ? 
          email.bodyText.substring(0, 200) + (email.bodyText.length > 200 ? '...' : '') :
          email.body.replace(/<[^>]*>/g, '').substring(0, 200) + '...'
      }))

      return NextResponse.json({
        success: true,
        messages: imapMessages,
        pagination: {
          page,
          limit,
          totalCount: result.totalCount,
          hasMore: result.hasMore,
          totalPages: Math.ceil(result.totalCount / limit)
        }
      })

    } catch (error) {
      console.error('IMAP messages error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve messages' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: false
  }
)

// PATCH /api/email/imap/messages - Update message flags
export const PATCH = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      const { accountId, emailId, action, value } = body

      if (!accountId || !emailId || !action) {
        return NextResponse.json(
          { error: 'Account ID, email ID, and action are required' },
          { status: 400 }
        )
      }

      // Verify account exists and user has access
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or inactive' },
          { status: 404 }
        )
      }

      let success = false
      let message = ''

      switch (action) {
        case 'markRead':
          success = await markEmailAsRead(accountId, emailId, value !== false)
          message = value !== false ? 'Email marked as read' : 'Email marked as unread'
          break

        case 'star':
          success = await starEmail(accountId, emailId, value !== false)
          message = value !== false ? 'Email starred' : 'Email unstarred'
          break

        case 'move':
          if (!value) {
            return NextResponse.json(
              { error: 'Target folder ID is required for move action' },
              { status: 400 }
            )
          }
          success = await moveEmailToFolder(accountId, emailId, value)
          message = 'Email moved successfully'
          break

        default:
          return NextResponse.json(
            { error: 'Invalid action' },
            { status: 400 }
          )
      }

      if (success) {
        return NextResponse.json({
          success: true,
          message
        })
      } else {
        return NextResponse.json(
          { error: 'Failed to update email' },
          { status: 500 }
        )
      }

    } catch (error) {
      console.error('IMAP message update error:', error)
      return NextResponse.json(
        { error: 'Failed to update message' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: true,
    sanitizeInput: true
  }
)
