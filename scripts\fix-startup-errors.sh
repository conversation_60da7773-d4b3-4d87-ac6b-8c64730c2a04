#!/bin/bash

echo "========================================"
echo "FIXING CRITICAL STARTUP ERRORS"
echo "========================================"
echo

echo "[1/8] Stopping any running development servers..."
pkill -f "next dev" 2>/dev/null || true
pkill -f "node.*next" 2>/dev/null || true
echo "✅ Stopped running processes"

echo
echo "[2/8] Clearing Next.js cache..."
if [ -d .next ]; then
    rm -rf .next
    echo "✅ Removed .next cache directory"
else
    echo "ℹ️  No .next cache found"
fi

echo
echo "[3/8] Clearing Prisma cache..."
if [ -d node_modules/.prisma ]; then
    rm -rf node_modules/.prisma
    echo "✅ Removed Prisma cache"
else
    echo "ℹ️  No Prisma cache found"
fi

echo
echo "[4/8] Removing old database file..."
if [ -f dev.db ]; then
    rm dev.db
    echo "✅ Removed old database file"
else
    echo "ℹ️  No old database file found"
fi

echo
echo "[5/8] Generating Prisma client..."
npx prisma generate
if [ $? -ne 0 ]; then
    echo "❌ ERROR: Prisma client generation failed!"
    echo
    echo "Trying to install dependencies first..."
    npm install
    echo
    echo "Retrying Prisma client generation..."
    npx prisma generate
    if [ $? -ne 0 ]; then
        echo "❌ ERROR: Prisma client generation still failed!"
        exit 1
    fi
fi
echo "✅ Prisma client generated successfully"

echo
echo "[6/8] Creating database schema..."
npx prisma db push --force-reset
if [ $? -ne 0 ]; then
    echo "❌ ERROR: Database schema creation failed!"
    exit 1
fi
echo "✅ Database schema created successfully"

echo
echo "[7/8] Seeding database with initial data..."
npx prisma db seed
if [ $? -ne 0 ]; then
    echo "⚠️  WARNING: Database seeding failed, but continuing..."
    echo "This is normal if seed script has issues"
else
    echo "✅ Database seeded successfully"
fi

echo
echo "[8/8] Verifying setup..."
if [ -d node_modules/.prisma/client ]; then
    echo "✅ Prisma client exists"
else
    echo "❌ ERROR: Prisma client still missing!"
    exit 1
fi

if [ -f dev.db ]; then
    echo "✅ Database file created"
else
    echo "❌ ERROR: Database file not created!"
    exit 1
fi

echo
echo "========================================"
echo "STARTUP ERRORS FIXED!"
echo "========================================"
echo
echo "✅ Prisma client generated"
echo "✅ Database schema created"
echo "✅ Cache cleared"
echo "✅ Environment verified"
echo
echo "Starting development server..."
echo
echo "Student Portal: http://localhost:3000/student/login"
echo "Admin Portal:   http://localhost:3000/admin/login"
echo "Health Check:   http://localhost:3000/api/system/health"
echo
echo "Press Ctrl+C to stop the server"
echo "========================================"
echo

npm run dev
