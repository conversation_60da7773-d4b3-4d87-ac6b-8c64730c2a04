import { NextRequest, NextResponse } from 'next/server'
import { createS<PERSON>ure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/payment-gateways - Get available payment gateways for student
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      // Get enabled payment gateways
      const gateways = await prisma.paymentGatewayConfig.findMany({
        where: {
          isEnabled: true
        },
        select: {
          id: true,
          gateway: true,
          additionalFeePercent: true,
          additionalFeeFixed: true,
          isTestMode: true
        },
        orderBy: {
          gateway: 'asc'
        }
      })

      // Transform to student-friendly format
      const studentGateways = gateways.map(gateway => ({
        id: gateway.gateway,
        name: getGatewayDisplayName(gateway.gateway),
        isEnabled: true,
        additionalFeePercent: gateway.additionalFeePercent,
        additionalFeeFixed: gateway.additionalFeeFixed,
        icon: getGatewayIcon(gateway.gateway),
        isTestMode: gateway.isTestMode
      }))

      return NextResponse.json({
        success: true,
        gateways: studentGateways
      })

    } catch (error) {
      console.error('Student payment gateways error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve payment gateways' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)

function getGatewayDisplayName(gateway: string): string {
  switch (gateway) {
    case 'PAYU': return 'PayU Money'
    case 'PHONEPE': return 'PhonePe'
    case 'CASHFREE': return 'Cashfree'
    default: return gateway
  }
}

function getGatewayIcon(gateway: string): string {
  switch (gateway) {
    case 'PAYU': return '💳'
    case 'PHONEPE': return '📱'
    case 'CASHFREE': return '💰'
    default: return '💳'
  }
}
