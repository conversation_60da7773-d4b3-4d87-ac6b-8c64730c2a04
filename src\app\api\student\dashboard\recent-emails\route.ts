import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/dashboard/recent-emails - Get recent emails for student dashboard
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const accountId = decoded.accountId

      // Get recent emails for the student
      const emailRecipients = await prisma.emailRecipient.findMany({
        where: {
          accountId,
          isDeleted: false,
          folder: {
            folderType: 'INBOX'
          }
        },
        include: {
          email: {
            include: {
              attachments: {
                select: {
                  id: true
                }
              }
            }
          }
        },
        orderBy: {
          email: {
            sentAt: 'desc'
          }
        },
        take: 10
      })

      // Transform to dashboard format
      const emails = emailRecipients.map(recipient => {
        const email = recipient.email
        
        // Generate preview text
        const preview = email.bodyText ? 
          email.bodyText.substring(0, 150) + (email.bodyText.length > 150 ? '...' : '') :
          email.body.replace(/<[^>]*>/g, '').substring(0, 150) + '...'

        return {
          id: email.id,
          subject: email.subject,
          fromEmail: email.fromEmail,
          fromName: email.fromName,
          receivedAt: email.sentAt,
          isRead: recipient.isRead,
          isStarred: email.isStarred,
          hasAttachments: email.attachments.length > 0,
          preview
        }
      })

      return NextResponse.json({
        success: true,
        emails
      })

    } catch (error) {
      console.error('Student recent emails error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve recent emails' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)
