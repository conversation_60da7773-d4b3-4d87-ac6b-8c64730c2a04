# Email Server Deployment Guide

## 🚀 Production Deployment Instructions

This guide provides step-by-step instructions for deploying the email server system to production using Vercel and Supabase (PostgreSQL).

## Prerequisites

### Required Accounts
- **Vercel Account**: For hosting the Next.js application
- **Supabase Account**: For PostgreSQL database hosting
- **Payment Gateway Accounts**: PayU, PhonePe, and/or Cashfree merchant accounts
- **Domain Name**: For custom email domain (optional but recommended)

### Required Tools
- **Node.js**: Version 18 or higher
- **Git**: For version control and deployment
- **Vercel CLI**: For deployment management
- **Database Client**: For database management (optional)

## Step 1: Database Setup (Supabase)

### 1.1 Create Supabase Project
```bash
# Visit https://supabase.com and create a new project
# Note down the following from your project settings:
# - Database URL
# - API URL
# - API Keys (anon and service_role)
```

### 1.2 Configure Database Connection
```bash
# Copy environment variables
cp .env.example .env.local

# Update DATABASE_URL in .env.local
DATABASE_URL="postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres"
```

### 1.3 Run Database Migrations
```bash
# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate deploy

# Seed initial data (optional)
npx prisma db seed
```

## Step 2: Environment Configuration

### 2.1 Core Application Settings
```bash
# .env.local or Vercel Environment Variables

# Application
NEXTAUTH_URL="https://your-domain.vercel.app"
NEXTAUTH_SECRET="your-nextauth-secret-key"
JWT_SECRET="your-jwt-secret-key"

# Database
DATABASE_URL="your-supabase-database-url"

# Institute Information
INSTITUTE_NAME="Your Institute Name"
INSTITUTE_ADDRESS="Institute Address, City, State, PIN"
INSTITUTE_EMAIL="<EMAIL>"
INSTITUTE_PHONE="+91-XXXXXXXXXX"
```

### 2.2 Email Server Configuration
```bash
# Email Client Compatibility
IMAP_HOST="imap.yourinstitute.edu"
IMAP_PORT="993"
IMAP_SECURE="true"

SMTP_HOST="smtp.yourinstitute.edu"
SMTP_PORT="587"
SMTP_SECURE="false"

POP3_HOST="pop3.yourinstitute.edu"
POP3_PORT="995"
POP3_SECURE="true"
```

### 2.3 Payment Gateway Configuration

#### PayU Money
```bash
PAYU_MERCHANT_ID="your-payu-merchant-id"
PAYU_MERCHANT_KEY="your-payu-merchant-key"
PAYU_SALT="your-payu-salt"
PAYU_TEST_MODE="false"  # Set to true for testing
```

#### PhonePe
```bash
PHONEPE_MERCHANT_ID="your-phonepe-merchant-id"
PHONEPE_API_KEY="your-phonepe-api-key"
PHONEPE_SALT_KEY="your-phonepe-salt-key"
PHONEPE_SALT_INDEX="1"
PHONEPE_TEST_MODE="false"  # Set to true for testing
```

#### Cashfree
```bash
CASHFREE_APP_ID="your-cashfree-app-id"
CASHFREE_SECRET_KEY="your-cashfree-secret-key"
CASHFREE_TEST_MODE="false"  # Set to true for testing
```

### 2.4 Security Settings
```bash
# Rate Limiting
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW_MS="900000"  # 15 minutes

# File Upload
MAX_FILE_SIZE="********"  # 10MB
ALLOWED_FILE_TYPES="pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png,gif"

# System Monitoring
ENABLE_HEALTH_CHECKS="true"
HEALTH_CHECK_INTERVAL="300000"  # 5 minutes
```

## Step 3: Vercel Deployment

### 3.1 Install Vercel CLI
```bash
npm install -g vercel
```

### 3.2 Login to Vercel
```bash
vercel login
```

### 3.3 Deploy Application
```bash
# From your project directory
vercel

# Follow the prompts:
# - Set up and deploy? Yes
# - Which scope? Your account/team
# - Link to existing project? No (for first deployment)
# - Project name? email-server (or your preferred name)
# - Directory? ./ (current directory)
# - Override settings? No (use defaults)
```

### 3.4 Configure Environment Variables in Vercel
```bash
# Option 1: Using Vercel CLI
vercel env add NEXTAUTH_SECRET
vercel env add DATABASE_URL
vercel env add JWT_SECRET
# ... add all other environment variables

# Option 2: Using Vercel Dashboard
# Visit https://vercel.com/dashboard
# Go to your project > Settings > Environment Variables
# Add all variables from your .env.local file
```

### 3.5 Redeploy with Environment Variables
```bash
vercel --prod
```

## Step 4: Domain Configuration (Optional)

### 4.1 Add Custom Domain
```bash
# Using Vercel CLI
vercel domains add yourdomain.com

# Or use Vercel Dashboard:
# Project Settings > Domains > Add Domain
```

### 4.2 Configure DNS Records
```bash
# Add these DNS records to your domain:
# Type: CNAME, Name: @, Value: cname.vercel-dns.com
# Type: CNAME, Name: www, Value: cname.vercel-dns.com

# For email subdomains (optional):
# Type: CNAME, Name: imap, Value: your-vercel-app.vercel.app
# Type: CNAME, Name: smtp, Value: your-vercel-app.vercel.app
# Type: CNAME, Name: pop3, Value: your-vercel-app.vercel.app
```

## Step 5: Initial Setup and Testing

### 5.1 Create Admin User
```bash
# Visit your deployed application
https://your-domain.vercel.app/admin/setup

# Create the first admin user
# This endpoint is only available when no admin users exist
```

### 5.2 Configure Payment Gateways
```bash
# Login to admin panel
https://your-domain.vercel.app/admin/login

# Navigate to Settings > Payment Gateways
# Enable and configure your payment gateways
# Test each gateway using the test functionality
```

### 5.3 Create Student Accounts
```bash
# In admin panel, navigate to Users > Email Accounts
# Create student email accounts
# Set up initial folder structure
# Configure storage limits and permissions
```

### 5.4 Test System Health
```bash
# Check system health endpoint
curl https://your-domain.vercel.app/api/system/health

# Should return status 200 with all services healthy
```

## Step 6: Production Optimization

### 6.1 Database Optimization
```sql
-- Run these queries in your Supabase SQL editor for better performance

-- Add indexes for frequently queried fields
CREATE INDEX IF NOT EXISTS idx_email_recipients_account_folder ON email_recipients(account_id, folder_id);
CREATE INDEX IF NOT EXISTS idx_emails_sent_at ON emails(sent_at DESC);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_student ON payment_transactions(student_id, status);

-- Optimize for email search
CREATE INDEX IF NOT EXISTS idx_emails_search ON emails USING gin(to_tsvector('english', subject || ' ' || body));
```

### 6.2 Monitoring Setup
```bash
# Set up monitoring alerts in Vercel
# Go to Project > Settings > Integrations
# Add monitoring services like:
# - Sentry (for error tracking)
# - LogRocket (for user session recording)
# - DataDog (for performance monitoring)
```

### 6.3 Backup Configuration
```bash
# Configure automated backups in Supabase
# Go to Supabase Dashboard > Settings > Database
# Enable Point-in-Time Recovery (PITR)
# Set up automated daily backups
```

## Step 7: Security Hardening

### 7.1 Enable Security Headers
```javascript
// Add to next.config.js
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ]
  },
}
```

### 7.2 Configure Rate Limiting
```bash
# Rate limiting is built-in but can be customized
# Update environment variables:
RATE_LIMIT_REQUESTS="50"  # Reduce for stricter limiting
RATE_LIMIT_WINDOW_MS="600000"  # 10 minutes
```

### 7.3 SSL Certificate
```bash
# Vercel automatically provides SSL certificates
# Ensure HTTPS redirect is enabled in Vercel settings
# Force HTTPS in your application settings
```

## Step 8: Maintenance and Updates

### 8.1 Regular Updates
```bash
# Keep dependencies updated
npm audit
npm update

# Redeploy after updates
vercel --prod
```

### 8.2 Database Maintenance
```sql
-- Run monthly in Supabase SQL editor
-- Clean up old audit logs (older than 6 months)
DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL '6 months';

-- Analyze table statistics
ANALYZE;

-- Vacuum to reclaim space
VACUUM;
```

### 8.3 Monitoring and Alerts
```bash
# Set up alerts for:
# - High error rates
# - Slow response times
# - Database connection issues
# - Payment gateway failures
# - Storage usage approaching limits
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check DATABASE_URL format
# Ensure Supabase project is active
# Verify IP allowlist settings in Supabase
```

#### Payment Gateway Issues
```bash
# Verify all gateway credentials
# Check test/production mode settings
# Ensure callback URLs are correctly configured
# Test gateway connectivity using health check API
```

#### Email Client Setup Issues
```bash
# Verify IMAP/SMTP/POP3 host settings
# Check DNS configuration for email subdomains
# Ensure SSL/TLS settings match client requirements
```

### Support Resources
- **Application Logs**: Available in Vercel Dashboard
- **Database Logs**: Available in Supabase Dashboard
- **Health Check API**: `/api/system/health`
- **Error Tracking**: Integrated with application monitoring

## 🎉 Deployment Complete

Your email server system is now deployed and ready for production use! 

### Next Steps:
1. **User Training**: Train administrators and students on system usage
2. **Data Migration**: Import existing student data if applicable
3. **Integration**: Integrate with existing institutional systems
4. **Monitoring**: Set up ongoing monitoring and maintenance schedules

### Access URLs:
- **Admin Panel**: `https://your-domain.vercel.app/admin`
- **Student Portal**: `https://your-domain.vercel.app/student`
- **Health Check**: `https://your-domain.vercel.app/api/system/health`
- **Email Config**: `https://your-domain.vercel.app/api/email-config/`

**Your enterprise-grade email and payment management system is now live and ready to serve your educational institution!** 🚀
