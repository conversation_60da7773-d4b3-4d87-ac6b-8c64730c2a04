'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import {
  Menu,
  X,
  Mail,
  MailOpen,
  Send,
  FileText,
  Trash2,
  Archive,
  Star,
  Settings,
  User,
  CreditCard,
  LogOut,
  Bell,
  Search,
  HardDrive,
  GraduationCap
} from 'lucide-react'

interface StudentLayoutProps {
  children: React.ReactNode
}

interface StudentData {
  id: string
  email: string
  displayName?: string
  studentId: string
  rollNumber?: string
  course?: string
  batch?: string
  storageUsed: number
  storageLimit: number
}

const navigation = [
  { name: 'Dashboard', href: '/student/dashboard', icon: Mail },
  { name: 'Inbox', href: '/student/email/inbox', icon: MailOpen },
  { name: 'Sent', href: '/student/email/sent', icon: Send },
  { name: 'Drafts', href: '/student/email/drafts', icon: FileText },
  { name: 'Starred', href: '/student/email/starred', icon: Star },
  { name: 'Archive', href: '/student/email/archive', icon: Archive },
  { name: 'Trash', href: '/student/email/trash', icon: Trash2 },
  { name: 'Payments', href: '/student/payments', icon: CreditCard },
  { name: 'Settings', href: '/student/settings', icon: Settings },
]

export default function StudentLayout({ children }: StudentLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [student, setStudent] = useState<StudentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    checkAuthentication()
  }, [])

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('student_token')
      if (!token) {
        router.push('/student/login')
        return
      }

      const response = await fetch('/api/auth/student', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        localStorage.removeItem('student_token')
        router.push('/student/login')
        return
      }

      const data = await response.json()
      setStudent(data.student)
      
      // Fetch unread count
      await fetchUnreadCount()
    } catch (error) {
      console.error('Authentication check failed:', error)
      router.push('/student/login')
    } finally {
      setLoading(false)
    }
  }

  const fetchUnreadCount = async () => {
    try {
      const token = localStorage.getItem('student_token')
      if (!token) return

      const response = await fetch('/api/student/email/unread-count', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setUnreadCount(data.count)
      }
    } catch (error) {
      console.error('Failed to fetch unread count:', error)
    }
  }

  const handleLogout = async () => {
    try {
      const token = localStorage.getItem('student_token')
      if (token) {
        await fetch('/api/auth/student', {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('student_token')
      router.push('/student/login')
    }
  }

  const formatStorageSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const getStoragePercentage = () => {
    if (!student || student.storageLimit === 0) return 0
    return (student.storageUsed / student.storageLimit) * 100
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-500">Loading...</div>
      </div>
    )
  }

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>
          <SidebarContent 
            navigation={navigation} 
            pathname={pathname} 
            student={student}
            unreadCount={unreadCount}
            onLogout={handleLogout}
            formatStorageSize={formatStorageSize}
            getStoragePercentage={getStoragePercentage}
          />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <SidebarContent 
            navigation={navigation} 
            pathname={pathname} 
            student={student}
            unreadCount={unreadCount}
            onLogout={handleLogout}
            formatStorageSize={formatStorageSize}
            getStoragePercentage={getStoragePercentage}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Top bar */}
        <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
          <button
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
          
          <div className="flex-1 px-4 flex justify-between items-center">
            <div className="flex-1 flex">
              <div className="w-full flex md:ml-0">
                <div className="relative w-full text-gray-400 focus-within:text-gray-600">
                  <div className="absolute inset-y-0 left-0 flex items-center pointer-events-none">
                    <Search className="h-5 w-5" />
                  </div>
                  <input
                    className="block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent sm:text-sm"
                    placeholder="Search emails..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            
            <div className="ml-4 flex items-center md:ml-6">
              {/* Notifications */}
              <button className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <Bell className="h-6 w-6" />
              </button>

              {/* User menu */}
              <div className="ml-3 relative">
                <div className="flex items-center">
                  <div className="bg-blue-600 p-2 rounded-full">
                    <GraduationCap className="h-5 w-5 text-white" />
                  </div>
                  <div className="ml-3 hidden md:block">
                    <div className="text-sm font-medium text-gray-700">
                      {student?.displayName || student?.studentId}
                    </div>
                    <div className="text-xs text-gray-500">{student?.email}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

// Sidebar content component
function SidebarContent({ 
  navigation, 
  pathname, 
  student, 
  unreadCount, 
  onLogout, 
  formatStorageSize, 
  getStoragePercentage 
}: {
  navigation: any[]
  pathname: string
  student: StudentData | null
  unreadCount: number
  onLogout: () => void
  formatStorageSize: (bytes: number) => string
  getStoragePercentage: () => number
}) {
  return (
    <div className="flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white">
      {/* Logo and title */}
      <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <div className="bg-blue-600 p-2 rounded-lg">
            <Mail className="h-6 w-6 text-white" />
          </div>
          <div className="ml-3">
            <h1 className="text-lg font-semibold text-gray-900">Email Portal</h1>
            <p className="text-xs text-gray-500">Student Access</p>
          </div>
        </div>

        {/* Student info */}
        {student && (
          <div className="mt-6 px-4">
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="flex items-center">
                <div className="bg-blue-600 p-2 rounded-full">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div className="ml-3 flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {student.displayName || student.studentId}
                  </p>
                  <p className="text-xs text-gray-500 truncate">{student.course}</p>
                </div>
              </div>
              
              {/* Storage usage */}
              <div className="mt-3">
                <div className="flex justify-between text-xs text-gray-600 mb-1">
                  <span>Storage</span>
                  <span>{formatStorageSize(student.storageUsed)} / {formatStorageSize(student.storageLimit)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      getStoragePercentage() > 80 ? 'bg-red-500' : 
                      getStoragePercentage() > 60 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.min(getStoragePercentage(), 100)}%` }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="mt-6 flex-1 px-2 space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            const showBadge = item.name === 'Inbox' && unreadCount > 0
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                  isActive
                    ? 'bg-blue-100 text-blue-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <item.icon
                  className={`mr-3 flex-shrink-0 h-5 w-5 ${
                    isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                />
                {item.name}
                {showBadge && (
                  <span className="ml-auto inline-block py-0.5 px-2 text-xs font-medium rounded-full bg-red-100 text-red-800">
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </span>
                )}
              </Link>
            )
          })}
        </nav>
      </div>

      {/* Logout button */}
      <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
        <button
          onClick={onLogout}
          className="flex-shrink-0 w-full group block"
        >
          <div className="flex items-center">
            <div>
              <LogOut className="inline-block h-5 w-5 text-gray-400 group-hover:text-gray-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                Sign out
              </p>
            </div>
          </div>
        </button>
      </div>
    </div>
  )
}
