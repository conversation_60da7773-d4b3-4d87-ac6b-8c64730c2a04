import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { checkDatabaseConnection } from '@/lib/supabase'
import { createSMTPTransporter } from '@/lib/email-server'
import { prisma } from '@/lib/prisma'

// GET /api/email/status - Get email server status and health
export const GET = createSecureApi(
  async (context) => {
    try {
      const healthChecks = {
        database: false,
        smtp: false,
        storage: false,
        queue: false
      }

      const errors: string[] = []
      const warnings: string[] = []

      // Check database connection
      try {
        healthChecks.database = await checkDatabaseConnection()
        if (!healthChecks.database) {
          errors.push('Database connection failed')
        }
      } catch (error) {
        healthChecks.database = false
        errors.push(`Database error: ${(error as Error).message}`)
      }

      // Check SMTP configuration
      try {
        if (process.env.SMTP_USER && process.env.SMTP_PASS) {
          const transporter = createSMTPTransporter()
          await transporter.verify()
          healthChecks.smtp = true
        } else {
          warnings.push('SMTP credentials not configured')
        }
      } catch (error) {
        healthChecks.smtp = false
        errors.push(`SMTP error: ${(error as Error).message}`)
      }

      // Check storage (Supabase)
      try {
        // Simple storage check by trying to list buckets
        const { supabaseAdmin } = await import('@/lib/supabase')
        const { data, error } = await supabaseAdmin.storage.listBuckets()
        
        if (error) {
          healthChecks.storage = false
          errors.push(`Storage error: ${error.message}`)
        } else {
          healthChecks.storage = true
        }
      } catch (error) {
        healthChecks.storage = false
        errors.push(`Storage error: ${(error as Error).message}`)
      }

      // Check email queue status
      try {
        const queueStats = await prisma.emailQueue.groupBy({
          by: ['status'],
          _count: {
            status: true
          }
        })

        const pendingCount = queueStats.find(s => s.status === 'PENDING')?._count.status || 0
        const failedCount = queueStats.find(s => s.status === 'FAILED')?._count.status || 0

        healthChecks.queue = true

        if (pendingCount > 100) {
          warnings.push(`High number of pending emails in queue: ${pendingCount}`)
        }

        if (failedCount > 50) {
          warnings.push(`High number of failed emails in queue: ${failedCount}`)
        }
      } catch (error) {
        healthChecks.queue = false
        errors.push(`Queue error: ${(error as Error).message}`)
      }

      // Get system statistics
      const stats = await getSystemStatistics()

      // Determine overall health
      const overallHealth = Object.values(healthChecks).every(check => check)
      const healthStatus = overallHealth ? 'healthy' : 'unhealthy'

      return NextResponse.json({
        status: healthStatus,
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        healthChecks,
        statistics: stats,
        errors,
        warnings,
        uptime: process.uptime(),
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024)
        }
      })

    } catch (error) {
      console.error('Email status check error:', error)
      return NextResponse.json(
        { 
          status: 'error',
          error: 'Failed to check email server status',
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: false
  }
)

// Helper function to get system statistics
async function getSystemStatistics() {
  try {
    const [
      totalAccounts,
      activeAccounts,
      totalEmails,
      emailsToday,
      queueStats,
      storageStats
    ] = await Promise.all([
      // Total email accounts
      prisma.emailAccount.count(),
      
      // Active email accounts
      prisma.emailAccount.count({
        where: { isActive: true }
      }),
      
      // Total emails
      prisma.email.count(),
      
      // Emails sent today
      prisma.email.count({
        where: {
          sentAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
      
      // Queue statistics
      prisma.emailQueue.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      }),
      
      // Storage statistics
      prisma.emailAccount.aggregate({
        _sum: {
          storageUsed: true,
          storageLimit: true
        },
        _avg: {
          storageUsed: true
        }
      })
    ])

    const queueStatsMap = queueStats.reduce((acc, stat) => {
      acc[stat.status] = stat._count.status
      return acc
    }, {} as Record<string, number>)

    return {
      accounts: {
        total: totalAccounts,
        active: activeAccounts,
        inactive: totalAccounts - activeAccounts
      },
      emails: {
        total: totalEmails,
        today: emailsToday,
        averagePerDay: Math.round(totalEmails / Math.max(1, Math.ceil((Date.now() - new Date('2024-01-01').getTime()) / (1000 * 60 * 60 * 24))))
      },
      queue: {
        pending: queueStatsMap.PENDING || 0,
        processing: queueStatsMap.PROCESSING || 0,
        sent: queueStatsMap.SENT || 0,
        failed: queueStatsMap.FAILED || 0,
        cancelled: queueStatsMap.CANCELLED || 0
      },
      storage: {
        totalUsed: storageStats._sum.storageUsed || 0,
        totalLimit: storageStats._sum.storageLimit || 0,
        averageUsed: storageStats._avg.storageUsed || 0,
        usagePercentage: storageStats._sum.storageLimit ? 
          ((storageStats._sum.storageUsed || 0) / storageStats._sum.storageLimit) * 100 : 0
      }
    }
  } catch (error) {
    console.error('Error getting system statistics:', error)
    return {
      accounts: { total: 0, active: 0, inactive: 0 },
      emails: { total: 0, today: 0, averagePerDay: 0 },
      queue: { pending: 0, processing: 0, sent: 0, failed: 0, cancelled: 0 },
      storage: { totalUsed: 0, totalLimit: 0, averageUsed: 0, usagePercentage: 0 }
    }
  }
}

// POST /api/email/status - Run system maintenance tasks
export const POST = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      const { action } = body

      const results: any = {
        action,
        timestamp: new Date().toISOString(),
        success: false,
        details: {}
      }

      switch (action) {
        case 'cleanup_old_sessions':
          // Clean up expired email sessions
          const expiredSessions = await prisma.emailSession.deleteMany({
            where: {
              OR: [
                { expiresAt: { lt: new Date() } },
                { isActive: false }
              ]
            }
          })
          results.details.deletedSessions = expiredSessions.count
          results.success = true
          break

        case 'cleanup_old_queue_items':
          // Clean up old completed queue items (older than 7 days)
          const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          const oldQueueItems = await prisma.emailQueue.deleteMany({
            where: {
              status: { in: ['SENT', 'FAILED', 'CANCELLED'] },
              updatedAt: { lt: sevenDaysAgo }
            }
          })
          results.details.deletedQueueItems = oldQueueItems.count
          results.success = true
          break

        case 'process_queue':
          // Process email queue
          const { processEmailQueue } = await import('@/lib/email-delivery')
          await processEmailQueue()
          results.details.message = 'Email queue processing initiated'
          results.success = true
          break

        case 'update_storage_usage':
          // Recalculate storage usage for all accounts
          const accounts = await prisma.emailAccount.findMany({
            select: { id: true }
          })

          let updatedAccounts = 0
          for (const account of accounts) {
            try {
              // Calculate actual storage usage
              const emailSizes = await prisma.emailRecipient.findMany({
                where: { accountId: account.id, isDeleted: false },
                include: {
                  email: {
                    select: {
                      body: true,
                      bodyText: true,
                      subject: true,
                      attachments: {
                        select: { size: true }
                      }
                    }
                  }
                }
              })

              const totalSize = emailSizes.reduce((total, recipient) => {
                const email = recipient.email
                const bodySize = Buffer.byteLength(email.body, 'utf8')
                const bodyTextSize = email.bodyText ? Buffer.byteLength(email.bodyText, 'utf8') : 0
                const subjectSize = Buffer.byteLength(email.subject, 'utf8')
                const attachmentSize = email.attachments.reduce((sum, att) => sum + att.size, 0)
                return total + bodySize + bodyTextSize + subjectSize + attachmentSize
              }, 0)

              await prisma.emailAccount.update({
                where: { id: account.id },
                data: { storageUsed: totalSize }
              })

              updatedAccounts++
            } catch (error) {
              console.error(`Error updating storage for account ${account.id}:`, error)
            }
          }

          results.details.updatedAccounts = updatedAccounts
          results.success = true
          break

        default:
          return NextResponse.json(
            { error: 'Invalid maintenance action' },
            { status: 400 }
          )
      }

      return NextResponse.json(results)

    } catch (error) {
      console.error('Email maintenance error:', error)
      return NextResponse.json(
        { error: 'Maintenance task failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: true,
    sanitizeInput: true
  }
)
