{"buildCommand": "npm run vercel-build", "installCommand": "npm install", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}, "src/app/api/email/**/*.ts": {"maxDuration": 30}, "src/app/api/student/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "rewrites": [{"source": "/admin/:path*", "destination": "/admin/:path*"}], "env": {"PRISMA_GENERATE_SKIP_AUTOINSTALL": "false"}, "build": {"env": {"PRISMA_GENERATE_SKIP_AUTOINSTALL": "false", "NEXT_PHASE": "phase-production-build"}}}