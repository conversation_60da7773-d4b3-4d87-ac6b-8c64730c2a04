#!/bin/bash

echo "========================================"
echo "DATABASE RESET AND SETUP"
echo "========================================"
echo

echo "[1/6] Removing existing database..."
if [ -f dev.db ]; then
    rm dev.db
    echo "✅ Removed existing database file"
else
    echo "ℹ️  No existing database file found"
fi

echo
echo "[2/6] Removing Prisma cache..."
if [ -d node_modules/.prisma ]; then
    rm -rf node_modules/.prisma
    echo "✅ Removed Prisma cache"
else
    echo "ℹ️  No Prisma cache found"
fi

echo
echo "[3/6] Generating Prisma client..."
npx prisma generate
if [ $? -ne 0 ]; then
    echo "❌ ERROR: Prisma client generation failed!"
    exit 1
fi
echo "✅ Prisma client generated successfully"

echo
echo "[4/6] Creating database schema..."
npx prisma db push --force-reset
if [ $? -ne 0 ]; then
    echo "❌ ERROR: Database schema creation failed!"
    exit 1
fi
echo "✅ Database schema created successfully"

echo
echo "[5/6] Seeding database with initial data..."
npx prisma db seed
if [ $? -ne 0 ]; then
    echo "⚠️  WARNING: Database seeding failed, but continuing..."
else
    echo "✅ Database seeded successfully"
fi

echo
echo "[6/6] Verifying database setup..."
if [ -f dev.db ]; then
    echo "✅ SQLite database file created: dev.db"
else
    echo "❌ ERROR: Database file not created!"
    exit 1
fi

echo
echo "========================================"
echo "DATABASE SETUP COMPLETE!"
echo "========================================"
echo
echo "Database file: dev.db"
echo "Provider: SQLite"
echo "Status: Ready for development"
echo
echo "You can now start the development server:"
echo "  npm run dev"
echo
