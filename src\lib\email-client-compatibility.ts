import { prisma } from './prisma'
import { createHash } from 'crypto'

interface EmailClientConfig {
  imapHost: string
  imapPort: number
  imapSecure: boolean
  smtpHost: string
  smtpPort: number
  smtpSecure: boolean
  pop3Host: string
  pop3Port: number
  pop3Secure: boolean
}

interface ClientCompatibilityInfo {
  outlook: {
    autoconfig: string
    manualConfig: EmailClientConfig
  }
  thunderbird: {
    autoconfig: string
    manualConfig: EmailClientConfig
  }
  apple: {
    mobileconfig: string
    manualConfig: EmailClientConfig
  }
  android: {
    manualConfig: EmailClientConfig
  }
}

export class EmailClientCompatibilityService {
  private config: EmailClientConfig

  constructor() {
    this.config = {
      imapHost: process.env.IMAP_HOST || 'imap.institute.edu',
      imapPort: parseInt(process.env.IMAP_PORT || '993'),
      imapSecure: process.env.IMAP_SECURE !== 'false',
      smtpHost: process.env.SMTP_HOST || 'smtp.institute.edu',
      smtpPort: parseInt(process.env.SMTP_PORT || '587'),
      smtpSecure: process.env.SMTP_SECURE === 'true',
      pop3Host: process.env.POP3_HOST || 'pop3.institute.edu',
      pop3Port: parseInt(process.env.POP3_PORT || '995'),
      pop3Secure: process.env.POP3_SECURE !== 'false'
    }
  }

  /**
   * Get client compatibility information for a student
   */
  async getClientCompatibilityInfo(studentId: string): Promise<ClientCompatibilityInfo> {
    const account = await prisma.emailAccount.findFirst({
      where: { studentId, accountType: 'STUDENT_ID' }
    })

    if (!account) {
      throw new Error('Student account not found')
    }

    const baseUrl = process.env.NEXTAUTH_URL || 'https://email.institute.edu'
    
    return {
      outlook: {
        autoconfig: `${baseUrl}/api/email-config/outlook/${account.email}`,
        manualConfig: this.config
      },
      thunderbird: {
        autoconfig: `${baseUrl}/api/email-config/thunderbird/${account.email}`,
        manualConfig: this.config
      },
      apple: {
        mobileconfig: `${baseUrl}/api/email-config/apple/${account.email}`,
        manualConfig: this.config
      },
      android: {
        manualConfig: this.config
      }
    }
  }

  /**
   * Generate Outlook autodiscover XML
   */
  generateOutlookAutoconfig(email: string): string {
    const domain = email.split('@')[1]
    
    return `<?xml version="1.0" encoding="utf-8"?>
<Autodiscover xmlns="http://schemas.microsoft.com/exchange/autodiscover/responseschema/2006">
  <Response xmlns="http://schemas.microsoft.com/exchange/autodiscover/outlook/responseschema/2006a">
    <Account>
      <AccountType>email</AccountType>
      <Action>settings</Action>
      <Protocol>
        <Type>IMAP</Type>
        <Server>${this.config.imapHost}</Server>
        <Port>${this.config.imapPort}</Port>
        <DomainRequired>off</DomainRequired>
        <LoginName>${email}</LoginName>
        <SPA>off</SPA>
        <SSL>${this.config.imapSecure ? 'on' : 'off'}</SSL>
        <AuthRequired>on</AuthRequired>
      </Protocol>
      <Protocol>
        <Type>SMTP</Type>
        <Server>${this.config.smtpHost}</Server>
        <Port>${this.config.smtpPort}</Port>
        <DomainRequired>off</DomainRequired>
        <LoginName>${email}</LoginName>
        <SPA>off</SPA>
        <Encryption>${this.config.smtpSecure ? 'SSL' : 'TLS'}</Encryption>
        <AuthRequired>on</AuthRequired>
        <UsePOPAuth>off</UsePOPAuth>
        <SMTPLast>off</SMTPLast>
      </Protocol>
    </Account>
  </Response>
</Autodiscover>`
  }

  /**
   * Generate Thunderbird autoconfig XML
   */
  generateThunderbirdAutoconfig(email: string): string {
    const domain = email.split('@')[1]
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<clientConfig version="1.1">
  <emailProvider id="${domain}">
    <domain>${domain}</domain>
    <displayName>Institute Email</displayName>
    <displayShortName>Institute</displayShortName>
    <incomingServer type="imap">
      <hostname>${this.config.imapHost}</hostname>
      <port>${this.config.imapPort}</port>
      <socketType>${this.config.imapSecure ? 'SSL' : 'STARTTLS'}</socketType>
      <authentication>password-cleartext</authentication>
      <username>${email}</username>
    </incomingServer>
    <incomingServer type="pop3">
      <hostname>${this.config.pop3Host}</hostname>
      <port>${this.config.pop3Port}</port>
      <socketType>${this.config.pop3Secure ? 'SSL' : 'STARTTLS'}</socketType>
      <authentication>password-cleartext</authentication>
      <username>${email}</username>
    </incomingServer>
    <outgoingServer type="smtp">
      <hostname>${this.config.smtpHost}</hostname>
      <port>${this.config.smtpPort}</port>
      <socketType>${this.config.smtpSecure ? 'SSL' : 'STARTTLS'}</socketType>
      <authentication>password-cleartext</authentication>
      <username>${email}</username>
    </outgoingServer>
  </emailProvider>
</clientConfig>`
  }

  /**
   * Generate Apple iOS/macOS configuration profile
   */
  generateAppleMobileConfig(email: string, displayName: string): string {
    const uuid1 = this.generateUUID()
    const uuid2 = this.generateUUID()
    const uuid3 = this.generateUUID()
    const domain = email.split('@')[1]
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>PayloadContent</key>
  <array>
    <dict>
      <key>EmailAccountDescription</key>
      <string>${displayName} - Institute Email</string>
      <key>EmailAccountName</key>
      <string>${displayName}</string>
      <key>EmailAccountType</key>
      <string>EmailTypeIMAP</string>
      <key>EmailAddress</key>
      <string>${email}</string>
      <key>IncomingMailServerAuthentication</key>
      <string>EmailAuthPassword</string>
      <key>IncomingMailServerHostName</key>
      <string>${this.config.imapHost}</string>
      <key>IncomingMailServerPortNumber</key>
      <integer>${this.config.imapPort}</integer>
      <key>IncomingMailServerUseSSL</key>
      <${this.config.imapSecure ? 'true' : 'false'}/>
      <key>IncomingMailServerUsername</key>
      <string>${email}</string>
      <key>OutgoingMailServerAuthentication</key>
      <string>EmailAuthPassword</string>
      <key>OutgoingMailServerHostName</key>
      <string>${this.config.smtpHost}</string>
      <key>OutgoingMailServerPortNumber</key>
      <integer>${this.config.smtpPort}</integer>
      <key>OutgoingMailServerUseSSL</key>
      <${this.config.smtpSecure ? 'true' : 'false'}/>
      <key>OutgoingMailServerUsername</key>
      <string>${email}</string>
      <key>PayloadDescription</key>
      <string>Configures email account for Institute</string>
      <key>PayloadDisplayName</key>
      <string>Institute Email Account</string>
      <key>PayloadIdentifier</key>
      <string>com.institute.email.${uuid2}</string>
      <key>PayloadType</key>
      <string>com.apple.mail.managed</string>
      <key>PayloadUUID</key>
      <string>${uuid2}</string>
      <key>PayloadVersion</key>
      <integer>1</integer>
      <key>PreventAppSheet</key>
      <false/>
      <key>PreventMove</key>
      <false/>
      <key>SMIMEEnabled</key>
      <false/>
    </dict>
  </array>
  <key>PayloadDescription</key>
  <string>Institute Email Configuration</string>
  <key>PayloadDisplayName</key>
  <string>Institute Email</string>
  <key>PayloadIdentifier</key>
  <string>com.institute.email.${uuid1}</string>
  <key>PayloadRemovalDisallowed</key>
  <false/>
  <key>PayloadType</key>
  <string>Configuration</string>
  <key>PayloadUUID</key>
  <string>${uuid1}</string>
  <key>PayloadVersion</key>
  <integer>1</integer>
</dict>
</plist>`
  }

  /**
   * Generate setup instructions for manual configuration
   */
  generateSetupInstructions(email: string): {
    outlook: string[]
    thunderbird: string[]
    apple: string[]
    android: string[]
    ios: string[]
  } {
    return {
      outlook: [
        'Open Microsoft Outlook',
        'Go to File > Add Account',
        'Choose "Manual setup or additional server types"',
        'Select "POP or IMAP"',
        `Enter your email address: ${email}`,
        `IMAP Server: ${this.config.imapHost}, Port: ${this.config.imapPort}, Encryption: ${this.config.imapSecure ? 'SSL/TLS' : 'STARTTLS'}`,
        `SMTP Server: ${this.config.smtpHost}, Port: ${this.config.smtpPort}, Encryption: ${this.config.smtpSecure ? 'SSL/TLS' : 'STARTTLS'}`,
        'Enter your email password',
        'Test account settings and finish setup'
      ],
      thunderbird: [
        'Open Mozilla Thunderbird',
        'Go to Account Settings > Account Actions > Add Mail Account',
        `Enter your name, email address (${email}), and password`,
        'Click "Configure manually"',
        `IMAP Server: ${this.config.imapHost}, Port: ${this.config.imapPort}, Security: ${this.config.imapSecure ? 'SSL/TLS' : 'STARTTLS'}`,
        `SMTP Server: ${this.config.smtpHost}, Port: ${this.config.smtpPort}, Security: ${this.config.smtpSecure ? 'SSL/TLS' : 'STARTTLS'}`,
        'Authentication: Normal password',
        'Click "Re-test" and then "Create Account"'
      ],
      apple: [
        'Open Settings > Mail > Accounts',
        'Tap "Add Account" > "Other" > "Add Mail Account"',
        `Enter your name, email (${email}), password, and description`,
        'Tap "Next" and select "IMAP"',
        `Incoming Mail Server: ${this.config.imapHost}`,
        `Outgoing Mail Server: ${this.config.smtpHost}`,
        'Enter your email and password for both servers',
        'Tap "Next" to verify and save'
      ],
      android: [
        'Open the Email app or Gmail app',
        'Tap "Add Account" > "Other"',
        `Enter your email address: ${email}`,
        'Select "IMAP account"',
        `IMAP Server: ${this.config.imapHost}, Port: ${this.config.imapPort}`,
        `SMTP Server: ${this.config.smtpHost}, Port: ${this.config.smtpPort}`,
        'Security type: SSL/TLS or STARTTLS',
        'Enter your password and complete setup'
      ],
      ios: [
        'Open Settings > Mail > Accounts',
        'Tap "Add Account" > "Other" > "Add Mail Account"',
        `Enter your name, email (${email}), password, and description`,
        'Tap "Next" and select "IMAP"',
        `Incoming Mail Server: ${this.config.imapHost}`,
        `Outgoing Mail Server: ${this.config.smtpHost}`,
        'Enter your email and password for both servers',
        'Tap "Next" to verify and save'
      ]
    }
  }

  /**
   * Generate a UUID for Apple configuration profiles
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * Validate email client access for a student
   */
  async validateClientAccess(email: string, protocol: 'IMAP' | 'POP3' | 'SMTP'): Promise<boolean> {
    const account = await prisma.emailAccount.findFirst({
      where: { email, isActive: true }
    })

    if (!account) {
      return false
    }

    switch (protocol) {
      case 'IMAP':
        return account.imapEnabled
      case 'POP3':
        return account.pop3Enabled
      case 'SMTP':
        return account.smtpEnabled
      default:
        return false
    }
  }
}

/**
 * Factory function to create email client compatibility service
 */
export function createEmailClientCompatibilityService(): EmailClientCompatibilityService {
  return new EmailClientCompatibilityService()
}
