import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Create Supabase client for client-side operations
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // We handle auth with NextAuth
    autoRefreshToken: false,
  },
})

// Create Supabase client for server-side operations with service role key
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
)

// Email storage bucket configuration
export const EMAIL_STORAGE_BUCKET = 'email-attachments'

// Helper function to upload email attachments
export async function uploadEmailAttachment(
  file: File | Buffer,
  fileName: string,
  emailId: string
): Promise<{ url: string; error?: string }> {
  try {
    const filePath = `${emailId}/${fileName}`
    
    const { data, error } = await supabaseAdmin.storage
      .from(EMAIL_STORAGE_BUCKET)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      })

    if (error) {
      console.error('Error uploading attachment:', error)
      return { url: '', error: error.message }
    }

    // Get public URL
    const { data: urlData } = supabaseAdmin.storage
      .from(EMAIL_STORAGE_BUCKET)
      .getPublicUrl(filePath)

    return { url: urlData.publicUrl }
  } catch (error) {
    console.error('Error uploading attachment:', error)
    return { url: '', error: 'Failed to upload attachment' }
  }
}

// Helper function to delete email attachments
export async function deleteEmailAttachment(filePath: string): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin.storage
      .from(EMAIL_STORAGE_BUCKET)
      .remove([filePath])

    if (error) {
      console.error('Error deleting attachment:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error deleting attachment:', error)
    return false
  }
}

// Helper function to get attachment download URL with expiration
export async function getAttachmentDownloadUrl(
  filePath: string,
  expiresIn: number = 3600 // 1 hour default
): Promise<{ url: string; error?: string }> {
  try {
    const { data, error } = await supabaseAdmin.storage
      .from(EMAIL_STORAGE_BUCKET)
      .createSignedUrl(filePath, expiresIn)

    if (error) {
      console.error('Error creating signed URL:', error)
      return { url: '', error: error.message }
    }

    return { url: data.signedUrl }
  } catch (error) {
    console.error('Error creating signed URL:', error)
    return { url: '', error: 'Failed to create download URL' }
  }
}

// Real-time subscription for email updates
export function subscribeToEmailUpdates(
  accountId: string,
  callback: (payload: any) => void
) {
  return supabase
    .channel(`email-updates-${accountId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'email_recipients',
        filter: `accountId=eq.${accountId}`,
      },
      callback
    )
    .subscribe()
}

// Real-time subscription for new emails
export function subscribeToNewEmails(
  accountId: string,
  callback: (payload: any) => void
) {
  return supabase
    .channel(`new-emails-${accountId}`)
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'email_recipients',
        filter: `accountId=eq.${accountId}`,
      },
      callback
    )
    .subscribe()
}

// Helper function to initialize storage buckets
export async function initializeStorageBuckets() {
  try {
    // Check if email attachments bucket exists
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets()
    
    if (listError) {
      console.error('Error listing buckets:', listError)
      return false
    }

    const emailBucketExists = buckets?.some(bucket => bucket.name === EMAIL_STORAGE_BUCKET)

    if (!emailBucketExists) {
      // Create email attachments bucket
      const { error: createError } = await supabaseAdmin.storage.createBucket(
        EMAIL_STORAGE_BUCKET,
        {
          public: false, // Private bucket for security
          allowedMimeTypes: [
            'image/*',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'text/csv',
          ],
          fileSizeLimit: 10485760, // 10MB limit per file
        }
      )

      if (createError) {
        console.error('Error creating email attachments bucket:', createError)
        return false
      }

      console.log('Email attachments bucket created successfully')
    }

    return true
  } catch (error) {
    console.error('Error initializing storage buckets:', error)
    return false
  }
}

// Database health check
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('id')
      .limit(1)

    if (error) {
      console.error('Database connection error:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Database connection error:', error)
    return false
  }
}

// Email system initialization
export async function initializeEmailSystem() {
  try {
    console.log('Initializing email system...')
    
    // Check database connection
    const dbConnected = await checkDatabaseConnection()
    if (!dbConnected) {
      throw new Error('Database connection failed')
    }

    // Initialize storage buckets
    const storageInitialized = await initializeStorageBuckets()
    if (!storageInitialized) {
      throw new Error('Storage initialization failed')
    }

    console.log('Email system initialized successfully')
    return true
  } catch (error) {
    console.error('Email system initialization failed:', error)
    return false
  }
}
