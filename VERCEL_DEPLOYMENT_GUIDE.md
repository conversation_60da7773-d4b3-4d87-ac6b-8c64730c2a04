# Vercel Deployment Guide for SNPITC Website

This guide provides step-by-step instructions for deploying the SNPITC website remake to Vercel with proper Prisma configuration.

## 🚀 Quick Deployment Steps

### 1. Prerequisites
- GitHub repository: `https://github.com/Navsaharan/vercel.git`
- Vercel account (free tier is sufficient)
- Node.js 18+ (for local testing)

### 2. Vercel Project Setup

1. **Connect Repository to Vercel:**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import from GitHub: `Navsaharan/vercel`
   - Select the repository

2. **Configure Build Settings:**
   - Framework Preset: `Next.js`
   - Build Command: `npm run vercel-build` (or leave default)
   - Output Directory: `.next` (default)
   - Install Command: `npm install` (default)

### 3. Environment Variables Setup

Add these environment variables in Vercel Dashboard → Project → Settings → Environment Variables:

```bash
# Required Variables
DATABASE_URL="file:/tmp/prod.db"
NEXTAUTH_SECRET="your-secure-random-string-here"
NEXTAUTH_URL="https://your-project-name.vercel.app"

# Site Configuration
SITE_NAME="S.N. Pvt. Industrial Training Institute"
SITE_URL="https://your-project-name.vercel.app"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-admin-password"

# Optional: Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

### 4. Deploy

1. Click "Deploy" in Vercel
2. Wait for the build to complete
3. Visit your deployed site

## 🔧 Prisma Configuration

The project is configured to automatically handle Prisma Client generation during deployment:

### Build Process
1. `postinstall` script runs `prisma generate`
2. `vercel-build` script ensures Prisma Client is generated before build
3. Next.js build process uses the generated client

### Database Initialization
Since we're using SQLite with file storage in `/tmp`, the database will be recreated on each deployment. For production, consider:

1. **Using a persistent database:**
   - PostgreSQL (Vercel Postgres)
   - PlanetScale (MySQL)
   - Railway (PostgreSQL/MySQL)

2. **Database seeding:**
   - The seed script will run automatically if configured
   - Initial admin user will be created

## 🛠️ Troubleshooting

### Common Issues

1. **Prisma Client Not Generated:**
   - Check build logs for `prisma generate` execution
   - Verify `postinstall` script in package.json
   - Ensure `vercel.json` configuration is correct

2. **Database Connection Issues:**
   - Verify `DATABASE_URL` environment variable
   - Check file permissions for SQLite
   - Consider using a cloud database for production

3. **Build Timeouts:**
   - Optimize dependencies
   - Use `maxDuration` in vercel.json for API routes
   - Consider splitting large operations

### Debug Commands

```bash
# Local testing
npm run build
npm run start

# Check Prisma Client
npx prisma generate
npx prisma db push

# Verify environment
npm run vercel-build
```

## 📝 Post-Deployment Steps

1. **Access Admin Panel:**
   - Visit: `https://your-domain.vercel.app/admin`
   - Login with configured admin credentials

2. **Configure Site Settings:**
   - Update site information
   - Upload images and media
   - Create navigation structure
   - Add content pages

3. **Test Functionality:**
   - Contact form submission
   - Image uploads
   - Page creation and editing
   - Navigation management

## 🔒 Security Considerations

1. **Change Default Passwords:**
   - Update `NEXTAUTH_SECRET`
   - Change admin password
   - Use strong, unique passwords

2. **Environment Variables:**
   - Never commit sensitive data to repository
   - Use Vercel's environment variable management
   - Rotate secrets regularly

3. **Database Security:**
   - Consider using a managed database service
   - Implement proper backup strategies
   - Monitor access logs

## 📊 Performance Optimization

1. **Image Optimization:**
   - Use Next.js Image component
   - Optimize uploaded images
   - Implement lazy loading

2. **Caching:**
   - Configure appropriate cache headers
   - Use Vercel's edge caching
   - Implement database query optimization

3. **Monitoring:**
   - Set up Vercel Analytics
   - Monitor build times
   - Track performance metrics

## 🆘 Support

If you encounter issues:
1. Check Vercel build logs
2. Review this deployment guide
3. Verify environment variables
4. Test locally first
5. Contact support if needed

---

**Last Updated:** December 2024
**Version:** 1.0.0
