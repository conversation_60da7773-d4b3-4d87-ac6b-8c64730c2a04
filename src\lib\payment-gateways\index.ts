import { PayUGateway, createPayUGateway } from './payu'
import { PhonePeGateway, createPhonePeGateway } from './phonepe'
import { CashfreeGateway, createCashfreeGateway } from './cashfree'

export type PaymentGatewayType = 'PAYU' | 'PHONEPE' | 'CASHFREE'

export interface PaymentRequest {
  transactionId: string
  amount: number
  studentEmail: string
  studentName: string
  studentPhone?: string
  description: string
  successUrl: string
  failureUrl: string
  callbackUrl: string
}

export interface PaymentResponse {
  paymentUrl: string
  transactionId: string
  gateway: PaymentGatewayType
  additionalData?: any
}

export interface PaymentCallbackResult {
  transactionId: string
  status: string
  amount: number
  gatewayTransactionId: string
  isSuccess: boolean
  rawResponse: any
  errorMessage?: string
}

export class PaymentGatewayManager {
  private gateways: Map<PaymentGatewayType, any> = new Map()

  constructor() {
    this.initializeGateways()
  }

  private initializeGateways() {
    try {
      // Initialize PayU
      if (this.isGatewayConfigured('PAYU')) {
        this.gateways.set('PAYU', createPayUGateway())
      }
    } catch (error) {
      console.warn('PayU gateway initialization failed:', error)
    }

    try {
      // Initialize PhonePe
      if (this.isGatewayConfigured('PHONEPE')) {
        this.gateways.set('PHONEPE', createPhonePeGateway())
      }
    } catch (error) {
      console.warn('PhonePe gateway initialization failed:', error)
    }

    try {
      // Initialize Cashfree
      if (this.isGatewayConfigured('CASHFREE')) {
        this.gateways.set('CASHFREE', createCashfreeGateway())
      }
    } catch (error) {
      console.warn('Cashfree gateway initialization failed:', error)
    }
  }

  private isGatewayConfigured(gateway: PaymentGatewayType): boolean {
    switch (gateway) {
      case 'PAYU':
        return !!(process.env.PAYU_MERCHANT_ID && process.env.PAYU_MERCHANT_KEY && process.env.PAYU_SALT)
      case 'PHONEPE':
        return !!(process.env.PHONEPE_MERCHANT_ID && process.env.PHONEPE_API_KEY && process.env.PHONEPE_SALT_KEY)
      case 'CASHFREE':
        return !!(process.env.CASHFREE_APP_ID && process.env.CASHFREE_SECRET_KEY)
      default:
        return false
    }
  }

  /**
   * Get available payment gateways
   */
  getAvailableGateways(): PaymentGatewayType[] {
    return Array.from(this.gateways.keys())
  }

  /**
   * Check if a gateway is available
   */
  isGatewayAvailable(gateway: PaymentGatewayType): boolean {
    return this.gateways.has(gateway)
  }

  /**
   * Create payment request for specified gateway
   */
  async createPaymentRequest(gateway: PaymentGatewayType, request: PaymentRequest): Promise<PaymentResponse> {
    const gatewayInstance = this.gateways.get(gateway)
    if (!gatewayInstance) {
      throw new Error(`Payment gateway ${gateway} is not available`)
    }

    let result: any

    switch (gateway) {
      case 'PAYU':
        result = await (gatewayInstance as PayUGateway).createPaymentRequest({
          ...request,
          successUrl: request.successUrl,
          failureUrl: request.failureUrl
        })
        break

      case 'PHONEPE':
        result = await (gatewayInstance as PhonePeGateway).createPaymentRequest({
          ...request,
          callbackUrl: request.callbackUrl,
          redirectUrl: request.successUrl
        })
        break

      case 'CASHFREE':
        result = await (gatewayInstance as CashfreeGateway).createPaymentRequest({
          ...request,
          returnUrl: request.successUrl,
          notifyUrl: request.callbackUrl
        })
        break

      default:
        throw new Error(`Unsupported payment gateway: ${gateway}`)
    }

    return {
      paymentUrl: result.paymentUrl,
      transactionId: result.transactionId,
      gateway,
      additionalData: result
    }
  }

  /**
   * Process payment callback for specified gateway
   */
  async processCallback(gateway: PaymentGatewayType, callbackData: any, additionalData?: any): Promise<PaymentCallbackResult> {
    const gatewayInstance = this.gateways.get(gateway)
    if (!gatewayInstance) {
      throw new Error(`Payment gateway ${gateway} is not available`)
    }

    let result: any

    switch (gateway) {
      case 'PAYU':
        result = await (gatewayInstance as PayUGateway).processCallback(callbackData)
        break

      case 'PHONEPE':
        result = await (gatewayInstance as PhonePeGateway).processCallback(callbackData, additionalData?.checksum)
        break

      case 'CASHFREE':
        result = await (gatewayInstance as CashfreeGateway).processCallback(callbackData)
        break

      default:
        throw new Error(`Unsupported payment gateway: ${gateway}`)
    }

    return result
  }

  /**
   * Get payment status from gateway
   */
  async getPaymentStatus(gateway: PaymentGatewayType, transactionId: string) {
    const gatewayInstance = this.gateways.get(gateway)
    if (!gatewayInstance) {
      throw new Error(`Payment gateway ${gateway} is not available`)
    }

    switch (gateway) {
      case 'PAYU':
        return await (gatewayInstance as PayUGateway).getTransactionStatus(transactionId)

      case 'PHONEPE':
        return await (gatewayInstance as PhonePeGateway).checkPaymentStatus(transactionId)

      case 'CASHFREE':
        return await (gatewayInstance as CashfreeGateway).getPaymentStatus(transactionId)

      default:
        throw new Error(`Unsupported payment gateway: ${gateway}`)
    }
  }

  /**
   * Initiate refund
   */
  async initiateRefund(gateway: PaymentGatewayType, transactionId: string, amount: number, reason?: string) {
    const gatewayInstance = this.gateways.get(gateway)
    if (!gatewayInstance) {
      throw new Error(`Payment gateway ${gateway} is not available`)
    }

    switch (gateway) {
      case 'PAYU':
        return await (gatewayInstance as PayUGateway).initiateRefund(transactionId, amount, reason || 'Refund requested')

      case 'PHONEPE':
        const refundId = `REF_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        return await (gatewayInstance as PhonePeGateway).initiateRefund(transactionId, refundId, amount, reason)

      case 'CASHFREE':
        return await (gatewayInstance as CashfreeGateway).initiateRefund(transactionId, amount, reason)

      default:
        throw new Error(`Unsupported payment gateway: ${gateway}`)
    }
  }

  /**
   * Test gateway connection
   */
  async testGatewayConnection(gateway: PaymentGatewayType): Promise<{ success: boolean; message: string }> {
    try {
      const gatewayInstance = this.gateways.get(gateway)
      if (!gatewayInstance) {
        return { success: false, message: `Gateway ${gateway} is not configured` }
      }

      // Create a test transaction (small amount)
      const testRequest: PaymentRequest = {
        transactionId: `TEST_${Date.now()}`,
        amount: 1, // ₹1 for testing
        studentEmail: '<EMAIL>',
        studentName: 'Test User',
        description: 'Test transaction',
        successUrl: `${process.env.NEXTAUTH_URL}/test/success`,
        failureUrl: `${process.env.NEXTAUTH_URL}/test/failure`,
        callbackUrl: `${process.env.NEXTAUTH_URL}/test/callback`
      }

      const result = await this.createPaymentRequest(gateway, testRequest)
      
      return {
        success: true,
        message: `${gateway} gateway connection successful. Test payment URL generated.`
      }
    } catch (error) {
      return {
        success: false,
        message: `${gateway} gateway test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
}

// Singleton instance
let paymentGatewayManager: PaymentGatewayManager | null = null

export function getPaymentGatewayManager(): PaymentGatewayManager {
  if (!paymentGatewayManager) {
    paymentGatewayManager = new PaymentGatewayManager()
  }
  return paymentGatewayManager
}

// Export gateway classes for direct use if needed
export { PayUGateway, PhonePeGateway, CashfreeGateway }
