@echo off
echo ========================================
echo DATABASE RESET AND SETUP
echo ========================================
echo.

echo [1/6] Removing existing database...
if exist dev.db (
    del dev.db
    echo ✅ Removed existing database file
) else (
    echo ℹ️  No existing database file found
)

echo.
echo [2/6] Removing Prisma cache...
if exist node_modules\.prisma (
    rmdir /s /q node_modules\.prisma
    echo ✅ Removed Prisma cache
) else (
    echo ℹ️  No Prisma cache found
)

echo.
echo [3/6] Generating Prisma client...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ ERROR: Prisma client generation failed!
    pause
    exit /b 1
)
echo ✅ Prisma client generated successfully

echo.
echo [4/6] Creating database schema...
call npx prisma db push --force-reset
if %errorlevel% neq 0 (
    echo ❌ ERROR: Database schema creation failed!
    pause
    exit /b 1
)
echo ✅ Database schema created successfully

echo.
echo [5/6] Seeding database with initial data...
call npx prisma db seed
if %errorlevel% neq 0 (
    echo ⚠️  WARNING: Database seeding failed, but continuing...
) else (
    echo ✅ Database seeded successfully
)

echo.
echo [6/6] Verifying database setup...
if exist dev.db (
    echo ✅ SQLite database file created: dev.db
) else (
    echo ❌ ERROR: Database file not created!
    pause
    exit /b 1
)

echo.
echo ========================================
echo DATABASE SETUP COMPLETE!
echo ========================================
echo.
echo Database file: dev.db
echo Provider: SQLite
echo Status: Ready for development
echo.
echo You can now start the development server:
echo   npm run dev
echo.
pause
