# Prisma Deployment Fix for Vercel

## 🐛 Problem Description

The SNPITC website remake was encountering a `PrismaClientInitializationError` during Vercel deployment. The error occurred because:

1. Vercel's dependency caching prevented Prisma's auto-generation from triggering
2. The build process failed during "Collecting page data" phase
3. Prisma Client wasn't properly generated before Next.js attempted to use it

## ✅ Solution Implemented

### 1. Updated package.json Scripts

```json
{
  "scripts": {
    "build": "prisma generate && next build",
    "build:prod": "prisma generate && NODE_ENV=production next build",
    "postinstall": "prisma generate",
    "vercel-build": "prisma generate && next build"
  }
}
```

**Changes:**
- Added `prisma generate` to build scripts
- Added `postinstall` script to ensure Prisma Client generation after npm install
- Created dedicated `vercel-build` script for Vercel deployment

### 2. Created vercel.json Configuration

```json
{
  "buildCommand": "npm run vercel-build",
  "installCommand": "npm install",
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "PRISMA_GENERATE_SKIP_AUTOINSTALL": "false"
  },
  "build": {
    "env": {
      "PRISMA_GENERATE_SKIP_AUTOINSTALL": "false"
    }
  }
}
```

**Key Features:**
- Custom build command that ensures Prisma generation
- Extended timeout for API routes (30 seconds)
- Disabled Prisma's skip autoinstall to ensure generation

### 3. Updated Prisma Schema

```prisma
datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}
```

**Changes:**
- Changed from hardcoded file path to environment variable
- Enables flexible database configuration for different environments

### 4. Enhanced Next.js Configuration

```typescript
webpack: (config: any, { isServer }: { isServer: boolean }) => {
  // Prisma configuration for serverless environments
  if (isServer) {
    config.externals.push('_http_common');
  }
  return config;
}
```

**Benefits:**
- Optimized webpack configuration for Prisma in serverless environment
- Prevents bundling issues with Prisma Client

### 5. Created Build Scripts

- `scripts/vercel-build.sh` - Unix/Linux build script
- `scripts/vercel-build.bat` - Windows build script

Both scripts ensure proper Prisma Client generation before building.

## 🚀 Deployment Process

### For Vercel:

1. **Environment Variables:**
   ```bash
   DATABASE_URL="file:/tmp/prod.db"
   NEXTAUTH_SECRET="your-secure-secret"
   NEXTAUTH_URL="https://your-domain.vercel.app"
   ```

2. **Build Process:**
   ```bash
   npm install          # Triggers postinstall → prisma generate
   npm run vercel-build # Ensures prisma generate → next build
   ```

3. **Verification:**
   - Check build logs for "Prisma Client generated successfully"
   - Verify API routes are accessible
   - Test admin panel functionality

## 🔍 Testing the Fix

### Local Testing:
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# Test build process
npm run vercel-build

# Verify Prisma Client
npx prisma generate
```

### Vercel Testing:
1. Deploy to Vercel
2. Check build logs for Prisma generation
3. Test API endpoints: `/api/admin/navigation`
4. Verify admin panel access: `/admin`

## 📋 Checklist for Successful Deployment

- [ ] `package.json` includes `postinstall` script
- [ ] `vercel.json` uses custom build command
- [ ] Environment variables are set in Vercel dashboard
- [ ] Prisma schema uses `env("DATABASE_URL")`
- [ ] Build logs show successful Prisma Client generation
- [ ] API routes respond without errors
- [ ] Admin panel is accessible

## 🛠️ Troubleshooting

### If build still fails:

1. **Check Environment Variables:**
   - Verify `DATABASE_URL` is set
   - Ensure `NEXTAUTH_SECRET` is configured

2. **Verify Build Logs:**
   - Look for "Prisma Client generated successfully"
   - Check for any Prisma-related errors

3. **Test Locally:**
   - Run `npm run vercel-build` locally
   - Ensure no TypeScript errors

4. **Clear Vercel Cache:**
   - Redeploy with fresh build
   - Check Vercel function logs

## 📝 Additional Notes

- The fix ensures Prisma Client is generated at multiple points in the build process
- SQLite database will be recreated on each deployment (consider persistent DB for production)
- All API routes have extended timeout (30 seconds) to handle database operations
- The solution is compatible with both Vercel and other deployment platforms

---

**Status:** ✅ Fixed
**Last Updated:** December 2024
**Tested On:** Vercel, Local Development
