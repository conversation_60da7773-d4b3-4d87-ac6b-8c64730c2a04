const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Last batch of remaining pages
const lastPages = [
  {
    title: 'DGET And State Govt. Orders',
    slug: 'dget-orders',
    description: 'DGET and State Government orders, circulars, and policy documents.',
    content: `<h2>DGET And State Govt. Orders</h2><p>Important orders and circulars from DGET and State Government.</p><h3>DGET Orders:</h3><ul><li>Affiliation orders and renewals</li><li>Curriculum updates and guidelines</li><li>Examination procedures</li><li>Quality assurance directives</li></ul><h3>State Government Orders:</h3><ul><li>Policy implementations</li><li>Admission guidelines</li><li>Fee structure approvals</li><li>Regulatory compliance</li></ul>`,
    metaTitle: 'DGET Orders - S.N. Pvt. ITI',
    metaDesc: 'DGET and State Government orders, circulars, and policy documents at S.N. Pvt. ITI.',
    status: 'PUBLISHED'
  },
  {
    title: 'Rating Of Institute',
    slug: 'ratting',
    description: 'Institute rating and performance evaluation by regulatory authorities.',
    content: `<h2>Rating Of Institute</h2><p>Performance evaluation and rating by regulatory authorities.</p><h3>Rating Parameters:</h3><ul><li>Infrastructure quality</li><li>Faculty qualifications</li><li>Training effectiveness</li><li>Student outcomes</li><li>Industry linkages</li></ul><h3>Performance Indicators:</h3><ul><li>Pass percentage in examinations</li><li>Placement rates</li><li>Industry feedback</li><li>Student satisfaction</li></ul>`,
    metaTitle: 'Institute Rating - S.N. Pvt. ITI',
    metaDesc: 'Institute rating and performance evaluation at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Grievance Redressal Mechanism',
    slug: 'grm',
    description: 'Grievance redressal mechanism and complaint handling procedures.',
    content: `<h2>Grievance Redressal Mechanism</h2><p>Systematic approach to address student and stakeholder grievances.</p><h3>Grievance Categories:</h3><ul><li>Academic issues</li><li>Administrative matters</li><li>Infrastructure concerns</li><li>Fee-related queries</li></ul><h3>Redressal Process:</h3><ul><li>Complaint registration</li><li>Investigation and review</li><li>Resolution and feedback</li><li>Follow-up and monitoring</li></ul>`,
    metaTitle: 'Grievance Redressal - S.N. Pvt. ITI',
    metaDesc: 'Grievance redressal mechanism and complaint handling at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Maintenance Expenditure',
    slug: 'building-maintenance',
    description: 'Building and infrastructure maintenance expenditure details.',
    content: `<h2>Maintenance Expenditure</h2><p>Regular maintenance and upkeep of infrastructure and facilities.</p><h3>Maintenance Areas:</h3><ul><li>Building and structural maintenance</li><li>Equipment servicing and repairs</li><li>Electrical and plumbing systems</li><li>Safety equipment maintenance</li></ul><h3>Expenditure Categories:</h3><ul><li>Preventive maintenance</li><li>Corrective maintenance</li><li>Emergency repairs</li><li>Upgradation and improvements</li></ul>`,
    metaTitle: 'Maintenance Expenditure - S.N. Pvt. ITI',
    metaDesc: 'Building and infrastructure maintenance expenditure at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Feedback',
    slug: 'feedback',
    description: 'Feedback system for students, parents, and stakeholders to share their experiences.',
    content: `<h2>Feedback</h2><p>Your feedback is valuable for continuous improvement of our services and facilities.</p><h3>Feedback Categories:</h3><ul><li>Training quality</li><li>Infrastructure facilities</li><li>Faculty performance</li><li>Administrative services</li><li>Overall experience</li></ul><h3>How to Provide Feedback:</h3><ul><li>Online feedback form</li><li>Direct communication with faculty</li><li>Suggestion box</li><li>Parent-teacher meetings</li></ul><p>We welcome constructive feedback to enhance the quality of education and training at our institute.</p>`,
    metaTitle: 'Feedback - S.N. Pvt. ITI',
    metaDesc: 'Feedback system for students and stakeholders at S.N. Pvt. Industrial Training Institute.',
    status: 'PUBLISHED'
  },
  {
    title: 'Site Map',
    slug: 'sitemap',
    description: 'Complete site map showing all pages and sections of the website.',
    content: `<h2>Site Map</h2><p>Complete navigation structure of the S.N. Pvt. ITI website.</p><h3>Main Sections:</h3><ul><li><strong>Home</strong> - Welcome page</li><li><strong>About Us</strong><ul><li>About Institute</li><li>Introduction of Institute</li><li>Scheme Running</li></ul></li><li><strong>Admissions</strong><ul><li>Admission Criteria</li><li>NCVT/SCVT Affiliated Trades</li><li>Application Format</li><li>Fee Structure</li></ul></li><li><strong>Facilities</strong><ul><li>Infrastructure</li><li>Computer Lab</li><li>Library</li><li>Sports</li></ul></li><li><strong>Trainee</strong><ul><li>Achievements</li><li>Records</li><li>Placements</li><li>Results</li></ul></li><li><strong>Staff</strong><ul><li>Faculty</li><li>Administrative Staff</li></ul></li><li><strong>More</strong><ul><li>Industry Linkage</li><li>Activities</li><li>RTI</li></ul></li><li><strong>Gallery</strong></li><li><strong>Contact</strong></li></ul>`,
    metaTitle: 'Site Map - S.N. Pvt. ITI',
    metaDesc: 'Complete site map and navigation structure of S.N. Pvt. Industrial Training Institute website.',
    status: 'PUBLISHED'
  }
]

async function createLastPages() {
  try {
    console.log('🚀 Starting last page creation...')
    
    for (const pageData of lastPages) {
      console.log(`Creating page: ${pageData.title}`)
      
      // Check if page already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: pageData.slug }
      })
      
      if (existingPage) {
        console.log(`⚠️  Page ${pageData.slug} already exists, skipping...`)
        continue
      }
      
      // Create the page
      await prisma.page.create({
        data: {
          title: pageData.title,
          slug: pageData.slug,
          description: pageData.description,
          content: pageData.content,
          metaTitle: pageData.metaTitle,
          metaDesc: pageData.metaDesc,
          status: pageData.status,
          order: 0,
          createdById: 'cmc1p577i0000fcy4iafh42k2' // <EMAIL>
        }
      })
      
      console.log(`✅ Created: ${pageData.title}`)
    }
    
    console.log('🎉 Last page creation completed successfully!')
    
  } catch (error) {
    console.error('❌ Error creating pages:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createLastPages()
