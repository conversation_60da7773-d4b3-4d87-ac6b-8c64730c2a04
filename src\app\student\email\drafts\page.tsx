'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import StudentLayout from '@/components/student/student-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Search, 
  Edit, 
  Trash2, 
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  FileText
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface Draft {
  id: string
  subject: string
  toEmails: string[]
  ccEmails: string[]
  bccEmails: string[]
  body: string
  priority: string
  attachments: any[]
  createdAt: string
  updatedAt: string
}

export default function DraftsPage() {
  const router = useRouter()
  const [drafts, setDrafts] = useState<Draft[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDrafts, setSelectedDrafts] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)

  const fetchDrafts = async (page = 1, search = '') => {
    try {
      setLoading(true)
      const token = localStorage.getItem('studentToken')
      
      if (!token) {
        router.push('/student/login')
        return
      }

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20'
      })

      if (search) {
        params.append('search', search)
      }

      const response = await fetch(`/api/student/email/drafts?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setDrafts(data.drafts || [])
        setTotalPages(data.pagination?.totalPages || 1)
        setTotalCount(data.pagination?.totalCount || 0)
      } else if (response.status === 401) {
        localStorage.removeItem('studentToken')
        router.push('/student/login')
      } else {
        console.error('Failed to fetch drafts')
      }
    } catch (error) {
      console.error('Error fetching drafts:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDrafts(currentPage, searchTerm)
  }, [currentPage])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchDrafts(1, searchTerm)
  }

  const handleDraftClick = (draftId: string) => {
    // Navigate to compose page with draft data
    router.push(`/student/email/compose?draft=${draftId}`)
  }

  const handleSelectDraft = (draftId: string, checked: boolean) => {
    if (checked) {
      setSelectedDrafts([...selectedDrafts, draftId])
    } else {
      setSelectedDrafts(selectedDrafts.filter(id => id !== draftId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDrafts(drafts.map(draft => draft.id))
    } else {
      setSelectedDrafts([])
    }
  }

  const handleDeleteDrafts = async (draftIds: string[]) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      for (const draftId of draftIds) {
        await fetch(`/api/student/email/drafts/${draftId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
      }

      setSelectedDrafts([])
      fetchDrafts(currentPage, searchTerm)
    } catch (error) {
      console.error('Delete drafts error:', error)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } else if (diffDays <= 7) {
      return date.toLocaleDateString('en-US', { weekday: 'short' })
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'LOW': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <StudentLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Drafts</h1>
          <p className="text-sm text-gray-600">
            {totalCount} {totalCount === 1 ? 'draft' : 'drafts'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => router.push('/student/email/compose')}
            size="sm"
          >
            <Edit className="h-4 w-4 mr-2" />
            New Draft
          </Button>
          <Button
            onClick={() => fetchDrafts(currentPage, searchTerm)}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between gap-4">
        <form onSubmit={handleSearch} className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search drafts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </form>

        {selectedDrafts.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {selectedDrafts.length} selected
            </span>
            <Button
              onClick={() => handleDeleteDrafts(selectedDrafts)}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        )}
      </div>

      {/* Drafts List */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={selectedDrafts.length === drafts.length && drafts.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <CardTitle className="text-sm font-medium">
              Select All
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">Loading drafts...</p>
            </div>
          ) : drafts.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No drafts</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm ? 'No drafts match your search.' : 'You don\'t have any saved drafts.'}
              </p>
              <Button onClick={() => router.push('/student/email/compose')}>
                <Edit className="h-4 w-4 mr-2" />
                Create New Draft
              </Button>
            </div>
          ) : (
            <div className="divide-y">
              {drafts.map((draft) => (
                <div
                  key={draft.id}
                  className="flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleDraftClick(draft.id)}
                >
                  <Checkbox
                    checked={selectedDrafts.includes(draft.id)}
                    onCheckedChange={(checked) => handleSelectDraft(draft.id, checked as boolean)}
                    onClick={(e) => e.stopPropagation()}
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm text-gray-600">
                        To: {draft.toEmails.length > 0 ? draft.toEmails.join(', ') : '(No recipients)'}
                      </span>
                      {draft.priority !== 'NORMAL' && (
                        <Badge variant="secondary" className={getPriorityColor(draft.priority)}>
                          {draft.priority}
                        </Badge>
                      )}
                      {draft.attachments.length > 0 && (
                        <Badge variant="outline">
                          📎 {draft.attachments.length}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-900 font-medium truncate mb-1">
                      {draft.subject || '(No subject)'}
                    </p>
                    <p className="text-sm text-gray-600 truncate">
                      {draft.body.replace(/<[^>]*>/g, '').substring(0, 100)}...
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">
                      {formatDate(draft.updatedAt)}
                    </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleDraftClick(draft.id)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Draft
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteDrafts([draft.id])}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
      </div>
    </StudentLayout>
  )
}
