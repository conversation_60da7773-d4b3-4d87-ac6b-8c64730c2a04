import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/email/folders - Get email folders for student
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Get all folders for the account
      const folders = await prisma.emailFolder.findMany({
        where: {
          accountId: account.id
        },
        include: {
          _count: {
            select: {
              recipients: {
                where: {
                  isDeleted: false
                }
              }
            }
          }
        },
        orderBy: [
          { isSystem: 'desc' },
          { order: 'asc' },
          { name: 'asc' }
        ]
      })

      // Get unread counts for each folder
      const foldersWithCounts = await Promise.all(
        folders.map(async (folder) => {
          const unreadCount = await prisma.emailRecipient.count({
            where: {
              folderId: folder.id,
              isRead: false,
              isDeleted: false
            }
          })

          return {
            id: folder.id,
            name: folder.name,
            folderType: folder.folderType,
            isSystem: folder.isSystem,
            order: folder.order,
            totalCount: folder._count.recipients,
            unreadCount,
            createdAt: folder.createdAt,
            updatedAt: folder.updatedAt
          }
        })
      )

      return NextResponse.json({
        success: true,
        folders: foldersWithCounts
      })

    } catch (error) {
      console.error('Student folders get error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve folders' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)

// POST /api/student/email/folders - Create custom folder
export const POST = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId

      // Get request body
      const body = await context.request.json()
      const { name } = body

      if (!name || !name.trim()) {
        return NextResponse.json(
          { error: 'Folder name is required' },
          { status: 400 }
        )
      }

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Check if folder name already exists
      const existingFolder = await prisma.emailFolder.findFirst({
        where: {
          accountId: account.id,
          name: name.trim()
        }
      })

      if (existingFolder) {
        return NextResponse.json(
          { error: 'Folder with this name already exists' },
          { status: 400 }
        )
      }

      // Get the highest order number for custom folders
      const lastFolder = await prisma.emailFolder.findFirst({
        where: {
          accountId: account.id,
          isSystem: false
        },
        orderBy: {
          order: 'desc'
        }
      })

      const nextOrder = lastFolder ? lastFolder.order + 1 : 100

      // Create the folder
      const folder = await prisma.emailFolder.create({
        data: {
          accountId: account.id,
          name: name.trim(),
          folderType: 'CUSTOM',
          isSystem: false,
          order: nextOrder
        }
      })

      return NextResponse.json({
        success: true,
        folder: {
          id: folder.id,
          name: folder.name,
          folderType: folder.folderType,
          isSystem: folder.isSystem,
          order: folder.order,
          totalCount: 0,
          unreadCount: 0,
          createdAt: folder.createdAt,
          updatedAt: folder.updatedAt
        },
        message: 'Folder created successfully'
      })

    } catch (error) {
      console.error('Student folder create error:', error)
      return NextResponse.json(
        { error: 'Failed to create folder' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)
