'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import StudentLayout from '@/components/student/student-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Search, 
  Archive, 
  Star, 
  Trash2, 
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  ArchiveRestore
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface ArchivedEmail {
  id: string
  subject: string
  fromEmail: string
  fromName: string
  body: string
  priority: string
  isRead: boolean
  isStarred: boolean
  sentAt: string
  attachments: any[]
}

export default function ArchivePage() {
  const router = useRouter()
  const [emails, setEmails] = useState<ArchivedEmail[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)

  const fetchArchivedEmails = async (page = 1, search = '') => {
    try {
      setLoading(true)
      const token = localStorage.getItem('studentToken')
      
      if (!token) {
        router.push('/student/login')
        return
      }

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        folder: 'archive'
      })

      if (search) {
        params.append('search', search)
      }

      const response = await fetch(`/api/student/email/messages?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setEmails(data.emails || [])
        setTotalPages(data.pagination?.totalPages || 1)
        setTotalCount(data.pagination?.totalCount || 0)
      } else if (response.status === 401) {
        localStorage.removeItem('studentToken')
        router.push('/student/login')
      } else {
        console.error('Failed to fetch archived emails')
      }
    } catch (error) {
      console.error('Error fetching archived emails:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchArchivedEmails(currentPage, searchTerm)
  }, [currentPage])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchArchivedEmails(1, searchTerm)
  }

  const handleEmailClick = (emailId: string) => {
    router.push(`/student/email/message/${emailId}`)
  }

  const handleSelectEmail = (emailId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmails([...selectedEmails, emailId])
    } else {
      setSelectedEmails(selectedEmails.filter(id => id !== emailId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEmails(emails.map(email => email.id))
    } else {
      setSelectedEmails([])
    }
  }

  const handleBulkAction = async (action: string) => {
    if (selectedEmails.length === 0) return

    try {
      const token = localStorage.getItem('studentToken')
      
      for (const emailId of selectedEmails) {
        await fetch(`/api/student/email/message/${emailId}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ action })
        })
      }

      setSelectedEmails([])
      fetchArchivedEmails(currentPage, searchTerm)
    } catch (error) {
      console.error('Bulk action error:', error)
    }
  }

  const handleUnarchive = async (emailId: string) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      // Get inbox folder ID first
      const foldersResponse = await fetch('/api/student/email/folders', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (foldersResponse.ok) {
        const foldersData = await foldersResponse.json()
        const inboxFolder = foldersData.folders.find((f: any) => f.folderType === 'INBOX')
        
        if (inboxFolder) {
          await fetch(`/api/student/email/message/${emailId}`, {
            method: 'PATCH',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ folderId: inboxFolder.id })
          })

          fetchArchivedEmails(currentPage, searchTerm)
        }
      }
    } catch (error) {
      console.error('Unarchive error:', error)
    }
  }

  const handleToggleStar = async (emailId: string, isStarred: boolean) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      await fetch(`/api/student/email/message/${emailId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isStarred: !isStarred })
      })

      fetchArchivedEmails(currentPage, searchTerm)
    } catch (error) {
      console.error('Toggle star error:', error)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } else if (diffDays <= 7) {
      return date.toLocaleDateString('en-US', { weekday: 'short' })
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'LOW': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <StudentLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Archive</h1>
          <p className="text-sm text-gray-600">
            {totalCount} archived {totalCount === 1 ? 'email' : 'emails'}
          </p>
        </div>
        <Button
          onClick={() => fetchArchivedEmails(currentPage, searchTerm)}
          variant="outline"
          size="sm"
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between gap-4">
        <form onSubmit={handleSearch} className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search archived emails..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </form>

        {selectedEmails.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {selectedEmails.length} selected
            </span>
            <Button
              onClick={() => {
                selectedEmails.forEach(emailId => handleUnarchive(emailId))
                setSelectedEmails([])
              }}
              variant="outline"
              size="sm"
            >
              <ArchiveRestore className="h-4 w-4 mr-2" />
              Unarchive
            </Button>
            <Button
              onClick={() => handleBulkAction('delete')}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        )}
      </div>

      {/* Email List */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={selectedEmails.length === emails.length && emails.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <CardTitle className="text-sm font-medium">
              Select All
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">Loading archived emails...</p>
            </div>
          ) : emails.length === 0 ? (
            <div className="p-8 text-center">
              <Archive className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No archived emails</h3>
              <p className="text-gray-600">
                {searchTerm ? 'No archived emails match your search.' : 'You haven\'t archived any emails yet.'}
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {emails.map((email) => (
                <div
                  key={email.id}
                  className={`flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer ${
                    !email.isRead ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => handleEmailClick(email.id)}
                >
                  <Checkbox
                    checked={selectedEmails.includes(email.id)}
                    onCheckedChange={(checked) => handleSelectEmail(email.id, checked as boolean)}
                    onClick={(e) => e.stopPropagation()}
                  />
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`p-1 h-auto ${email.isStarred ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-400 hover:text-yellow-500'}`}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleToggleStar(email.id, email.isStarred)
                    }}
                  >
                    <Star className={`h-4 w-4 ${email.isStarred ? 'fill-current' : ''}`} />
                  </Button>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`text-sm truncate ${!email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`}>
                        {email.fromName || email.fromEmail}
                      </span>
                      {email.priority !== 'NORMAL' && (
                        <Badge variant="secondary" className={getPriorityColor(email.priority)}>
                          {email.priority}
                        </Badge>
                      )}
                      {email.attachments.length > 0 && (
                        <Badge variant="outline">
                          📎 {email.attachments.length}
                        </Badge>
                      )}
                    </div>
                    <p className={`text-sm truncate mb-1 ${!email.isRead ? 'font-semibold text-gray-900' : 'text-gray-900'}`}>
                      {email.subject || '(No subject)'}
                    </p>
                    <p className="text-sm text-gray-600 truncate">
                      {email.body.replace(/<[^>]*>/g, '').substring(0, 100)}...
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">
                      {formatDate(email.sentAt)}
                    </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleUnarchive(email.id)}>
                          <ArchiveRestore className="h-4 w-4 mr-2" />
                          Move to Inbox
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleStar(email.id, email.isStarred)}>
                          <Star className="h-4 w-4 mr-2" />
                          {email.isStarred ? 'Remove Star' : 'Add Star'}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkAction('delete')}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
      </div>
    </StudentLayout>
  )
}
