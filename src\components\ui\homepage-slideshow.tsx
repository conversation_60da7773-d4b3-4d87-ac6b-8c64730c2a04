'use client'

import { useState, useEffect } from 'react'
import ImageSlideshow from './image-slideshow'

interface SlideshowImage {
  id: string
  url: string
  alt: string
  caption?: string
}

export default function HomepageSlideshow() {
  const [images, setImages] = useState<SlideshowImage[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSlideshowImages()
  }, [])

  const fetchSlideshowImages = async () => {
    try {
      const response = await fetch('/api/slideshow')
      if (response.ok) {
        const data = await response.json()
        setImages(data.images || [])
      }
    } catch (error) {
      console.error('Error fetching slideshow images:', error)
      // Fallback to default images
      setImages([
        {
          id: 'default-1',
          url: '/api/placeholder-image?width=800&height=600&text=Institute%20Building&bg=%23dbeafe&color=%231e40af',
          alt: 'Institute Building',
          caption: 'S.N. Private Industrial Training Institute'
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center animate-pulse">
        <span className="text-gray-500">Loading slideshow...</span>
      </div>
    )
  }

  return (
    <ImageSlideshow
      images={images}
      autoPlay={true}
      interval={5000}
      showControls={true}
      showIndicators={true}
      className="h-64 md:h-80 lg:h-96"
    />
  )
}
