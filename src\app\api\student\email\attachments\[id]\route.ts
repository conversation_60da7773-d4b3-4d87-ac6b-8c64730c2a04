import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'
import fs from 'fs'
import path from 'path'

// GET /api/student/email/attachments/[id] - Download attachment
export const GET = createSecureApi(
  async (context, { params }: { params: { id: string } }) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const attachmentId = params.id

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Get the attachment and verify access
      const attachment = await prisma.emailAttachment.findFirst({
        where: {
          id: attachmentId,
          email: {
            OR: [
              // Email sent by the student
              { fromAccountId: account.id },
              // Email received by the student
              {
                recipients: {
                  some: {
                    accountId: account.id,
                    isDeleted: false
                  }
                }
              }
            ],
            isDeleted: false
          }
        },
        include: {
          email: true
        }
      })

      if (!attachment) {
        return NextResponse.json(
          { error: 'Attachment not found' },
          { status: 404 }
        )
      }

      // For demo purposes, we'll return a mock file response
      // In a real implementation, you would read the file from storage
      try {
        // Check if file exists (for real implementation)
        if (attachment.path && fs.existsSync(attachment.path)) {
          const fileBuffer = fs.readFileSync(attachment.path)
          
          return new NextResponse(fileBuffer, {
            status: 200,
            headers: {
              'Content-Type': attachment.mimeType || 'application/octet-stream',
              'Content-Disposition': `attachment; filename="${attachment.originalName}"`,
              'Content-Length': attachment.size.toString()
            }
          })
        } else {
          // For demo purposes, return a mock file
          const mockContent = `Mock file content for ${attachment.originalName}\nThis is a demonstration file.`
          const buffer = Buffer.from(mockContent, 'utf-8')
          
          return new NextResponse(buffer, {
            status: 200,
            headers: {
              'Content-Type': attachment.mimeType || 'text/plain',
              'Content-Disposition': `attachment; filename="${attachment.originalName}"`,
              'Content-Length': buffer.length.toString()
            }
          })
        }
      } catch (fileError) {
        console.error('File read error:', fileError)
        
        // Return a mock file as fallback
        const mockContent = `Mock file content for ${attachment.originalName}\nThis is a demonstration file.`
        const buffer = Buffer.from(mockContent, 'utf-8')
        
        return new NextResponse(buffer, {
          status: 200,
          headers: {
            'Content-Type': 'text/plain',
            'Content-Disposition': `attachment; filename="${attachment.originalName}"`,
            'Content-Length': buffer.length.toString()
          }
        })
      }

    } catch (error) {
      console.error('Attachment download error:', error)
      return NextResponse.json(
        { error: 'Failed to download attachment' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true
  }
)
