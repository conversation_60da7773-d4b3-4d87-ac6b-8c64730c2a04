import React, { useState, createContext, useContext } from 'react'

interface AlertDialogContextType {
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}

const AlertDialogContext = createContext<AlertDialogContextType | null>(null)

interface AlertDialogProps {
  children: React.ReactNode
}

export function AlertDialog({ children }: AlertDialogProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <AlertDialogContext.Provider value={{ isOpen, setIsOpen }}>
      {children}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setIsOpen(false)} />
          <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
            {React.Children.map(children, (child) => {
              if (React.isValidElement(child) && child.type === AlertDialogContent) {
                return child
              }
              return null
            })}
          </div>
        </div>
      )}
    </AlertDialogContext.Provider>
  )
}

interface AlertDialogTriggerProps {
  children: React.ReactNode
  asChild?: boolean
}

export function AlertDialogTrigger({ children, asChild }: AlertDialogTriggerProps) {
  const context = useContext(AlertDialogContext)
  
  const handleClick = () => {
    context?.setIsOpen(true)
  }

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, { onClick: handleClick })
  }
  
  return (
    <button onClick={handleClick}>
      {children}
    </button>
  )
}

interface AlertDialogContentProps {
  children: React.ReactNode
}

export function AlertDialogContent({ children }: AlertDialogContentProps) {
  return (
    <div className="p-6">
      {children}
    </div>
  )
}

interface AlertDialogHeaderProps {
  children: React.ReactNode
}

export function AlertDialogHeader({ children }: AlertDialogHeaderProps) {
  return (
    <div className="mb-4">
      {children}
    </div>
  )
}

interface AlertDialogTitleProps {
  children: React.ReactNode
}

export function AlertDialogTitle({ children }: AlertDialogTitleProps) {
  return (
    <h3 className="text-lg font-medium text-gray-900 mb-2">
      {children}
    </h3>
  )
}

interface AlertDialogDescriptionProps {
  children: React.ReactNode
}

export function AlertDialogDescription({ children }: AlertDialogDescriptionProps) {
  return (
    <p className="text-sm text-gray-600">
      {children}
    </p>
  )
}

interface AlertDialogFooterProps {
  children: React.ReactNode
}

export function AlertDialogFooter({ children }: AlertDialogFooterProps) {
  return (
    <div className="flex justify-end space-x-3 mt-6">
      {children}
    </div>
  )
}

interface AlertDialogActionProps {
  children: React.ReactNode
  onClick?: () => void
}

export function AlertDialogAction({ children, onClick }: AlertDialogActionProps) {
  const context = useContext(AlertDialogContext)
  
  const handleClick = () => {
    onClick?.()
    context?.setIsOpen(false)
  }

  return (
    <button
      onClick={handleClick}
      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
    >
      {children}
    </button>
  )
}

interface AlertDialogCancelProps {
  children: React.ReactNode
}

export function AlertDialogCancel({ children }: AlertDialogCancelProps) {
  const context = useContext(AlertDialogContext)
  
  return (
    <button
      onClick={() => context?.setIsOpen(false)}
      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
    >
      {children}
    </button>
  )
}
