import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'

// POST /api/student/change-password - Change student password
export const POST = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const accountId = decoded.accountId
      const body = await context.request.json()
      const { currentPassword, newPassword } = body

      // Validation
      if (!currentPassword || !newPassword) {
        return NextResponse.json(
          { error: 'Current password and new password are required' },
          { status: 400 }
        )
      }

      if (newPassword.length < 8) {
        return NextResponse.json(
          { error: 'New password must be at least 8 characters long' },
          { status: 400 }
        )
      }

      // Get current account
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId },
        select: {
          id: true,
          password: true,
          email: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found' },
          { status: 404 }
        )
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, account.password)
      if (!isCurrentPasswordValid) {
        return NextResponse.json(
          { error: 'Current password is incorrect' },
          { status: 400 }
        )
      }

      // Hash new password
      const saltRounds = 12
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds)

      // Update password
      await prisma.emailAccount.update({
        where: { id: accountId },
        data: {
          password: hashedNewPassword,
          updatedAt: new Date()
        }
      })

      // Invalidate all existing sessions for this account (security measure)
      await prisma.emailSession.updateMany({
        where: {
          accountId,
          isActive: true
        },
        data: {
          isActive: false
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Password changed successfully. Please log in again.'
      })

    } catch (error) {
      console.error('Student password change error:', error)
      return NextResponse.json(
        { error: 'Failed to change password' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)
