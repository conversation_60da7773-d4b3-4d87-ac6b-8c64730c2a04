import { prisma } from './prisma'
import { getFolderByType } from './email-server'

// Email retrieval interfaces
export interface EmailListOptions {
  folderId?: string
  folderType?: string
  page?: number
  limit?: number
  search?: string
  unreadOnly?: boolean
  starredOnly?: boolean
  sortBy?: 'date' | 'subject' | 'sender'
  sortOrder?: 'asc' | 'desc'
}

export interface EmailDetails {
  id: string
  messageId: string
  subject: string
  body: string
  bodyText?: string
  fromEmail: string
  fromName?: string
  toEmails: string[]
  ccEmails: string[]
  bccEmails: string[]
  priority: string
  isRead: boolean
  isStarred: boolean
  isSpam: boolean
  threadId?: string
  sentAt?: Date
  createdAt: Date
  attachments: EmailAttachmentInfo[]
  folder?: string
}

export interface EmailAttachmentInfo {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
}

export interface FolderInfo {
  id: string
  name: string
  folderType: string
  unreadCount: number
  totalCount: number
  isSystem: boolean
  order: number
}

// Get emails for an account with filtering and pagination
export async function getEmails(
  accountId: string,
  options: EmailListOptions = {}
): Promise<{ emails: EmailDetails[]; totalCount: number; hasMore: boolean }> {
  const {
    folderId,
    folderType,
    page = 1,
    limit = 50,
    search,
    unreadOnly = false,
    starredOnly = false,
    sortBy = 'date',
    sortOrder = 'desc'
  } = options

  const skip = (page - 1) * limit

  // Build where clause
  const whereClause: any = {
    accountId,
    isDeleted: false
  }

  // Filter by folder
  if (folderId) {
    whereClause.folderId = folderId
  } else if (folderType) {
    const folder = await getFolderByType(accountId, folderType)
    if (folder) {
      whereClause.folderId = folder.id
    }
  }

  // Filter by read status
  if (unreadOnly) {
    whereClause.isRead = false
  }

  // Filter by starred status
  if (starredOnly) {
    whereClause.email = {
      isStarred: true
    }
  }

  // Search functionality
  if (search) {
    whereClause.email = {
      ...whereClause.email,
      OR: [
        { subject: { contains: search, mode: 'insensitive' } },
        { body: { contains: search, mode: 'insensitive' } },
        { fromEmail: { contains: search, mode: 'insensitive' } },
        { fromName: { contains: search, mode: 'insensitive' } }
      ]
    }
  }

  // Build order clause
  const orderBy: any = {}
  switch (sortBy) {
    case 'subject':
      orderBy.email = { subject: sortOrder }
      break
    case 'sender':
      orderBy.email = { fromEmail: sortOrder }
      break
    case 'date':
    default:
      orderBy.email = { sentAt: sortOrder }
      break
  }

  // Get emails with recipients
  const emailRecipients = await prisma.emailRecipient.findMany({
    where: whereClause,
    include: {
      email: {
        include: {
          attachments: true,
          recipients: {
            where: { accountId },
            select: { recipientType: true }
          }
        }
      },
      folder: {
        select: { name: true, folderType: true }
      }
    },
    orderBy,
    skip,
    take: limit + 1 // Get one extra to check if there are more
  })

  // Get total count for pagination
  const totalCount = await prisma.emailRecipient.count({
    where: whereClause
  })

  const hasMore = emailRecipients.length > limit
  const emails = emailRecipients.slice(0, limit)

  // Transform to EmailDetails format
  const emailDetails: EmailDetails[] = emails.map(recipient => {
    const email = recipient.email
    
    // Extract recipient emails by type
    const allRecipients = email.recipients || []
    const toEmails: string[] = []
    const ccEmails: string[] = []
    const bccEmails: string[] = []

    // Note: This is simplified - in a real implementation, you'd need to store
    // the original recipient lists or reconstruct them from all recipients
    
    return {
      id: email.id,
      messageId: email.messageId,
      subject: email.subject,
      body: email.body,
      bodyText: email.bodyText || undefined,
      fromEmail: email.fromEmail,
      fromName: email.fromName || undefined,
      toEmails,
      ccEmails,
      bccEmails,
      priority: email.priority,
      isRead: recipient.isRead,
      isStarred: email.isStarred,
      isSpam: email.isSpam,
      threadId: email.threadId || undefined,
      sentAt: email.sentAt || undefined,
      createdAt: email.createdAt,
      attachments: email.attachments.map(att => ({
        id: att.id,
        filename: att.filename,
        originalName: att.originalName,
        mimeType: att.mimeType,
        size: att.size,
        url: att.url
      })),
      folder: recipient.folder?.name
    }
  })

  return {
    emails: emailDetails,
    totalCount,
    hasMore
  }
}

// Get single email by ID
export async function getEmailById(
  accountId: string,
  emailId: string
): Promise<EmailDetails | null> {
  const emailRecipient = await prisma.emailRecipient.findFirst({
    where: {
      accountId,
      emailId,
      isDeleted: false
    },
    include: {
      email: {
        include: {
          attachments: true,
          recipients: true
        }
      },
      folder: {
        select: { name: true, folderType: true }
      }
    }
  })

  if (!emailRecipient) return null

  const email = emailRecipient.email

  // Extract recipient emails by type
  const toEmails = email.recipients
    .filter(r => r.recipientType === 'TO')
    .map(r => r.accountId) // This would need to be resolved to email addresses

  const ccEmails = email.recipients
    .filter(r => r.recipientType === 'CC')
    .map(r => r.accountId) // This would need to be resolved to email addresses

  const bccEmails = email.recipients
    .filter(r => r.recipientType === 'BCC')
    .map(r => r.accountId) // This would need to be resolved to email addresses

  return {
    id: email.id,
    messageId: email.messageId,
    subject: email.subject,
    body: email.body,
    bodyText: email.bodyText || undefined,
    fromEmail: email.fromEmail,
    fromName: email.fromName || undefined,
    toEmails,
    ccEmails,
    bccEmails,
    priority: email.priority,
    isRead: emailRecipient.isRead,
    isStarred: email.isStarred,
    isSpam: email.isSpam,
    threadId: email.threadId || undefined,
    sentAt: email.sentAt || undefined,
    createdAt: email.createdAt,
    attachments: email.attachments.map(att => ({
      id: att.id,
      filename: att.filename,
      originalName: att.originalName,
      mimeType: att.mimeType,
      size: att.size,
      url: att.url
    })),
    folder: emailRecipient.folder?.name
  }
}

// Mark email as read/unread
export async function markEmailAsRead(
  accountId: string,
  emailId: string,
  isRead: boolean = true
): Promise<boolean> {
  try {
    await prisma.emailRecipient.updateMany({
      where: {
        accountId,
        emailId,
        isDeleted: false
      },
      data: {
        isRead,
        readAt: isRead ? new Date() : null
      }
    })

    return true
  } catch (error) {
    console.error('Error marking email as read:', error)
    return false
  }
}

// Star/unstar email
export async function starEmail(
  accountId: string,
  emailId: string,
  isStarred: boolean = true
): Promise<boolean> {
  try {
    // Check if user has access to this email
    const emailRecipient = await prisma.emailRecipient.findFirst({
      where: {
        accountId,
        emailId,
        isDeleted: false
      }
    })

    if (!emailRecipient) return false

    // Update the email's starred status
    await prisma.email.update({
      where: { id: emailId },
      data: { isStarred }
    })

    return true
  } catch (error) {
    console.error('Error starring email:', error)
    return false
  }
}

// Move email to folder
export async function moveEmailToFolder(
  accountId: string,
  emailId: string,
  targetFolderId: string
): Promise<boolean> {
  try {
    // Verify the target folder belongs to the account
    const targetFolder = await prisma.emailFolder.findFirst({
      where: {
        id: targetFolderId,
        accountId
      }
    })

    if (!targetFolder) return false

    // Update the email recipient's folder
    await prisma.emailRecipient.updateMany({
      where: {
        accountId,
        emailId,
        isDeleted: false
      },
      data: {
        folderId: targetFolderId
      }
    })

    return true
  } catch (error) {
    console.error('Error moving email to folder:', error)
    return false
  }
}

// Delete email (move to trash or permanent delete)
export async function deleteEmail(
  accountId: string,
  emailId: string,
  permanent: boolean = false
): Promise<boolean> {
  try {
    if (permanent) {
      // Permanent delete - remove from email_recipients
      await prisma.emailRecipient.deleteMany({
        where: {
          accountId,
          emailId
        }
      })
    } else {
      // Move to trash
      const trashFolder = await getFolderByType(accountId, 'TRASH')
      if (trashFolder) {
        await prisma.emailRecipient.updateMany({
          where: {
            accountId,
            emailId,
            isDeleted: false
          },
          data: {
            folderId: trashFolder.id,
            isDeleted: true,
            deletedAt: new Date()
          }
        })
      }
    }

    return true
  } catch (error) {
    console.error('Error deleting email:', error)
    return false
  }
}

// Get folders for an account
export async function getFolders(accountId: string): Promise<FolderInfo[]> {
  const folders = await prisma.emailFolder.findMany({
    where: { accountId },
    orderBy: [
      { isSystem: 'desc' },
      { order: 'asc' },
      { name: 'asc' }
    ]
  })

  // Get counts for each folder
  const folderInfos: FolderInfo[] = []

  for (const folder of folders) {
    const totalCount = await prisma.emailRecipient.count({
      where: {
        accountId,
        folderId: folder.id,
        isDeleted: false
      }
    })

    const unreadCount = await prisma.emailRecipient.count({
      where: {
        accountId,
        folderId: folder.id,
        isDeleted: false,
        isRead: false
      }
    })

    folderInfos.push({
      id: folder.id,
      name: folder.name,
      folderType: folder.folderType,
      unreadCount,
      totalCount,
      isSystem: folder.isSystem,
      order: folder.order
    })
  }

  return folderInfos
}

// Create custom folder
export async function createFolder(
  accountId: string,
  name: string,
  parentId?: string
): Promise<{ success: boolean; folderId?: string; error?: string }> {
  try {
    // Check if folder name already exists for this account
    const existingFolder = await prisma.emailFolder.findFirst({
      where: {
        accountId,
        name,
        parentId: parentId || null
      }
    })

    if (existingFolder) {
      return { success: false, error: 'Folder name already exists' }
    }

    // Get the highest order number for custom folders
    const lastFolder = await prisma.emailFolder.findFirst({
      where: {
        accountId,
        isSystem: false,
        parentId: parentId || null
      },
      orderBy: { order: 'desc' }
    })

    const order = (lastFolder?.order || 100) + 1

    const folder = await prisma.emailFolder.create({
      data: {
        accountId,
        name,
        folderType: 'CUSTOM',
        parentId,
        order,
        isSystem: false
      }
    })

    return { success: true, folderId: folder.id }
  } catch (error) {
    console.error('Error creating folder:', error)
    return { success: false, error: 'Failed to create folder' }
  }
}

// Get email thread
export async function getEmailThread(
  accountId: string,
  threadId: string
): Promise<EmailDetails[]> {
  const emailRecipients = await prisma.emailRecipient.findMany({
    where: {
      accountId,
      isDeleted: false,
      email: {
        threadId
      }
    },
    include: {
      email: {
        include: {
          attachments: true,
          recipients: true
        }
      },
      folder: {
        select: { name: true, folderType: true }
      }
    },
    orderBy: {
      email: {
        sentAt: 'asc'
      }
    }
  })

  return emailRecipients.map(recipient => {
    const email = recipient.email
    
    return {
      id: email.id,
      messageId: email.messageId,
      subject: email.subject,
      body: email.body,
      bodyText: email.bodyText || undefined,
      fromEmail: email.fromEmail,
      fromName: email.fromName || undefined,
      toEmails: [], // Simplified for now
      ccEmails: [],
      bccEmails: [],
      priority: email.priority,
      isRead: recipient.isRead,
      isStarred: email.isStarred,
      isSpam: email.isSpam,
      threadId: email.threadId || undefined,
      sentAt: email.sentAt || undefined,
      createdAt: email.createdAt,
      attachments: email.attachments.map(att => ({
        id: att.id,
        filename: att.filename,
        originalName: att.originalName,
        mimeType: att.mimeType,
        size: att.size,
        url: att.url
      })),
      folder: recipient.folder?.name
    }
  })
}
