import crypto from 'crypto'

interface PayUConfig {
  merchantId: string
  merchantKey: string
  salt: string
  isTestMode: boolean
}

interface PayUPaymentRequest {
  transactionId: string
  amount: number
  studentEmail: string
  studentName: string
  studentPhone?: string
  description: string
  successUrl: string
  failureUrl: string
}

interface PayUPaymentResponse {
  paymentUrl: string
  transactionId: string
  hash: string
}

export class PayUGateway {
  private config: PayUConfig

  constructor(config: PayUConfig) {
    this.config = config
  }

  /**
   * Generate PayU payment URL with proper hash
   */
  async createPaymentRequest(request: PayUPaymentRequest): Promise<PayUPaymentResponse> {
    const {
      transactionId,
      amount,
      studentEmail,
      studentName,
      studentPhone,
      description,
      successUrl,
      failureUrl
    } = request

    // PayU requires amount in paisa (multiply by 100)
    const amountInPaisa = Math.round(amount * 100)

    // Generate hash as per PayU documentation
    const hashString = `${this.config.merchantKey}|${transactionId}|${amountInPaisa}|${description}|${studentName}|${studentEmail}|||||||||||${this.config.salt}`
    const hash = crypto.createHash('sha512').update(hashString).digest('hex')

    // PayU payment parameters
    const paymentParams = {
      key: this.config.merchantId,
      txnid: transactionId,
      amount: amountInPaisa.toString(),
      productinfo: description,
      firstname: studentName,
      email: studentEmail,
      phone: studentPhone || '',
      surl: successUrl,
      furl: failureUrl,
      hash: hash,
      service_provider: 'payu_paisa'
    }

    // Generate payment URL
    const baseUrl = this.config.isTestMode 
      ? 'https://test.payu.in/_payment'
      : 'https://secure.payu.in/_payment'

    const paymentUrl = `${baseUrl}?${new URLSearchParams(paymentParams).toString()}`

    return {
      paymentUrl,
      transactionId,
      hash
    }
  }

  /**
   * Verify PayU payment response
   */
  verifyPaymentResponse(responseData: Record<string, string>): boolean {
    const {
      key,
      txnid,
      amount,
      productinfo,
      firstname,
      email,
      status,
      hash
    } = responseData

    // Generate verification hash
    const hashString = `${this.config.salt}|${status}|||||||||||${email}|${firstname}|${productinfo}|${amount}|${txnid}|${key}`
    const expectedHash = crypto.createHash('sha512').update(hashString).digest('hex')

    return hash === expectedHash
  }

  /**
   * Process PayU webhook/callback
   */
  async processCallback(callbackData: Record<string, string>) {
    const isValid = this.verifyPaymentResponse(callbackData)
    
    if (!isValid) {
      throw new Error('Invalid payment response hash')
    }

    const {
      txnid: transactionId,
      status,
      amount,
      payuMoneyId,
      mihpayid,
      error_Message
    } = callbackData

    return {
      transactionId,
      status: this.mapPayUStatus(status),
      amount: parseFloat(amount) / 100, // Convert back from paisa
      gatewayTransactionId: payuMoneyId || mihpayid,
      errorMessage: error_Message,
      isSuccess: status === 'success',
      rawResponse: callbackData
    }
  }

  /**
   * Map PayU status to our internal status
   */
  private mapPayUStatus(payuStatus: string): string {
    switch (payuStatus.toLowerCase()) {
      case 'success':
        return 'PAID'
      case 'failure':
        return 'FAILED'
      case 'pending':
        return 'PROCESSING'
      default:
        return 'FAILED'
    }
  }

  /**
   * Get transaction status from PayU
   */
  async getTransactionStatus(transactionId: string) {
    const command = 'verify_payment'
    const hashString = `${this.config.merchantKey}|${command}|${transactionId}|${this.config.salt}`
    const hash = crypto.createHash('sha512').update(hashString).digest('hex')

    const verifyUrl = this.config.isTestMode
      ? 'https://test.payu.in/merchant/postservice.php?form=2'
      : 'https://info.payu.in/merchant/postservice.php?form=2'

    const response = await fetch(verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        key: this.config.merchantId,
        command,
        var1: transactionId,
        hash
      })
    })

    const result = await response.json()
    return result
  }

  /**
   * Initiate refund (if supported)
   */
  async initiateRefund(transactionId: string, amount: number, reason: string) {
    // PayU refund implementation
    const command = 'cancel_refund_transaction'
    const hashString = `${this.config.merchantKey}|${command}|${transactionId}|${amount}|${this.config.salt}`
    const hash = crypto.createHash('sha512').update(hashString).digest('hex')

    const refundUrl = this.config.isTestMode
      ? 'https://test.payu.in/merchant/postservice.php?form=2'
      : 'https://info.payu.in/merchant/postservice.php?form=2'

    const response = await fetch(refundUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        key: this.config.merchantId,
        command,
        var1: transactionId,
        var2: amount.toString(),
        var3: reason,
        hash
      })
    })

    const result = await response.json()
    return result
  }
}

/**
 * Factory function to create PayU gateway instance
 */
export function createPayUGateway(): PayUGateway {
  const config: PayUConfig = {
    merchantId: process.env.PAYU_MERCHANT_ID || '',
    merchantKey: process.env.PAYU_MERCHANT_KEY || '',
    salt: process.env.PAYU_SALT || '',
    isTestMode: process.env.PAYU_TEST_MODE === 'true'
  }

  if (!config.merchantId || !config.merchantKey || !config.salt) {
    throw new Error('PayU configuration is incomplete. Please check environment variables.')
  }

  return new PayUGateway(config)
}
