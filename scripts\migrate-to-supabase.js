#!/usr/bin/env node

/**
 * Migration script to move from SQLite to Supabase PostgreSQL
 * This script handles the database migration and initial setup
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkEnvironmentVariables() {
  log('🔍 Checking environment variables...', 'blue');
  
  const requiredVars = [
    'DATABASE_URL',
    'DIRECT_URL',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL'
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    log('❌ Missing required environment variables:', 'red');
    missing.forEach(varName => log(`   - ${varName}`, 'red'));
    log('\nPlease set these variables in your .env file before running the migration.', 'yellow');
    log('Example .env configuration:', 'cyan');
    log(`
# Supabase Database Configuration
DATABASE_URL="postgresql://postgres:[password]@[host]:5432/postgres?pgbouncer=true&connection_limit=1"
DIRECT_URL="postgresql://postgres:[password]@[host]:5432/postgres"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://[project-id].supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="[anon-key]"
SUPABASE_SERVICE_ROLE_KEY="[service-role-key]"

# NextAuth Configuration
NEXTAUTH_SECRET="[32-character-secret]"
NEXTAUTH_URL="http://localhost:3000"
    `, 'cyan');
    process.exit(1);
  }

  log('✅ All required environment variables are set', 'green');
}

function backupSQLiteData() {
  log('💾 Creating backup of SQLite database...', 'blue');
  
  const sqliteDbPath = path.join(process.cwd(), 'prisma', 'dev.db');
  
  if (fs.existsSync(sqliteDbPath)) {
    const backupPath = path.join(process.cwd(), 'prisma', `dev.db.backup.${Date.now()}`);
    fs.copyFileSync(sqliteDbPath, backupPath);
    log(`✅ SQLite database backed up to: ${backupPath}`, 'green');
    return backupPath;
  } else {
    log('ℹ️  No existing SQLite database found, skipping backup', 'yellow');
    return null;
  }
}

function generatePrismaClient() {
  log('🔧 Generating Prisma client...', 'blue');
  
  try {
    execSync('npx prisma generate', { stdio: 'inherit' });
    log('✅ Prisma client generated successfully', 'green');
  } catch (error) {
    log('❌ Failed to generate Prisma client', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

function runDatabaseMigration() {
  log('🚀 Running database migration to Supabase...', 'blue');
  
  try {
    // Push the schema to Supabase
    execSync('npx prisma db push --accept-data-loss', { stdio: 'inherit' });
    log('✅ Database schema pushed to Supabase successfully', 'green');
  } catch (error) {
    log('❌ Failed to push database schema', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

function seedDatabase() {
  log('🌱 Seeding database with initial data...', 'blue');
  
  try {
    execSync('npm run db:seed', { stdio: 'inherit' });
    log('✅ Database seeded successfully', 'green');
  } catch (error) {
    log('❌ Failed to seed database', 'red');
    log(error.message, 'red');
    log('ℹ️  You can manually run "npm run db:seed" later', 'yellow');
  }
}

function updateVercelConfig() {
  log('⚙️  Updating Vercel configuration...', 'blue');
  
  const vercelConfigPath = path.join(process.cwd(), 'vercel.json');
  
  if (fs.existsSync(vercelConfigPath)) {
    const config = JSON.parse(fs.readFileSync(vercelConfigPath, 'utf8'));
    
    // Update environment variables for Supabase
    config.env = {
      ...config.env,
      DATABASE_URL: "@database_url",
      DIRECT_URL: "@direct_url",
      NEXT_PUBLIC_SUPABASE_URL: "@supabase_url",
      NEXT_PUBLIC_SUPABASE_ANON_KEY: "@supabase_anon_key",
      SUPABASE_SERVICE_ROLE_KEY: "@supabase_service_role_key",
      NEXTAUTH_SECRET: "@nextauth_secret",
      NEXTAUTH_URL: "@nextauth_url"
    };

    // Remove SQLite-specific configuration
    delete config.env.DATABASE_URL;
    if (config.build && config.build.env) {
      delete config.build.env.DATABASE_URL;
    }

    // Update function timeout for email processing
    config.functions = {
      ...config.functions,
      "src/app/api/email/**/*.ts": {
        "maxDuration": 30
      },
      "src/app/api/student/**/*.ts": {
        "maxDuration": 30
      }
    };

    fs.writeFileSync(vercelConfigPath, JSON.stringify(config, null, 2));
    log('✅ Vercel configuration updated', 'green');
  } else {
    log('⚠️  vercel.json not found, skipping Vercel config update', 'yellow');
  }
}

function createEmailSystemTables() {
  log('📧 Creating email system specific configurations...', 'blue');
  
  // This will be handled by Prisma schema, but we can add any custom SQL here if needed
  log('✅ Email system tables will be created by Prisma schema', 'green');
}

function displayPostMigrationInstructions() {
  log('\n🎉 Migration completed successfully!', 'green');
  log('\n📋 Next steps:', 'cyan');
  log('1. Update your Vercel environment variables:', 'yellow');
  log('   - Go to your Vercel dashboard', 'yellow');
  log('   - Navigate to your project settings', 'yellow');
  log('   - Add the Supabase environment variables', 'yellow');
  log('\n2. Deploy to Vercel:', 'yellow');
  log('   npm run build', 'cyan');
  log('   vercel --prod', 'cyan');
  log('\n3. Test the email system:', 'yellow');
  log('   - Access the admin panel at /admin', 'yellow');
  log('   - Create email accounts', 'yellow');
  log('   - Test email functionality', 'yellow');
  log('\n4. Configure Supabase Storage:', 'yellow');
  log('   - Email attachments bucket will be created automatically', 'yellow');
  log('   - Configure RLS policies if needed', 'yellow');
  log('\n🔗 Useful commands:', 'cyan');
  log('   npm run db:studio  # Open Prisma Studio', 'cyan');
  log('   npm run dev        # Start development server', 'cyan');
  log('   npm run build      # Build for production', 'cyan');
}

async function main() {
  log('🚀 Starting migration from SQLite to Supabase PostgreSQL', 'bright');
  log('=' * 60, 'cyan');

  try {
    // Step 1: Check environment variables
    checkEnvironmentVariables();

    // Step 2: Backup existing SQLite data
    const backupPath = backupSQLiteData();

    // Step 3: Generate Prisma client
    generatePrismaClient();

    // Step 4: Run database migration
    runDatabaseMigration();

    // Step 5: Seed database
    seedDatabase();

    // Step 6: Update Vercel configuration
    updateVercelConfig();

    // Step 7: Create email system specific configurations
    createEmailSystemTables();

    // Step 8: Display post-migration instructions
    displayPostMigrationInstructions();

  } catch (error) {
    log('\n❌ Migration failed:', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  main();
}

module.exports = {
  checkEnvironmentVariables,
  backupSQLiteData,
  generatePrismaClient,
  runDatabaseMigration,
  seedDatabase,
  updateVercelConfig,
  createEmailSystemTables
};
