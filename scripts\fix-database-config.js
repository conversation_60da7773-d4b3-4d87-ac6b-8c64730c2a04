#!/usr/bin/env node

/**
 * Automatic Database Configuration Fix Script
 * Fixes PostgreSQL to SQLite configuration issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 AUTOMATIC DATABASE CONFIGURATION FIX\n');

let fixesApplied = 0;

// Fix 1: Update Prisma schema
console.log('1. Fixing Prisma schema...');
try {
  if (fs.existsSync('prisma/schema.prisma')) {
    let schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
    let schemaChanged = false;
    
    // Fix provider
    if (schemaContent.includes('provider = "postgresql"')) {
      schemaContent = schemaContent.replace(
        /provider\s*=\s*"postgresql"/g,
        'provider = "sqlite"'
      );
      console.log('   ✅ Changed provider from postgresql to sqlite');
      schemaChanged = true;
      fixesApplied++;
    }
    
    // Remove directUrl
    if (schemaContent.includes('directUrl')) {
      schemaContent = schemaContent.replace(
        /\s*directUrl\s*=\s*env\("DIRECT_URL"\)\s*/g,
        ''
      );
      console.log('   ✅ Removed directUrl (PostgreSQL specific)');
      schemaChanged = true;
      fixesApplied++;
    }
    
    // Fix Json type to String for SQLite compatibility
    if (schemaContent.includes('Json?')) {
      schemaContent = schemaContent.replace(
        /Json\?/g,
        'String? // JSON stored as string for SQLite compatibility'
      );
      console.log('   ✅ Changed Json? to String? for SQLite compatibility');
      schemaChanged = true;
      fixesApplied++;
    }
    
    if (schemaContent.includes('Json ')) {
      schemaContent = schemaContent.replace(
        /Json /g,
        'String // JSON stored as string for SQLite compatibility '
      );
      console.log('   ✅ Changed Json to String for SQLite compatibility');
      schemaChanged = true;
      fixesApplied++;
    }
    
    if (schemaChanged) {
      fs.writeFileSync('prisma/schema.prisma', schemaContent);
      console.log('   ✅ Prisma schema updated');
    } else {
      console.log('   ℹ️  Prisma schema already correct');
    }
  } else {
    console.log('   ❌ Prisma schema file not found');
  }
} catch (error) {
  console.log(`   ❌ Error fixing Prisma schema: ${error.message}`);
}

// Fix 2: Update .env file
console.log('\n2. Fixing .env file...');
try {
  if (fs.existsSync('.env')) {
    let envContent = fs.readFileSync('.env', 'utf8');
    let envChanged = false;
    
    // Fix DATABASE_URL
    const dbUrlMatch = envContent.match(/DATABASE_URL\s*=\s*["']?([^"'\n]+)["']?/);
    if (dbUrlMatch && !dbUrlMatch[1].startsWith('file:')) {
      envContent = envContent.replace(
        /DATABASE_URL\s*=\s*["']?[^"'\n]+["']?/,
        'DATABASE_URL="file:./dev.db"'
      );
      console.log('   ✅ Fixed DATABASE_URL to use SQLite');
      envChanged = true;
      fixesApplied++;
    }
    
    // Remove DIRECT_URL
    if (envContent.includes('DIRECT_URL')) {
      envContent = envContent.replace(
        /DIRECT_URL\s*=\s*["']?[^"'\n]+["']?\s*\n?/g,
        ''
      );
      console.log('   ✅ Removed DIRECT_URL (PostgreSQL specific)');
      envChanged = true;
      fixesApplied++;
    }
    
    if (envChanged) {
      fs.writeFileSync('.env', envContent);
      console.log('   ✅ .env file updated');
    } else {
      console.log('   ℹ️  .env file already correct');
    }
  } else {
    console.log('   ❌ .env file not found');
  }
} catch (error) {
  console.log(`   ❌ Error fixing .env file: ${error.message}`);
}

// Fix 3: Clean up old database files and cache
console.log('\n3. Cleaning up old files...');
try {
  // Remove old database files
  const oldDbFiles = ['postgres.db', 'postgresql.db'];
  oldDbFiles.forEach(file => {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
      console.log(`   ✅ Removed old database file: ${file}`);
      fixesApplied++;
    }
  });
  
  // Remove Prisma cache
  if (fs.existsSync('node_modules/.prisma')) {
    fs.rmSync('node_modules/.prisma', { recursive: true, force: true });
    console.log('   ✅ Removed Prisma cache');
    fixesApplied++;
  }
  
  if (fixesApplied === 0) {
    console.log('   ℹ️  No cleanup needed');
  }
} catch (error) {
  console.log(`   ❌ Error during cleanup: ${error.message}`);
}

// Summary
console.log('\n📊 SUMMARY:');
console.log(`   Fixes applied: ${fixesApplied}`);

if (fixesApplied > 0) {
  console.log('\n✅ Configuration fixed! Next steps:');
  console.log('1. npx prisma generate');
  console.log('2. npx prisma db push --force-reset');
  console.log('3. npx prisma db seed (optional)');
  console.log('4. npm run dev');
  
  console.log('\n🎯 Or run the automated setup:');
  console.log('   scripts/reset-database.bat (Windows)');
  console.log('   scripts/reset-database.sh (Unix/Linux)');
} else {
  console.log('\n✅ Configuration is already correct!');
  console.log('If you\'re still having issues, try:');
  console.log('1. scripts/reset-database.bat (Windows)');
  console.log('2. scripts/reset-database.sh (Unix/Linux)');
}

console.log('\n✨ Fix complete!');
