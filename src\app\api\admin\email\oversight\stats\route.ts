import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'

// GET /api/admin/email/oversight/stats - Get email statistics for admin oversight
export const GET = createSecureApi(
  async (context) => {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      // Get comprehensive email statistics
      const [
        totalEmails,
        unreadEmails,
        spamEmails,
        todayEmails,
        attachmentEmails
      ] = await Promise.all([
        // Total emails in the system
        prisma.emailRecipient.count({
          where: { isDeleted: false }
        }),

        // Unread emails
        prisma.emailRecipient.count({
          where: {
            isDeleted: false,
            isRead: false
          }
        }),

        // Spam emails
        prisma.emailRecipient.count({
          where: {
            isDeleted: false,
            email: {
              isSpam: true
            }
          }
        }),

        // Emails received today
        prisma.emailRecipient.count({
          where: {
            isDeleted: false,
            email: {
              sentAt: {
                gte: today
              }
            }
          }
        }),

        // Emails with attachments
        prisma.emailRecipient.count({
          where: {
            isDeleted: false,
            email: {
              attachments: {
                some: {}
              }
            }
          }
        })
      ])

      return NextResponse.json({
        success: true,
        statistics: {
          totalEmails,
          unreadEmails,
          spamEmails,
          todayEmails,
          attachmentEmails
        }
      })

    } catch (error) {
      console.error('Email oversight stats error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve email statistics' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: false
  }
)
