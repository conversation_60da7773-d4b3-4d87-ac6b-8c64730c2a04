/* Rich Text Editor Custom Styles */

/* Editor Container */
.rich-text-editor {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

/* Toolbar Styling */
.editor-toolbar {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: space-between;
  align-items: center;
}

.toolbar-section {
  display: flex;
  gap: 4px;
  border-right: 1px solid #d1d5db;
  padding-right: 8px;
  margin-right: 8px;
}

.toolbar-section:last-child {
  border-right: none;
  margin-right: 0;
}

/* Button Styling */
.toolbar-button {
  padding: 8px;
  border-radius: 4px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #374151;
}

.toolbar-button:hover {
  background: #e5e7eb;
}

.toolbar-button.active {
  background: #dbeafe;
  color: #1d4ed8;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dropdown Styling */
.toolbar-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
  padding: 8px;
  display: none;
}

.toolbar-dropdown:hover .dropdown-content,
.dropdown-content:hover {
  display: block;
}

.dropdown-item {
  width: 100%;
  text-align: left;
  padding: 6px 8px;
  font-size: 12px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.dropdown-item:hover {
  background: #f3f4f6;
}

/* Color Picker Styling */
.color-palette {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.color-swatch:hover {
  transform: scale(1.1);
}

.color-input {
  width: 100%;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  cursor: pointer;
}

/* Editor Content Styling */
.editor-content {
  background: white;
}

.editor-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.editor-scroll-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.editor-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.editor-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.editor-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ProseMirror Editor Styling */
.ProseMirror {
  outline: none;
  padding: 16px;
  min-height: 300px;
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ProseMirror p {
  margin: 0 0 16px 0;
}

.ProseMirror h1, .ProseMirror h2, .ProseMirror h3, .ProseMirror h4, .ProseMirror h5, .ProseMirror h6 {
  margin: 24px 0 16px 0;
  font-weight: 600;
  line-height: 1.3;
}

.ProseMirror h1 { font-size: 2em; }
.ProseMirror h2 { font-size: 1.5em; }
.ProseMirror h3 { font-size: 1.25em; }

.ProseMirror ul, .ProseMirror ol {
  margin: 16px 0;
  padding-left: 24px;
}

.ProseMirror li {
  margin: 4px 0;
}

.ProseMirror blockquote {
  margin: 16px 0;
  padding-left: 16px;
  border-left: 4px solid #e5e7eb;
  font-style: italic;
  color: #6b7280;
}

.ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.ProseMirror th, .ProseMirror td {
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  text-align: left;
}

.ProseMirror th {
  background-color: #f9fafb;
  font-weight: 600;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

/* Highlight Styling */
.ProseMirror mark {
  background-color: #fef08a;
  padding: 2px 4px;
  border-radius: 2px;
}

/* Split View Styling */
.split-view {
  display: flex;
  height: 500px;
}

.split-view .editor-side {
  flex: 1;
  border-right: 1px solid #d1d5db;
}

.split-view .preview-side {
  flex: 1;
  background: #f9fafb;
  overflow-y: auto;
  padding: 16px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .editor-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .toolbar-section {
    border-right: none;
    border-bottom: 1px solid #d1d5db;
    padding-bottom: 8px;
    margin-bottom: 8px;
    margin-right: 0;
    justify-content: center;
  }
  
  .toolbar-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
  
  .toolbar-button {
    padding: 12px;
    font-size: 14px;
  }
  
  .split-view {
    flex-direction: column;
    height: auto;
  }
  
  .split-view .editor-side {
    border-right: none;
    border-bottom: 1px solid #d1d5db;
    min-height: 300px;
  }
  
  .dropdown-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    max-width: 300px;
  }
}

/* Accessibility */
.toolbar-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.dropdown-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

/* Animation */
.dropdown-content {
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-4px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Content Preservation Indicators */
.preserved-content {
  border: 1px dashed #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.preserved-content::before {
  content: "Preserved Content";
  font-size: 10px;
  color: #10b981;
  background: white;
  padding: 2px 4px;
  border-radius: 2px;
  position: absolute;
  top: -8px;
  left: 4px;
}
