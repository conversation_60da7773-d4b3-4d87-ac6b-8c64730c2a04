import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// GET /api/email/accounts/[id] - Get single email account details
export const GET = createSecureApi(
  async (context, body, params) => {
    try {
      const accountId = params?.id

      if (!accountId) {
        return NextResponse.json(
          { error: 'Account ID is required' },
          { status: 400 }
        )
      }

      // Get account details
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId },
        select: {
          id: true,
          email: true,
          displayName: true,
          accountType: true,
          isActive: true,
          studentId: true,
          rollNumber: true,
          course: true,
          batch: true,
          department: true,
          designation: true,
          storageUsed: true,
          storageLimit: true,
          imapEnabled: true,
          pop3Enabled: true,
          smtpEnabled: true,
          createdAt: true,
          updatedAt: true,
          createdBy: {
            select: {
              name: true,
              email: true
            }
          },
          folders: {
            select: {
              id: true,
              name: true,
              folderType: true,
              isSystem: true,
              order: true
            },
            orderBy: [
              { isSystem: 'desc' },
              { order: 'asc' }
            ]
          }
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found' },
          { status: 404 }
        )
      }

      // Get email statistics
      const emailStats = await prisma.emailRecipient.groupBy({
        by: ['isRead'],
        where: {
          accountId,
          isDeleted: false
        },
        _count: {
          isRead: true
        }
      })

      const totalEmails = emailStats.reduce((sum, stat) => sum + stat._count.isRead, 0)
      const unreadEmails = emailStats.find(stat => !stat.isRead)?._count.isRead || 0

      // Get recent email activity
      const recentEmails = await prisma.emailRecipient.findMany({
        where: {
          accountId,
          isDeleted: false
        },
        include: {
          email: {
            select: {
              subject: true,
              fromEmail: true,
              sentAt: true,
              createdAt: true
            }
          }
        },
        orderBy: {
          email: {
            sentAt: 'desc'
          }
        },
        take: 5
      })

      return NextResponse.json({
        success: true,
        account: {
          ...account,
          statistics: {
            totalEmails,
            unreadEmails,
            readEmails: totalEmails - unreadEmails,
            storageUsedPercent: (account.storageUsed / account.storageLimit) * 100
          },
          recentActivity: recentEmails.map(recipient => ({
            subject: recipient.email.subject,
            fromEmail: recipient.email.fromEmail,
            receivedAt: recipient.email.sentAt || recipient.email.createdAt,
            isRead: recipient.isRead
          }))
        }
      })

    } catch (error) {
      console.error('Get email account error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve email account' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: false
  }
)

// PATCH /api/email/accounts/[id] - Update email account
export const PATCH = createSecureApi(
  async (context, body, params) => {
    try {
      const accountId = params?.id
      const updateData = await context.request.json()

      if (!accountId) {
        return NextResponse.json(
          { error: 'Account ID is required' },
          { status: 400 }
        )
      }

      // Check if account exists
      const existingAccount = await prisma.emailAccount.findUnique({
        where: { id: accountId }
      })

      if (!existingAccount) {
        return NextResponse.json(
          { error: 'Account not found' },
          { status: 404 }
        )
      }

      // Prepare update data
      const allowedFields = [
        'displayName',
        'isActive',
        'rollNumber',
        'course',
        'batch',
        'department',
        'designation',
        'storageLimit',
        'imapEnabled',
        'pop3Enabled',
        'smtpEnabled'
      ]

      const filteredUpdateData: any = {}
      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          filteredUpdateData[field] = updateData[field]
        }
      }

      // Handle password update separately
      if (updateData.password) {
        filteredUpdateData.password = await bcrypt.hash(updateData.password, 12)
      }

      // Update account
      const updatedAccount = await prisma.emailAccount.update({
        where: { id: accountId },
        data: filteredUpdateData,
        select: {
          id: true,
          email: true,
          displayName: true,
          accountType: true,
          isActive: true,
          studentId: true,
          rollNumber: true,
          course: true,
          batch: true,
          department: true,
          designation: true,
          storageUsed: true,
          storageLimit: true,
          imapEnabled: true,
          pop3Enabled: true,
          smtpEnabled: true,
          updatedAt: true
        }
      })

      return NextResponse.json({
        success: true,
        account: updatedAccount,
        message: 'Account updated successfully'
      })

    } catch (error) {
      console.error('Update email account error:', error)
      return NextResponse.json(
        { error: 'Failed to update email account' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: true,
    sanitizeInput: true
  }
)

// DELETE /api/email/accounts/[id] - Delete email account
export const DELETE = createSecureApi(
  async (context, body, params) => {
    try {
      const accountId = params?.id
      const { searchParams } = new URL(context.request.url)
      const permanent = searchParams.get('permanent') === 'true'

      if (!accountId) {
        return NextResponse.json(
          { error: 'Account ID is required' },
          { status: 400 }
        )
      }

      // Check if account exists
      const existingAccount = await prisma.emailAccount.findUnique({
        where: { id: accountId }
      })

      if (!existingAccount) {
        return NextResponse.json(
          { error: 'Account not found' },
          { status: 404 }
        )
      }

      if (permanent) {
        // Permanent deletion - remove all related data
        await prisma.$transaction(async (tx) => {
          // Delete email sessions
          await tx.emailSession.deleteMany({
            where: { accountId }
          })

          // Delete email recipients (this will remove access to emails)
          await tx.emailRecipient.deleteMany({
            where: { accountId }
          })

          // Delete folders
          await tx.emailFolder.deleteMany({
            where: { accountId }
          })

          // Delete aliases
          await tx.emailAlias.deleteMany({
            where: { accountId }
          })

          // Delete the account
          await tx.emailAccount.delete({
            where: { id: accountId }
          })
        })

        return NextResponse.json({
          success: true,
          message: 'Account permanently deleted'
        })
      } else {
        // Soft deletion - deactivate account
        await prisma.emailAccount.update({
          where: { id: accountId },
          data: {
            isActive: false,
            email: `deleted_${Date.now()}_${existingAccount.email}` // Prevent email conflicts
          }
        })

        return NextResponse.json({
          success: true,
          message: 'Account deactivated'
        })
      }

    } catch (error) {
      console.error('Delete email account error:', error)
      return NextResponse.json(
        { error: 'Failed to delete email account' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: true
  }
)
