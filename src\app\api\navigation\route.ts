import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/navigation - Get public navigation items (no auth required)
export async function GET(request: NextRequest) {
  try {
    const navigationItems = await prisma.navigationItem.findMany({
      orderBy: [
        { order: 'asc' },
        { title: 'asc' }
      ],
      include: {
        children: {
          orderBy: [
            { order: 'asc' },
            { title: 'asc' }
          ],
          include: {
            children: {
              orderBy: [
                { order: 'asc' },
                { title: 'asc' }
              ]
            }
          }
        }
      },
      where: {
        parentId: null, // Only get top-level items, children are included
        isVisible: true // Only get visible items
      }
    })

    return NextResponse.json({ navigation: navigationItems })
  } catch (error) {
    console.error('Public Navigation API Error:', error)

    // Return default navigation structure as fallback
    const defaultNavigation = [
      {
        id: 'nav-home',
        title: 'Home',
        href: '/',
        parentId: null,
        order: 0,
        isVisible: true,
        linkType: 'internal',
        target: '_self',
        children: []
      },
      {
        id: 'nav-about',
        title: 'About Us',
        href: null,
        parentId: null,
        order: 1,
        isVisible: true,
        linkType: 'dropdown',
        target: '_self',
        children: [
          {
            id: 'nav-about-institute',
            title: 'About Institute',
            href: '/about-institute',
            parentId: 'nav-about',
            order: 0,
            isVisible: true,
            linkType: 'internal',
            target: '_self',
            children: []
          }
        ]
      }
    ]

    return NextResponse.json({ navigation: defaultNavigation })
  }
}
