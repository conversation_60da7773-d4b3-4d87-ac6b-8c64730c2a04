@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Custom Color Palette - WCAG AA Compliant High Contrast */
  --color-primary: #1a365d;      /* Dark blue - 7.2:1 contrast on white */
  --color-secondary: #c53030;    /* Dark red - 5.9:1 contrast on white */
  --color-accent: #2d7d32;       /* Dark green - 5.4:1 contrast on white */
  --color-text-primary: #000000; /* Pure black - 21:1 contrast on white */
  --color-text-secondary: #2d3748; /* Dark gray - 12.6:1 contrast on white */
  --color-bg-primary: #ffffff;   /* Pure white background */
  --color-bg-secondary: #f7fafc; /* Very light gray */
  --color-border: #cbd5e0;       /* Medium gray for borders */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Disabled dark mode to ensure consistent light theme for accessibility
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
*/

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Loading Screen Animations */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin-slow-reverse {
  from { transform: rotate(360deg); }
  to { transform: rotate(0deg); }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-spin-slow-reverse {
  animation: spin-slow-reverse 12s linear infinite;
}

/* Content Styling for Database Pages */
.prose {
  max-width: none;
}

.prose table,
.table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.prose table th,
.prose table td,
.table th,
.table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  color: var(--color-text-primary);
}

.prose table th,
.table th {
  background-color: var(--color-bg-secondary);
  font-weight: 600;
  color: var(--color-text-primary);
  border-bottom: 2px solid var(--color-border);
}

.prose table tbody tr:nth-child(even),
.table tbody tr:nth-child(even) {
  background-color: var(--color-bg-secondary);
}

.prose table tbody tr:hover,
.table tbody tr:hover {
  background-color: #e2e8f0;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: var(--color-text-primary);
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.25;
}

.prose h1 {
  font-size: 2.25rem;
  margin-top: 0;
}

.prose h2 {
  font-size: 1.875rem;
  border-bottom: 2px solid var(--color-border);
  padding-bottom: 0.5rem;
}

.prose h3 {
  font-size: 1.5rem;
}

.prose h4 {
  font-size: 1.25rem;
}

.prose p {
  color: var(--color-text-primary);
  line-height: 1.75;
  margin: 1rem 0;
}

.prose ul,
.prose ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.prose li {
  color: var(--color-text-primary);
  margin: 0.5rem 0;
  line-height: 1.6;
}

.prose ul li {
  list-style-type: disc;
}

.prose ol li {
  list-style-type: decimal;
}

.prose ul ul li {
  list-style-type: circle;
}

.prose ul ul ul li {
  list-style-type: square;
}

.prose a {
  color: var(--color-primary);
  text-decoration: underline;
  font-weight: 500;
}

.prose a:hover {
  color: var(--color-secondary);
  text-decoration: none;
}

.prose strong,
.prose b {
  color: var(--color-text-primary);
  font-weight: 700;
}

.prose em,
.prose i {
  font-style: italic;
}

.prose blockquote {
  border-left: 4px solid var(--color-primary);
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: var(--color-text-secondary);
  background-color: var(--color-bg-secondary);
  padding: 1rem;
  border-radius: 0.25rem;
}

.prose code {
  background-color: var(--color-bg-secondary);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.prose pre {
  background-color: var(--color-bg-secondary);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  border: 1px solid var(--color-border);
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  border: none;
}

/* Professional content sections */
.prose .content-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.prose .highlight-box {
  background-color: var(--color-bg-secondary);
  border-left: 4px solid var(--color-primary);
  padding: 1rem;
  margin: 1.5rem 0;
  border-radius: 0.25rem;
}

.prose .info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
}

.prose .info-card {
  background-color: var(--color-bg-secondary);
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
}

.prose .info-card h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--color-primary);
}

/* Enhanced list styling */
.prose ul li::marker {
  color: var(--color-primary);
}

.prose ol li::marker {
  color: var(--color-primary);
  font-weight: 600;
}

/* Better spacing for nested lists */
.prose ul ul,
.prose ol ol,
.prose ul ol,
.prose ol ul {
  margin: 0.5rem 0;
}

/* Contact information styling */
.prose .contact-info {
  background-color: var(--color-bg-secondary);
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  margin: 1.5rem 0;
}

.prose .contact-info h3 {
  margin-top: 0;
  color: var(--color-primary);
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: 0.5rem;
}

/* Status badges */
.prose .status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: var(--color-accent);
  color: white;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0.25rem;
}

.prose .status-badge.draft {
  background-color: #f59e0b;
}

.prose .status-badge.published {
  background-color: var(--color-accent);
}

.prose .status-badge.archived {
  background-color: #6b7280;
}

/* Responsive table styling */
@media (max-width: 768px) {
  .prose table,
  .table {
    font-size: 0.875rem;
  }

  .prose table th,
  .prose table td,
  .table th,
  .table td {
    padding: 0.5rem;
  }

  .prose .info-grid {
    grid-template-columns: 1fr;
  }
}
