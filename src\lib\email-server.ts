import { prisma } from './prisma'
import { supabaseAdmin, uploadEmailAttachment } from './supabase'
import nodemailer from 'nodemailer'
import { v4 as uuidv4 } from 'uuid'
import crypto from 'crypto'
import { getFolderByType, createDefaultFolders } from './email-folder-setup'

// Email server configuration
export const EMAIL_CONFIG = {
  domain: process.env.EMAIL_DOMAIN || 'institute.edu',
  maxAttachmentSize: parseInt(process.env.EMAIL_ATTACHMENT_LIMIT || '********'), // 10MB
  maxStoragePerAccount: parseInt(process.env.EMAIL_STORAGE_LIMIT || '**********'), // 1GB
  rateLimit: parseInt(process.env.EMAIL_RATE_LIMIT || '100'), // emails per hour
  smtpHost: process.env.SMTP_HOST || 'localhost',
  smtpPort: parseInt(process.env.SMTP_PORT || '587'),
  smtpUser: process.env.SMTP_USER,
  smtpPass: process.env.SMTP_PASS,
  smtpSecure: process.env.SMTP_SECURE === 'true',
}

// Email message interface
export interface EmailMessage {
  messageId?: string
  subject: string
  body: string
  bodyText?: string
  fromEmail: string
  fromName?: string
  toEmails: string[]
  ccEmails?: string[]
  bccEmails?: string[]
  attachments?: EmailAttachment[]
  priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  inReplyTo?: string
  references?: string
  isDraft?: boolean
}

export interface EmailAttachment {
  filename: string
  content: Buffer | string
  contentType: string
  size: number
}

// Generate RFC 5322 compliant Message-ID
export function generateMessageId(domain: string = EMAIL_CONFIG.domain): string {
  const timestamp = Date.now()
  const random = crypto.randomBytes(8).toString('hex')
  return `<${timestamp}.${random}@${domain}>`
}

// Generate thread ID for email conversations
export function generateThreadId(): string {
  return `thread_${uuidv4()}`
}

// Create SMTP transporter for external email delivery
export function createSMTPTransporter() {
  if (!EMAIL_CONFIG.smtpUser || !EMAIL_CONFIG.smtpPass) {
    throw new Error('SMTP credentials not configured')
  }

  return nodemailer.createTransporter({
    host: EMAIL_CONFIG.smtpHost,
    port: EMAIL_CONFIG.smtpPort,
    secure: EMAIL_CONFIG.smtpSecure,
    auth: {
      user: EMAIL_CONFIG.smtpUser,
      pass: EMAIL_CONFIG.smtpPass,
    },
    pool: true,
    maxConnections: 5,
    maxMessages: 100,
  })
}

// Check if email address is internal (within our domain)
export function isInternalEmail(email: string): boolean {
  return email.endsWith(`@${EMAIL_CONFIG.domain}`)
}

// Validate email address format
export function validateEmailAddress(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Check storage quota for email account
export async function checkStorageQuota(accountId: string, additionalSize: number = 0): Promise<boolean> {
  const account = await prisma.emailAccount.findUnique({
    where: { id: accountId },
    select: { storageUsed: true, storageLimit: true }
  })

  if (!account) return false
  return (account.storageUsed + additionalSize) <= account.storageLimit
}

// Update storage usage for email account
export async function updateStorageUsage(accountId: string, sizeChange: number): Promise<void> {
  await prisma.emailAccount.update({
    where: { id: accountId },
    data: {
      storageUsed: {
        increment: sizeChange
      }
    }
  })
}

// Check rate limiting for email sending
export async function checkRateLimit(accountId: string): Promise<boolean> {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
  
  const sentCount = await prisma.email.count({
    where: {
      fromAccountId: accountId,
      sentAt: {
        gte: oneHourAgo
      }
    }
  })

  return sentCount < EMAIL_CONFIG.rateLimit
}

// Create default folders for new email account
export async function createDefaultFolders(accountId: string): Promise<void> {
  const defaultFolders = [
    { name: 'INBOX', folderType: 'INBOX' as const, isSystem: true, order: 1 },
    { name: 'SENT', folderType: 'SENT' as const, isSystem: true, order: 2 },
    { name: 'DRAFTS', folderType: 'DRAFTS' as const, isSystem: true, order: 3 },
    { name: 'TRASH', folderType: 'TRASH' as const, isSystem: true, order: 4 },
    { name: 'SPAM', folderType: 'SPAM' as const, isSystem: true, order: 5 },
    { name: 'ARCHIVE', folderType: 'ARCHIVE' as const, isSystem: true, order: 6 },
  ]

  for (const folder of defaultFolders) {
    await prisma.emailFolder.create({
      data: {
        ...folder,
        accountId,
      }
    })
  }
}

// Get folder by type for an account
export async function getFolderByType(accountId: string, folderType: string) {
  return await prisma.emailFolder.findFirst({
    where: {
      accountId,
      folderType: folderType as any,
      isSystem: true
    }
  })
}

// Process email attachments
export async function processAttachments(
  emailId: string,
  attachments: EmailAttachment[]
): Promise<{ success: boolean; attachmentIds: string[]; totalSize: number }> {
  const attachmentIds: string[] = []
  let totalSize = 0

  try {
    for (const attachment of attachments) {
      // Validate attachment size
      if (attachment.size > EMAIL_CONFIG.maxAttachmentSize) {
        throw new Error(`Attachment ${attachment.filename} exceeds size limit`)
      }

      // Upload to Supabase storage
      const { url, error } = await uploadEmailAttachment(
        Buffer.from(attachment.content),
        attachment.filename,
        emailId
      )

      if (error) {
        throw new Error(`Failed to upload attachment ${attachment.filename}: ${error}`)
      }

      // Save attachment record
      const attachmentRecord = await prisma.emailAttachment.create({
        data: {
          emailId,
          filename: attachment.filename,
          originalName: attachment.filename,
          mimeType: attachment.contentType,
          size: attachment.size,
          url,
        }
      })

      attachmentIds.push(attachmentRecord.id)
      totalSize += attachment.size
    }

    return { success: true, attachmentIds, totalSize }
  } catch (error) {
    console.error('Error processing attachments:', error)
    return { success: false, attachmentIds: [], totalSize: 0 }
  }
}

// Apply spam filters to email
export async function applySpamFilters(email: EmailMessage): Promise<{ isSpam: boolean; action: string }> {
  const spamFilters = await prisma.spamFilter.findMany({
    where: { isActive: true },
    orderBy: { priority: 'asc' }
  })

  for (const filter of spamFilters) {
    let testContent = ''
    
    switch (filter.filterType) {
      case 'SUBJECT':
        testContent = email.subject
        break
      case 'BODY':
        testContent = email.bodyText || email.body
        break
      case 'SENDER':
        testContent = email.fromEmail
        break
      case 'DOMAIN':
        testContent = email.fromEmail.split('@')[1] || ''
        break
      default:
        continue
    }

    try {
      const regex = new RegExp(filter.pattern, 'i')
      if (regex.test(testContent)) {
        console.log(`Spam filter triggered: ${filter.name}`)
        return { isSpam: true, action: filter.action }
      }
    } catch (error) {
      console.error(`Invalid regex pattern in filter ${filter.name}:`, error)
    }
  }

  return { isSpam: false, action: 'ALLOW' }
}

// Calculate email size for storage tracking
export function calculateEmailSize(email: EmailMessage, attachments: EmailAttachment[] = []): number {
  const bodySize = Buffer.byteLength(email.body, 'utf8')
  const bodyTextSize = email.bodyText ? Buffer.byteLength(email.bodyText, 'utf8') : 0
  const subjectSize = Buffer.byteLength(email.subject, 'utf8')
  const attachmentSize = attachments.reduce((total, att) => total + att.size, 0)
  
  return bodySize + bodyTextSize + subjectSize + attachmentSize
}

// Generate email headers for RFC compliance
export function generateEmailHeaders(email: EmailMessage, messageId: string): Record<string, string> {
  const headers: Record<string, string> = {
    'Message-ID': messageId,
    'Date': new Date().toUTCString(),
    'From': email.fromName ? `${email.fromName} <${email.fromEmail}>` : email.fromEmail,
    'To': email.toEmails.join(', '),
    'Subject': email.subject,
    'MIME-Version': '1.0',
    'Content-Type': 'multipart/mixed; boundary="boundary123"',
  }

  if (email.ccEmails && email.ccEmails.length > 0) {
    headers['Cc'] = email.ccEmails.join(', ')
  }

  if (email.inReplyTo) {
    headers['In-Reply-To'] = email.inReplyTo
  }

  if (email.references) {
    headers['References'] = email.references
  }

  if (email.priority && email.priority !== 'NORMAL') {
    headers['X-Priority'] = email.priority === 'HIGH' ? '2' : email.priority === 'URGENT' ? '1' : '4'
  }

  return headers
}

// Email server error types
export class EmailServerError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message)
    this.name = 'EmailServerError'
  }
}

export class QuotaExceededError extends EmailServerError {
  constructor(message: string = 'Storage quota exceeded') {
    super(message, 'QUOTA_EXCEEDED', 413)
  }
}

export class RateLimitError extends EmailServerError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_EXCEEDED', 429)
  }
}

export class InvalidEmailError extends EmailServerError {
  constructor(message: string = 'Invalid email format') {
    super(message, 'INVALID_EMAIL', 400)
  }
}

/**
 * Send email from student account
 */
export async function sendEmail(
  accountId: string,
  emailData: {
    subject: string
    body: string
    bodyText: string
    fromEmail: string
    fromName: string
    toEmails: string[]
    ccEmails?: string[]
    bccEmails?: string[]
    attachments?: any[]
    priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  }
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    const { v4: uuidv4 } = require('uuid')

    // Generate unique message ID
    const messageId = `<${uuidv4()}@${emailData.fromEmail.split('@')[1]}>`

    // Create the email record
    const email = await prisma.email.create({
      data: {
        messageId,
        subject: emailData.subject,
        body: emailData.body,
        bodyText: emailData.bodyText,
        fromEmail: emailData.fromEmail,
        fromName: emailData.fromName,
        fromAccountId: accountId,
        toEmails: emailData.toEmails,
        ccEmails: emailData.ccEmails || [],
        bccEmails: emailData.bccEmails || [],
        priority: emailData.priority || 'NORMAL',
        sentAt: new Date(),
        isStarred: false,
        isDeleted: false
      }
    })

    // Get sender's sent folder
    const sentFolder = await prisma.emailFolder.findFirst({
      where: {
        accountId: accountId,
        folderType: 'SENT'
      }
    })

    // Add to sender's sent folder
    if (sentFolder) {
      await prisma.emailRecipient.create({
        data: {
          emailId: email.id,
          accountId: accountId,
          folderId: sentFolder.id,
          isRead: true,
          isDeleted: false
        }
      })
    }

    // Process recipients (for internal emails)
    const allRecipients = [
      ...emailData.toEmails,
      ...(emailData.ccEmails || []),
      ...(emailData.bccEmails || [])
    ]

    for (const recipientEmail of allRecipients) {
      // Check if recipient has an account in our system
      const recipientAccount = await prisma.emailAccount.findFirst({
        where: { email: recipientEmail, isActive: true }
      })

      if (recipientAccount) {
        // Get recipient's inbox folder
        const inboxFolder = await prisma.emailFolder.findFirst({
          where: {
            accountId: recipientAccount.id,
            folderType: 'INBOX'
          }
        })

        if (inboxFolder) {
          await prisma.emailRecipient.create({
            data: {
              emailId: email.id,
              accountId: recipientAccount.id,
              folderId: inboxFolder.id,
              isRead: false,
              isDeleted: false
            }
          })
        }
      }
    }

    // Handle attachments if any
    if (emailData.attachments && emailData.attachments.length > 0) {
      for (const attachment of emailData.attachments) {
        await prisma.emailAttachment.create({
          data: {
            emailId: email.id,
            filename: attachment.filename,
            originalName: attachment.originalName,
            mimeType: attachment.mimeType,
            size: attachment.size,
            path: attachment.path
          }
        })
      }
    }

    return { success: true, messageId: email.id }
  } catch (error) {
    console.error('Send email error:', error)
    return { success: false, error: 'Failed to send email' }
  }
}

/**
 * Save email draft
 */
export async function saveDraft(
  accountId: string,
  draftData: {
    subject: string
    body: string
    bodyText: string
    fromEmail: string
    fromName: string
    toEmails: string[]
    ccEmails?: string[]
    bccEmails?: string[]
    attachments?: any[]
    priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
    isDraft: boolean
  }
): Promise<{ success: boolean; draftId?: string; error?: string }> {
  try {
    const { v4: uuidv4 } = require('uuid')

    // Generate unique message ID
    const messageId = `<draft-${uuidv4()}@${draftData.fromEmail.split('@')[1]}>`

    // Create the draft email record
    const draft = await prisma.email.create({
      data: {
        messageId,
        subject: draftData.subject,
        body: draftData.body,
        bodyText: draftData.bodyText,
        fromEmail: draftData.fromEmail,
        fromName: draftData.fromName,
        fromAccountId: accountId,
        toEmails: draftData.toEmails,
        ccEmails: draftData.ccEmails || [],
        bccEmails: draftData.bccEmails || [],
        priority: draftData.priority || 'NORMAL',
        sentAt: null, // Not sent yet
        isStarred: false,
        isDeleted: false
      }
    })

    // Get drafts folder
    const draftsFolder = await prisma.emailFolder.findFirst({
      where: {
        accountId: accountId,
        folderType: 'DRAFTS'
      }
    })

    // Add to drafts folder
    if (draftsFolder) {
      await prisma.emailRecipient.create({
        data: {
          emailId: draft.id,
          accountId: accountId,
          folderId: draftsFolder.id,
          isRead: true, // Drafts are considered "read"
          isDeleted: false
        }
      })
    }

    // Handle attachments if any
    if (draftData.attachments && draftData.attachments.length > 0) {
      for (const attachment of draftData.attachments) {
        await prisma.emailAttachment.create({
          data: {
            emailId: draft.id,
            filename: attachment.filename,
            originalName: attachment.originalName,
            mimeType: attachment.mimeType,
            size: attachment.size,
            path: attachment.path
          }
        })
      }
    }

    return { success: true, draftId: draft.id }
  } catch (error) {
    console.error('Save draft error:', error)
    return { success: false, error: 'Failed to save draft' }
  }
}
