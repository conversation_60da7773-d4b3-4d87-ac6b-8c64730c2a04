'use client'

import { useState, useEffect } from 'react'
import StudentLayout from '@/components/student/student-layout'
import { 
  Mail, 
  MailOpen, 
  Send, 
  Star, 
  Trash2, 
  HardDrive, 
  Calendar,
  TrendingUp,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock,
  Plus,
  Eye
} from 'lucide-react'
import Link from 'next/link'

interface DashboardStats {
  totalEmails: number
  unreadEmails: number
  sentEmails: number
  starredEmails: number
  storageUsed: number
  storageLimit: number
  todayEmails: number
}

interface RecentEmail {
  id: string
  subject: string
  fromEmail: string
  fromName?: string
  receivedAt: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  preview: string
}

interface PaymentInfo {
  id: string
  description: string
  amount: number
  dueDate: string
  status: 'PENDING' | 'PAID' | 'OVERDUE'
  paymentDate?: string
}

export default function StudentDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentEmails, setRecentEmails] = useState<RecentEmail[]>([])
  const [pendingPayments, setPendingPayments] = useState<PaymentInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('student_token')
      if (!token) return

      // Fetch dashboard statistics
      const [statsResponse, emailsResponse, paymentsResponse] = await Promise.all([
        fetch('/api/student/dashboard/stats', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/student/dashboard/recent-emails', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/student/dashboard/payments', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ])

      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData.statistics)
      }

      if (emailsResponse.ok) {
        const emailsData = await emailsResponse.json()
        setRecentEmails(emailsData.emails)
      }

      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json()
        setPendingPayments(paymentsData.payments)
      }

    } catch (error) {
      setError('Failed to load dashboard data')
      console.error('Dashboard error:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatStorageSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const getStoragePercentage = () => {
    if (!stats || stats.storageLimit === 0) return 0
    return (stats.storageUsed / stats.storageLimit) * 100
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return 'Today'
    } else if (diffDays === 2) {
      return 'Yesterday'
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'text-green-600 bg-green-100'
      case 'OVERDUE': return 'text-red-600 bg-red-100'
      default: return 'text-yellow-600 bg-yellow-100'
    }
  }

  if (loading) {
    return (
      <StudentLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading dashboard...</div>
        </div>
      </StudentLayout>
    )
  }

  return (
    <StudentLayout>
      <div>
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-sm text-gray-600">Welcome to your email portal</p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Link
                  href="/student/email/compose"
                  className="bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-4 text-center transition-colors"
                >
                  <Plus className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-sm font-medium text-blue-900">Compose Email</div>
                </Link>

                <Link
                  href="/student/email/inbox"
                  className="bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg p-4 text-center transition-colors"
                >
                  <MailOpen className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-sm font-medium text-green-900">Check Inbox</div>
                </Link>

                <Link
                  href="/student/payments"
                  className="bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg p-4 text-center transition-colors"
                >
                  <CreditCard className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-sm font-medium text-purple-900">Pay Fees</div>
                </Link>

                <Link
                  href="/student/settings"
                  className="bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 text-center transition-colors"
                >
                  <Eye className="h-8 w-8 text-gray-600 mx-auto mb-2" />
                  <div className="text-sm font-medium text-gray-900">Settings</div>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Mail className="h-6 w-6 text-blue-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Emails</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalEmails}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <MailOpen className="h-6 w-6 text-green-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Unread</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.unreadEmails}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Send className="h-6 w-6 text-purple-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Sent</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.sentEmails}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <HardDrive className="h-6 w-6 text-orange-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Storage</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {getStoragePercentage().toFixed(1)}%
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Emails */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Recent Emails</h2>
              <Link
                href="/student/email/inbox"
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                View all
              </Link>
            </div>
            <div className="divide-y divide-gray-200">
              {recentEmails.length > 0 ? (
                recentEmails.slice(0, 5).map((email) => (
                  <div key={email.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${email.isRead ? 'bg-gray-300' : 'bg-blue-500'}`} />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className={`text-sm truncate ${!email.isRead ? 'font-semibold text-gray-900' : 'text-gray-700'}`}>
                            {email.subject || '(No Subject)'}
                          </p>
                          <div className="flex items-center space-x-1">
                            {email.isStarred && <Star className="h-4 w-4 text-yellow-400 fill-current" />}
                            {email.hasAttachments && <div className="w-2 h-2 bg-gray-400 rounded-full" />}
                          </div>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                          From: {email.fromName ? `${email.fromName} <${email.fromEmail}>` : email.fromEmail}
                        </p>
                        <p className="text-sm text-gray-500 mt-1 truncate">
                          {email.preview}
                        </p>
                        <p className="text-xs text-gray-400 mt-2">
                          {formatDate(email.receivedAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-6 text-center text-gray-500">
                  <Mail className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p>No recent emails</p>
                </div>
              )}
            </div>
          </div>

          {/* Storage Usage & Pending Payments */}
          <div className="space-y-6">
            {/* Storage Usage */}
            {stats && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900">Storage Usage</h2>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm text-gray-600">
                      {formatStorageSize(stats.storageUsed)} of {formatStorageSize(stats.storageLimit)} used
                    </span>
                    <span className="text-sm font-medium text-gray-900">
                      {getStoragePercentage().toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className={`h-3 rounded-full ${
                        getStoragePercentage() > 80 ? 'bg-red-500' : 
                        getStoragePercentage() > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(getStoragePercentage(), 100)}%` }}
                    />
                  </div>
                  {getStoragePercentage() > 80 && (
                    <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-3">
                      <div className="flex">
                        <AlertCircle className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                          <p className="text-sm text-red-800">
                            Your storage is almost full. Consider deleting old emails or attachments.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Pending Payments */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-900">Pending Payments</h2>
                <Link
                  href="/student/payments"
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  View all
                </Link>
              </div>
              <div className="divide-y divide-gray-200">
                {pendingPayments.length > 0 ? (
                  pendingPayments.slice(0, 3).map((payment) => (
                    <div key={payment.id} className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {payment.description}
                          </p>
                          <p className="text-sm text-gray-500 mt-1">
                            Due: {new Date(payment.dueDate).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">
                            ₹{payment.amount.toLocaleString()}
                          </p>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.status)}`}>
                            {payment.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-6 text-center text-gray-500">
                    <CheckCircle className="mx-auto h-12 w-12 text-green-400 mb-4" />
                    <p>No pending payments</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </StudentLayout>
  )
}
