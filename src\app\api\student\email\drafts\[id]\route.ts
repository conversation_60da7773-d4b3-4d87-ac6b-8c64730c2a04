import { NextRequest, NextResponse } from 'next/server'
import { createS<PERSON>ure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/email/drafts/[id] - Get specific draft
export const GET = createSecureApi(
  async (context, { params }: { params: { id: string } }) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const draftId = params.id

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Get drafts folder
      const draftsFolder = await prisma.emailFolder.findFirst({
        where: {
          accountId: account.id,
          folderType: 'DRAFTS'
        }
      })

      if (!draftsFolder) {
        return NextResponse.json(
          { error: 'Drafts folder not found' },
          { status: 404 }
        )
      }

      // Get the specific draft
      const draft = await prisma.email.findFirst({
        where: {
          id: draftId,
          fromAccountId: account.id,
          recipients: {
            some: {
              accountId: account.id,
              folderId: draftsFolder.id
            }
          }
        },
        include: {
          attachments: {
            select: {
              id: true,
              filename: true,
              originalName: true,
              size: true,
              mimeType: true,
              path: true
            }
          }
        }
      })

      if (!draft) {
        return NextResponse.json(
          { error: 'Draft not found' },
          { status: 404 }
        )
      }

      // Format draft
      const formattedDraft = {
        id: draft.id,
        subject: draft.subject,
        body: draft.body,
        bodyText: draft.bodyText,
        toEmails: draft.toEmails || [],
        ccEmails: draft.ccEmails || [],
        bccEmails: draft.bccEmails || [],
        priority: draft.priority,
        attachments: draft.attachments,
        createdAt: draft.createdAt,
        updatedAt: draft.updatedAt
      }

      return NextResponse.json({
        success: true,
        draft: formattedDraft
      })

    } catch (error) {
      console.error('Student draft get error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve draft' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)

// PUT /api/student/email/drafts/[id] - Update specific draft
export const PUT = createSecureApi(
  async (context, { params }: { params: { id: string } }) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const draftId = params.id

      // Get request body
      const body = await context.request.json()
      const {
        to = [],
        cc = [],
        bcc = [],
        subject = '',
        body: emailBody = '',
        priority = 'NORMAL'
      } = body

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Get drafts folder
      const draftsFolder = await prisma.emailFolder.findFirst({
        where: {
          accountId: account.id,
          folderType: 'DRAFTS'
        }
      })

      if (!draftsFolder) {
        return NextResponse.json(
          { error: 'Drafts folder not found' },
          { status: 404 }
        )
      }

      // Verify draft exists and belongs to student
      const existingDraft = await prisma.email.findFirst({
        where: {
          id: draftId,
          fromAccountId: account.id,
          recipients: {
            some: {
              accountId: account.id,
              folderId: draftsFolder.id
            }
          }
        }
      })

      if (!existingDraft) {
        return NextResponse.json(
          { error: 'Draft not found' },
          { status: 404 }
        )
      }

      // Update the draft
      const updatedDraft = await prisma.email.update({
        where: { id: draftId },
        data: {
          subject: subject.trim(),
          body: emailBody,
          bodyText: emailBody.replace(/<[^>]*>/g, ''), // Strip HTML for text version
          toEmails: to.map((email: string) => email.trim()).filter(Boolean),
          ccEmails: cc.map((email: string) => email.trim()).filter(Boolean),
          bccEmails: bcc.map((email: string) => email.trim()).filter(Boolean),
          priority: priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT',
          updatedAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Draft updated successfully',
        draftId: updatedDraft.id
      })

    } catch (error) {
      console.error('Student draft update error:', error)
      return NextResponse.json(
        { error: 'Failed to update draft' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)

// DELETE /api/student/email/drafts/[id] - Delete specific draft
export const DELETE = createSecureApi(
  async (context, { params }: { params: { id: string } }) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const draftId = params.id

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Get drafts folder
      const draftsFolder = await prisma.emailFolder.findFirst({
        where: {
          accountId: account.id,
          folderType: 'DRAFTS'
        }
      })

      if (!draftsFolder) {
        return NextResponse.json(
          { error: 'Drafts folder not found' },
          { status: 404 }
        )
      }

      // Verify draft exists and belongs to student
      const existingDraft = await prisma.email.findFirst({
        where: {
          id: draftId,
          fromAccountId: account.id,
          recipients: {
            some: {
              accountId: account.id,
              folderId: draftsFolder.id
            }
          }
        }
      })

      if (!existingDraft) {
        return NextResponse.json(
          { error: 'Draft not found' },
          { status: 404 }
        )
      }

      // Delete the draft (soft delete by marking as deleted)
      await prisma.email.update({
        where: { id: draftId },
        data: {
          isDeleted: true,
          updatedAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Draft deleted successfully'
      })

    } catch (error) {
      console.error('Student draft delete error:', error)
      return NextResponse.json(
        { error: 'Failed to delete draft' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true
  }
)
