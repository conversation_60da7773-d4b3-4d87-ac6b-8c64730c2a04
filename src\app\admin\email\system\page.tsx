'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import AdminLayout from '@/components/admin/admin-layout'
import { 
  Activity, 
  Server, 
  Database, 
  Mail, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Clock,
  HardDrive,
  Zap,
  TrendingUp,
  Set<PERSON>s,
  Play,
  Trash2
} from 'lucide-react'

interface ExtendedUser {
  role: string
  id: string
  email: string
  name?: string | null
}

interface SystemHealth {
  status: string
  timestamp: string
  version: string
  environment: string
  healthChecks: {
    database: boolean
    smtp: boolean
    storage: boolean
    queue: boolean
  }
  statistics: {
    accounts: {
      total: number
      active: number
      inactive: number
    }
    emails: {
      total: number
      today: number
      averagePerDay: number
    }
    queue: {
      pending: number
      processing: number
      sent: number
      failed: number
      cancelled: number
    }
    storage: {
      totalUsed: number
      totalLimit: number
      averageUsed: number
      usagePercentage: number
    }
  }
  errors: string[]
  warnings: string[]
  uptime: number
  memory: {
    used: number
    total: number
    external: number
  }
}

interface QueueStatus {
  statistics: {
    pending: number
    processing: number
    sent: number
    failed: number
    cancelled: number
    total: number
  }
  recentItems: Array<{
    id: string
    recipientEmail: string
    status: string
    attempts: number
    priority: number
    scheduledAt: string
    processedAt?: string
    lastError?: string
    email: {
      subject: string
      fromEmail: string
      messageId: string
    }
  }>
  failedItems: Array<{
    id: string
    recipientEmail: string
    attempts: number
    lastError: string
    updatedAt: string
    email: {
      subject: string
      fromEmail: string
      messageId: string
    }
  }>
}

export default function EmailSystemPage() {
  const { data: session, status } = useSession()
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null)
  const [queueStatus, setQueueStatus] = useState<QueueStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [processingQueue, setProcessingQueue] = useState(false)

  useEffect(() => {
    if (status === 'unauthenticated') {
      redirect('/admin/login')
    }
  }, [status])

  useEffect(() => {
    if (session) {
      fetchSystemHealth()
      fetchQueueStatus()
      
      // Set up auto-refresh every 30 seconds
      const interval = setInterval(() => {
        fetchSystemHealth()
        fetchQueueStatus()
      }, 30000)

      return () => clearInterval(interval)
    }
  }, [session])

  const fetchSystemHealth = async () => {
    try {
      const response = await fetch('/api/email/status')
      if (!response.ok) {
        throw new Error('Failed to fetch system health')
      }

      const data = await response.json()
      setSystemHealth(data)
    } catch (error) {
      setError('Failed to load system health')
      console.error('Error fetching system health:', error)
    }
  }

  const fetchQueueStatus = async () => {
    try {
      const response = await fetch('/api/email/queue/process')
      if (!response.ok) {
        throw new Error('Failed to fetch queue status')
      }

      const data = await response.json()
      setQueueStatus(data)
    } catch (error) {
      console.error('Error fetching queue status:', error)
    } finally {
      setLoading(false)
    }
  }

  const processQueue = async () => {
    try {
      setProcessingQueue(true)
      const response = await fetch('/api/email/queue/process', {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to process queue')
      }

      await fetchQueueStatus()
    } catch (error) {
      setError('Failed to process email queue')
      console.error('Error processing queue:', error)
    } finally {
      setProcessingQueue(false)
    }
  }

  const clearFailedItems = async () => {
    if (!confirm('Are you sure you want to clear all failed queue items?')) {
      return
    }

    try {
      const response = await fetch('/api/email/queue/process?type=failed', {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to clear failed items')
      }

      await fetchQueueStatus()
    } catch (error) {
      setError('Failed to clear failed items')
      console.error('Error clearing failed items:', error)
    }
  }

  const runMaintenance = async (action: string) => {
    try {
      const response = await fetch('/api/email/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      })

      if (!response.ok) {
        throw new Error('Failed to run maintenance task')
      }

      await fetchSystemHealth()
      await fetchQueueStatus()
    } catch (error) {
      setError('Failed to run maintenance task')
      console.error('Error running maintenance:', error)
    }
  }

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100'
      case 'unhealthy': return 'text-red-600 bg-red-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getHealthIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    )
  }

  if (status === 'loading' || loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading...</div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || (session.user as ExtendedUser)?.role !== 'ADMIN') {
    redirect('/admin/login')
  }

  return (
    <AdminLayout>
      <div>
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Email System Monitoring</h1>
            <p className="text-sm text-gray-600">Monitor system health, performance, and email queue status</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => {
                fetchSystemHealth()
                fetchQueueStatus()
              }}
              className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium inline-flex items-center hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {/* System Health Overview */}
        {systemHealth && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Overall Status */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Activity className="h-8 w-8 text-blue-500" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">System Status</dt>
                      <dd className="flex items-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(systemHealth.status)}`}>
                          {systemHealth.status.toUpperCase()}
                        </span>
                        <span className="ml-2 text-sm text-gray-500">
                          Uptime: {formatUptime(systemHealth.uptime)}
                        </span>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Memory Usage */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Zap className="h-8 w-8 text-purple-500" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Memory Usage</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {systemHealth.memory.used} MB / {systemHealth.memory.total} MB
                      </dd>
                      <dd className="text-sm text-gray-500">
                        {Math.round((systemHealth.memory.used / systemHealth.memory.total) * 100)}% used
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Storage Usage */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <HardDrive className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Storage Usage</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatBytes(systemHealth.statistics.storage.totalUsed)}
                      </dd>
                      <dd className="text-sm text-gray-500">
                        {systemHealth.statistics.storage.usagePercentage.toFixed(1)}% of {formatBytes(systemHealth.statistics.storage.totalLimit)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Health Checks */}
        {systemHealth && (
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Health Checks</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {getHealthIcon(systemHealth.healthChecks.database)}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">Database</p>
                    <p className="text-sm text-gray-500">
                      {systemHealth.healthChecks.database ? 'Connected' : 'Disconnected'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {getHealthIcon(systemHealth.healthChecks.smtp)}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">SMTP</p>
                    <p className="text-sm text-gray-500">
                      {systemHealth.healthChecks.smtp ? 'Available' : 'Unavailable'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {getHealthIcon(systemHealth.healthChecks.storage)}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">Storage</p>
                    <p className="text-sm text-gray-500">
                      {systemHealth.healthChecks.storage ? 'Available' : 'Unavailable'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {getHealthIcon(systemHealth.healthChecks.queue)}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">Queue</p>
                    <p className="text-sm text-gray-500">
                      {systemHealth.healthChecks.queue ? 'Operational' : 'Error'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Errors and Warnings */}
              {(systemHealth.errors.length > 0 || systemHealth.warnings.length > 0) && (
                <div className="mt-6 space-y-4">
                  {systemHealth.errors.length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <XCircle className="h-5 w-5 text-red-400" />
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-red-800">Errors</h3>
                          <div className="mt-2 text-sm text-red-700">
                            <ul className="list-disc pl-5 space-y-1">
                              {systemHealth.errors.map((error, index) => (
                                <li key={index}>{error}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {systemHealth.warnings.length > 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <AlertTriangle className="h-5 w-5 text-yellow-400" />
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-yellow-800">Warnings</h3>
                          <div className="mt-2 text-sm text-yellow-700">
                            <ul className="list-disc pl-5 space-y-1">
                              {systemHealth.warnings.map((warning, index) => (
                                <li key={index}>{warning}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Email Statistics */}
        {systemHealth && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Mail className="h-6 w-6 text-blue-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Emails</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {systemHealth.statistics.emails.total.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <TrendingUp className="h-6 w-6 text-green-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Today</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {systemHealth.statistics.emails.today.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Activity className="h-6 w-6 text-purple-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Daily Average</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {systemHealth.statistics.emails.averagePerDay.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Server className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Active Accounts</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {systemHealth.statistics.accounts.active.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Email Queue Status */}
        {queueStatus && (
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Email Queue Status</h2>
              <div className="flex space-x-2">
                <button
                  onClick={processQueue}
                  disabled={processingQueue}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium inline-flex items-center disabled:opacity-50"
                >
                  {processingQueue ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-1" />
                      Process Queue
                    </>
                  )}
                </button>
                {queueStatus.statistics.failed > 0 && (
                  <button
                    onClick={clearFailedItems}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium inline-flex items-center"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Clear Failed
                  </button>
                )}
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{queueStatus.statistics.pending}</div>
                  <div className="text-sm text-gray-500">Pending</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{queueStatus.statistics.processing}</div>
                  <div className="text-sm text-gray-500">Processing</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{queueStatus.statistics.sent}</div>
                  <div className="text-sm text-gray-500">Sent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{queueStatus.statistics.failed}</div>
                  <div className="text-sm text-gray-500">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{queueStatus.statistics.total}</div>
                  <div className="text-sm text-gray-500">Total</div>
                </div>
              </div>

              {/* Recent Queue Items */}
              {queueStatus.recentItems.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Recent Queue Items</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Recipient
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Subject
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Attempts
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Scheduled
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {queueStatus.recentItems.slice(0, 5).map((item) => (
                          <tr key={item.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.recipientEmail}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.email.subject}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                item.status === 'SENT' ? 'bg-green-100 text-green-800' :
                                item.status === 'FAILED' ? 'bg-red-100 text-red-800' :
                                item.status === 'PROCESSING' ? 'bg-blue-100 text-blue-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {item.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.attempts}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(item.scheduledAt).toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Failed Items */}
              {queueStatus.failedItems.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Failed Items Requiring Attention</h3>
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="space-y-3">
                      {queueStatus.failedItems.slice(0, 3).map((item) => (
                        <div key={item.id} className="flex justify-between items-start">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-red-900">
                              {item.email.subject} → {item.recipientEmail}
                            </p>
                            <p className="text-sm text-red-700 mt-1">
                              Error: {item.lastError}
                            </p>
                            <p className="text-xs text-red-600 mt-1">
                              {item.attempts} attempts • Last updated: {new Date(item.updatedAt).toLocaleString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Maintenance Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Maintenance Actions
            </h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <button
                onClick={() => runMaintenance('cleanup_old_sessions')}
                className="bg-blue-50 hover:bg-blue-100 border border-blue-200 text-blue-700 px-4 py-3 rounded-md text-sm font-medium text-center"
              >
                <Clock className="h-5 w-5 mx-auto mb-1" />
                Cleanup Sessions
              </button>

              <button
                onClick={() => runMaintenance('cleanup_old_queue_items')}
                className="bg-green-50 hover:bg-green-100 border border-green-200 text-green-700 px-4 py-3 rounded-md text-sm font-medium text-center"
              >
                <Trash2 className="h-5 w-5 mx-auto mb-1" />
                Cleanup Queue
              </button>

              <button
                onClick={() => runMaintenance('update_storage_usage')}
                className="bg-purple-50 hover:bg-purple-100 border border-purple-200 text-purple-700 px-4 py-3 rounded-md text-sm font-medium text-center"
              >
                <HardDrive className="h-5 w-5 mx-auto mb-1" />
                Update Storage
              </button>

              <button
                onClick={() => runMaintenance('process_queue')}
                className="bg-orange-50 hover:bg-orange-100 border border-orange-200 text-orange-700 px-4 py-3 rounded-md text-sm font-medium text-center"
              >
                <RefreshCw className="h-5 w-5 mx-auto mb-1" />
                Process Queue
              </button>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
