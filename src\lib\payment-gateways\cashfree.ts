import crypto from 'crypto'

interface CashfreeConfig {
  appId: string
  secretKey: string
  isTestMode: boolean
}

interface CashfreePaymentRequest {
  transactionId: string
  amount: number
  studentEmail: string
  studentName: string
  studentPhone?: string
  description: string
  returnUrl: string
  notifyUrl: string
}

interface CashfreePaymentResponse {
  paymentUrl: string
  transactionId: string
  cftoken: string
}

export class CashfreeGateway {
  private config: CashfreeConfig
  private baseUrl: string

  constructor(config: CashfreeConfig) {
    this.config = config
    this.baseUrl = config.isTestMode 
      ? 'https://test.cashfree.com/api/v2'
      : 'https://api.cashfree.com/api/v2'
  }

  /**
   * Create Cashfree payment session
   */
  async createPaymentRequest(request: CashfreePaymentRequest): Promise<CashfreePaymentResponse> {
    const {
      transactionId,
      amount,
      studentEmail,
      studentName,
      studentPhone,
      description,
      returnUrl,
      notifyUrl
    } = request

    // Create payment session payload
    const sessionPayload = {
      orderId: transactionId,
      orderAmount: amount,
      orderCurrency: 'INR',
      orderNote: description,
      customerName: studentName,
      customerEmail: studentEmail,
      customerPhone: studentPhone || '9999999999',
      returnUrl: returnUrl,
      notifyUrl: notifyUrl,
      paymentModes: 'cc,dc,nb,upi,paypal,wallet'
    }

    // Generate signature
    const signature = this.generateSignature(sessionPayload)

    // Create payment session
    const response = await fetch(`${this.baseUrl}/cftoken/order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': this.config.appId,
        'X-Client-Secret': this.config.secretKey
      },
      body: JSON.stringify({
        ...sessionPayload,
        signature
      })
    })

    const result = await response.json()

    if (result.status !== 'OK') {
      throw new Error(`Cashfree payment session creation failed: ${result.message}`)
    }

    // Generate payment URL
    const paymentUrl = this.config.isTestMode
      ? `https://test.cashfree.com/billpay/checkout/post/submit?order_id=${transactionId}&order_token=${result.cftoken}`
      : `https://www.cashfree.com/checkout/post/submit?order_id=${transactionId}&order_token=${result.cftoken}`

    return {
      paymentUrl,
      transactionId,
      cftoken: result.cftoken
    }
  }

  /**
   * Generate Cashfree signature
   */
  private generateSignature(payload: any): string {
    const signatureData = `${payload.orderId}${payload.orderAmount}${payload.orderCurrency}${this.config.secretKey}`
    return crypto.createHash('sha256').update(signatureData).digest('hex')
  }

  /**
   * Verify Cashfree signature
   */
  private verifySignature(orderId: string, orderAmount: string, referenceId: string, txStatus: string, paymentMode: string, txMsg: string, txTime: string, signature: string): boolean {
    const signatureData = `${orderId}${orderAmount}${referenceId}${txStatus}${paymentMode}${txMsg}${txTime}${this.config.secretKey}`
    const expectedSignature = crypto.createHash('sha256').update(signatureData).digest('hex')
    return signature === expectedSignature
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(transactionId: string) {
    const response = await fetch(`${this.baseUrl}/order/info/status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': this.config.appId,
        'X-Client-Secret': this.config.secretKey
      },
      body: JSON.stringify({
        orderId: transactionId
      })
    })

    const result = await response.json()
    return result
  }

  /**
   * Process Cashfree callback
   */
  async processCallback(callbackData: Record<string, string>) {
    const {
      orderId: transactionId,
      orderAmount,
      referenceId,
      txStatus,
      paymentMode,
      txMsg,
      txTime,
      signature
    } = callbackData

    // Verify signature
    const isValid = this.verifySignature(
      transactionId,
      orderAmount,
      referenceId,
      txStatus,
      paymentMode,
      txMsg,
      txTime,
      signature
    )

    if (!isValid) {
      throw new Error('Invalid Cashfree callback signature')
    }

    return {
      transactionId,
      status: this.mapCashfreeStatus(txStatus),
      amount: parseFloat(orderAmount),
      gatewayTransactionId: referenceId,
      paymentMode,
      message: txMsg,
      transactionTime: txTime,
      isSuccess: txStatus === 'SUCCESS',
      rawResponse: callbackData
    }
  }

  /**
   * Map Cashfree status to our internal status
   */
  private mapCashfreeStatus(cashfreeStatus: string): string {
    switch (cashfreeStatus) {
      case 'SUCCESS':
        return 'PAID'
      case 'FAILED':
        return 'FAILED'
      case 'PENDING':
        return 'PROCESSING'
      case 'CANCELLED':
        return 'CANCELLED'
      default:
        return 'FAILED'
    }
  }

  /**
   * Initiate refund
   */
  async initiateRefund(transactionId: string, refundAmount: number, refundNote?: string) {
    const refundPayload = {
      orderId: transactionId,
      refundAmount: refundAmount,
      refundNote: refundNote || 'Refund initiated'
    }

    const response = await fetch(`${this.baseUrl}/order/refund`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': this.config.appId,
        'X-Client-Secret': this.config.secretKey
      },
      body: JSON.stringify(refundPayload)
    })

    const result = await response.json()
    return result
  }

  /**
   * Get refund status
   */
  async getRefundStatus(transactionId: string, refundId: string) {
    const response = await fetch(`${this.baseUrl}/order/info/refund/status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': this.config.appId,
        'X-Client-Secret': this.config.secretKey
      },
      body: JSON.stringify({
        orderId: transactionId,
        refundId: refundId
      })
    })

    const result = await response.json()
    return result
  }

  /**
   * Get settlement details
   */
  async getSettlementDetails(transactionId: string) {
    const response = await fetch(`${this.baseUrl}/order/info/settlements`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': this.config.appId,
        'X-Client-Secret': this.config.secretKey
      },
      body: JSON.stringify({
        orderId: transactionId
      })
    })

    const result = await response.json()
    return result
  }
}

/**
 * Factory function to create Cashfree gateway instance
 */
export function createCashfreeGateway(): CashfreeGateway {
  const config: CashfreeConfig = {
    appId: process.env.CASHFREE_APP_ID || '',
    secretKey: process.env.CASHFREE_SECRET_KEY || '',
    isTestMode: process.env.CASHFREE_TEST_MODE === 'true'
  }

  if (!config.appId || !config.secretKey) {
    throw new Error('Cashfree configuration is incomplete. Please check environment variables.')
  }

  return new CashfreeGateway(config)
}
