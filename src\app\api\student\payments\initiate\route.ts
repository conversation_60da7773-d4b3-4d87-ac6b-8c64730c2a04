import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON>pi } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'
import { getPaymentGatewayManager, PaymentGatewayType } from '@/lib/payment-gateways'

// POST /api/student/payments/initiate - Initiate payment for student
export const POST = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const accountId = decoded.accountId
      const body = await context.request.json()
      const { paymentId, gateway } = body

      if (!paymentId || !gateway) {
        return NextResponse.json(
          { error: 'Payment ID and gateway are required' },
          { status: 400 }
        )
      }

      // Get gateway configuration
      const gatewayConfig = await prisma.paymentGatewayConfig.findUnique({
        where: { 
          gateway: gateway as any,
          isEnabled: true 
        }
      })

      if (!gatewayConfig) {
        return NextResponse.json(
          { error: 'Payment gateway not available' },
          { status: 400 }
        )
      }

      // Get student account details
      const student = await prisma.emailAccount.findUnique({
        where: { id: accountId },
        select: {
          email: true,
          displayName: true,
          studentId: true
        }
      })

      if (!student) {
        return NextResponse.json(
          { error: 'Student not found' },
          { status: 404 }
        )
      }

      // For demo purposes, we'll simulate payment initiation
      // In a real implementation, you would:
      // 1. Get the actual payment details from database
      // 2. Calculate total amount with gateway fees
      // 3. Create payment request with the gateway
      // 4. Return the gateway's redirect URL

      // Sample payment data (this would come from database)
      const samplePayments: Record<string, any> = {
        'pay_001': {
          description: 'Tuition Fee - Semester 1',
          amount: 50000,
          category: 'TUITION'
        },
        'pay_002': {
          description: 'Library Fee',
          amount: 2000,
          category: 'LIBRARY'
        },
        'pay_003': {
          description: 'Hostel Fee - Semester 1',
          amount: 25000,
          category: 'HOSTEL'
        },
        'pay_006': {
          description: 'Lab Fee - Computer Science',
          amount: 5000,
          category: 'OTHER'
        }
      }

      const payment = samplePayments[paymentId]
      if (!payment) {
        return NextResponse.json(
          { error: 'Payment not found' },
          { status: 404 }
        )
      }

      // Calculate total amount with gateway fees
      const baseAmount = payment.amount
      const percentageFee = (baseAmount * gatewayConfig.additionalFeePercent) / 100
      const totalAmount = baseAmount + percentageFee + gatewayConfig.additionalFeeFixed

      // Generate unique transaction ID
      const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Create payment record (in real implementation)
      // await prisma.paymentTransaction.create({
      //   data: {
      //     id: transactionId,
      //     studentId,
      //     paymentId,
      //     gateway: gatewayConfig.gateway,
      //     baseAmount,
      //     gatewayFee: percentageFee + gatewayConfig.additionalFeeFixed,
      //     totalAmount,
      //     status: 'INITIATED',
      //     // ... other fields
      //   }
      // })

      // Use real payment gateway integration
      const paymentGatewayManager = getPaymentGatewayManager()

      if (!paymentGatewayManager.isGatewayAvailable(gateway as PaymentGatewayType)) {
        return NextResponse.json(
          { error: 'Payment gateway is not available' },
          { status: 400 }
        )
      }

      const paymentRequest = {
        transactionId,
        amount: totalAmount,
        studentEmail: student.email,
        studentName: student.displayName || student.studentId,
        description: payment.description,
        successUrl: `${process.env.NEXTAUTH_URL}/student/payments/success?txnid=${transactionId}`,
        failureUrl: `${process.env.NEXTAUTH_URL}/student/payments/failure?txnid=${transactionId}`,
        callbackUrl: `${process.env.NEXTAUTH_URL}/api/payments/callback/${gateway.toLowerCase()}`
      }

      const paymentResponse = await paymentGatewayManager.createPaymentRequest(
        gateway as PaymentGatewayType,
        paymentRequest
      )

      // Create payment transaction record
      await prisma.paymentTransaction.create({
        data: {
          id: transactionId,
          studentId,
          paymentId,
          gateway: gatewayConfig.gateway,
          baseAmount,
          gatewayFee: percentageFee + gatewayConfig.additionalFeeFixed,
          totalAmount,
          status: 'INITIATED',
          description: payment.description,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        transactionId,
        redirectUrl: paymentResponse.paymentUrl,
        totalAmount,
        gateway: gatewayConfig.gateway,
        message: 'Payment initiated successfully'
      })

    } catch (error) {
      console.error('Payment initiation error:', error)
      return NextResponse.json(
        { error: 'Failed to initiate payment' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)


