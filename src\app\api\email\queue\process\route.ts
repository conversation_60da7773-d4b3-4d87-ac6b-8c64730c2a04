import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { processEmailQueue } from '@/lib/email-delivery'
import { prisma } from '@/lib/prisma'

// POST /api/email/queue/process - Process email queue (for cron jobs or manual processing)
export const POST = createSecureApi(
  async (context) => {
    try {
      // Check if this is an admin request or internal cron job
      const { searchParams } = new URL(context.request.url)
      const cronSecret = searchParams.get('secret')
      
      // Verify cron secret for automated processing
      if (cronSecret && cronSecret === process.env.CRON_SECRET) {
        // This is an automated cron job request
        console.log('Processing email queue via cron job')
      } else if (!context.user || context.user.role !== 'ADMIN') {
        // Manual processing requires admin access
        return NextResponse.json(
          { error: 'Admin access required' },
          { status: 403 }
        )
      }

      // Get queue statistics before processing
      const queueStats = await prisma.emailQueue.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      })

      const beforeStats = queueStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status
        return acc
      }, {} as Record<string, number>)

      // Process the email queue
      await processEmailQueue()

      // Get queue statistics after processing
      const afterQueueStats = await prisma.emailQueue.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      })

      const afterStats = afterQueueStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status
        return acc
      }, {} as Record<string, number>)

      // Calculate processed counts
      const processed = {
        pending: (beforeStats.PENDING || 0) - (afterStats.PENDING || 0),
        sent: (afterStats.SENT || 0) - (beforeStats.SENT || 0),
        failed: (afterStats.FAILED || 0) - (beforeStats.FAILED || 0)
      }

      return NextResponse.json({
        success: true,
        message: 'Email queue processed successfully',
        statistics: {
          before: beforeStats,
          after: afterStats,
          processed
        }
      })

    } catch (error) {
      console.error('Email queue processing error:', error)
      return NextResponse.json(
        { error: 'Failed to process email queue' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // Allow cron jobs with secret
    logAudit: true
  }
)

// GET /api/email/queue/process - Get queue status
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get queue statistics
      const queueStats = await prisma.emailQueue.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      })

      const stats = queueStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status
        return acc
      }, {} as Record<string, number>)

      // Get recent queue items
      const recentItems = await prisma.emailQueue.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          email: {
            select: {
              subject: true,
              fromEmail: true,
              messageId: true
            }
          }
        }
      })

      // Get failed items that need attention
      const failedItems = await prisma.emailQueue.findMany({
        where: {
          status: 'FAILED',
          attempts: { gte: 3 }
        },
        take: 5,
        orderBy: { updatedAt: 'desc' },
        include: {
          email: {
            select: {
              subject: true,
              fromEmail: true,
              messageId: true
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        statistics: {
          pending: stats.PENDING || 0,
          processing: stats.PROCESSING || 0,
          sent: stats.SENT || 0,
          failed: stats.FAILED || 0,
          cancelled: stats.CANCELLED || 0,
          total: Object.values(stats).reduce((sum, count) => sum + count, 0)
        },
        recentItems: recentItems.map(item => ({
          id: item.id,
          recipientEmail: item.recipientEmail,
          status: item.status,
          attempts: item.attempts,
          priority: item.priority,
          scheduledAt: item.scheduledAt,
          processedAt: item.processedAt,
          lastError: item.lastError,
          email: {
            subject: item.email.subject,
            fromEmail: item.email.fromEmail,
            messageId: item.email.messageId
          }
        })),
        failedItems: failedItems.map(item => ({
          id: item.id,
          recipientEmail: item.recipientEmail,
          attempts: item.attempts,
          lastError: item.lastError,
          updatedAt: item.updatedAt,
          email: {
            subject: item.email.subject,
            fromEmail: item.email.fromEmail,
            messageId: item.email.messageId
          }
        }))
      })

    } catch (error) {
      console.error('Email queue status error:', error)
      return NextResponse.json(
        { error: 'Failed to get queue status' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: false
  }
)

// DELETE /api/email/queue/process - Clear failed queue items
export const DELETE = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const clearType = searchParams.get('type') || 'failed'

      let whereClause: any = {}

      switch (clearType) {
        case 'failed':
          whereClause = { status: 'FAILED' }
          break
        case 'cancelled':
          whereClause = { status: 'CANCELLED' }
          break
        case 'old':
          // Clear items older than 7 days
          const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          whereClause = {
            status: { in: ['SENT', 'FAILED', 'CANCELLED'] },
            updatedAt: { lt: sevenDaysAgo }
          }
          break
        default:
          return NextResponse.json(
            { error: 'Invalid clear type. Use: failed, cancelled, or old' },
            { status: 400 }
          )
      }

      // Count items to be deleted
      const countToDelete = await prisma.emailQueue.count({
        where: whereClause
      })

      // Delete items
      const result = await prisma.emailQueue.deleteMany({
        where: whereClause
      })

      return NextResponse.json({
        success: true,
        message: `Cleared ${result.count} queue items`,
        deletedCount: result.count,
        type: clearType
      })

    } catch (error) {
      console.error('Email queue clear error:', error)
      return NextResponse.json(
        { error: 'Failed to clear queue items' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: true
  }
)
