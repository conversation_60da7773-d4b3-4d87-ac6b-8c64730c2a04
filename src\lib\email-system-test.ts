import { prisma } from './prisma'
import { sendEmail, saveDraft } from './email-server'
import { createDefaultFolders, getFolderByType } from './email-folder-setup'

/**
 * Comprehensive email system test
 */
export async function testEmailSystem() {
  const testResults = {
    folderCreation: false,
    draftSaving: false,
    emailSending: false,
    emailRetrieval: false,
    folderManagement: false,
    attachmentHandling: false,
    errors: [] as string[]
  }

  try {
    console.log('🧪 Starting Email System Test...')

    // Test 1: Folder Creation
    console.log('📁 Testing folder creation...')
    try {
      // Find a test account or create one
      let testAccount = await prisma.emailAccount.findFirst({
        where: { accountType: 'STUDENT_ID', isActive: true }
      })

      if (!testAccount) {
        // Create a test account
        testAccount = await prisma.emailAccount.create({
          data: {
            email: '<EMAIL>',
            password: 'hashed_password',
            displayName: 'Test Student',
            accountType: 'STUDENT_ID',
            studentId: 'TEST001',
            rollNumber: 'TEST001',
            course: 'Test Course',
            batch: '2024',
            createdById: 'system'
          }
        })
      }

      await createDefaultFolders(testAccount.id)
      
      // Verify folders were created
      const folders = await prisma.emailFolder.findMany({
        where: { accountId: testAccount.id }
      })

      if (folders.length >= 6) {
        testResults.folderCreation = true
        console.log('✅ Folder creation test passed')
      } else {
        testResults.errors.push('Insufficient folders created')
      }
    } catch (error) {
      testResults.errors.push(`Folder creation failed: ${error}`)
    }

    // Test 2: Draft Saving
    console.log('💾 Testing draft saving...')
    try {
      const testAccount = await prisma.emailAccount.findFirst({
        where: { accountType: 'STUDENT_ID', isActive: true }
      })

      if (testAccount) {
        const draftData = {
          subject: 'Test Draft',
          body: '<p>This is a test draft</p>',
          bodyText: 'This is a test draft',
          fromEmail: testAccount.email,
          fromName: testAccount.displayName || 'Test User',
          toEmails: ['<EMAIL>'],
          ccEmails: [],
          bccEmails: [],
          priority: 'NORMAL' as const,
          attachments: []
        }

        const result = await saveDraft(testAccount.id, draftData)
        
        if (result.success) {
          testResults.draftSaving = true
          console.log('✅ Draft saving test passed')
        } else {
          testResults.errors.push(`Draft saving failed: ${result.error}`)
        }
      }
    } catch (error) {
      testResults.errors.push(`Draft saving test failed: ${error}`)
    }

    // Test 3: Email Sending
    console.log('📧 Testing email sending...')
    try {
      const testAccount = await prisma.emailAccount.findFirst({
        where: { accountType: 'STUDENT_ID', isActive: true }
      })

      if (testAccount) {
        const emailData = {
          subject: 'Test Email',
          body: '<p>This is a test email</p>',
          bodyText: 'This is a test email',
          fromEmail: testAccount.email,
          fromName: testAccount.displayName || 'Test User',
          toEmails: ['<EMAIL>'],
          ccEmails: [],
          bccEmails: [],
          priority: 'NORMAL' as const,
          attachments: []
        }

        const result = await sendEmail(testAccount.id, emailData)
        
        if (result.success) {
          testResults.emailSending = true
          console.log('✅ Email sending test passed')
        } else {
          testResults.errors.push(`Email sending failed: ${result.error}`)
        }
      }
    } catch (error) {
      testResults.errors.push(`Email sending test failed: ${error}`)
    }

    // Test 4: Email Retrieval
    console.log('📬 Testing email retrieval...')
    try {
      const testAccount = await prisma.emailAccount.findFirst({
        where: { accountType: 'STUDENT_ID', isActive: true }
      })

      if (testAccount) {
        const emails = await prisma.emailRecipient.findMany({
          where: {
            accountId: testAccount.id,
            isDeleted: false
          },
          include: {
            email: true,
            folder: true
          },
          take: 5
        })

        testResults.emailRetrieval = true
        console.log('✅ Email retrieval test passed')
      }
    } catch (error) {
      testResults.errors.push(`Email retrieval test failed: ${error}`)
    }

    // Test 5: Folder Management
    console.log('📂 Testing folder management...')
    try {
      const testAccount = await prisma.emailAccount.findFirst({
        where: { accountType: 'STUDENT_ID', isActive: true }
      })

      if (testAccount) {
        const inboxFolder = await getFolderByType(testAccount.id, 'INBOX')
        const sentFolder = await getFolderByType(testAccount.id, 'SENT')
        
        if (inboxFolder && sentFolder) {
          testResults.folderManagement = true
          console.log('✅ Folder management test passed')
        } else {
          testResults.errors.push('Failed to retrieve folders by type')
        }
      }
    } catch (error) {
      testResults.errors.push(`Folder management test failed: ${error}`)
    }

    // Test 6: Database Integrity
    console.log('🔍 Testing database integrity...')
    try {
      // Check for orphaned records
      const orphanedRecipients = await prisma.emailRecipient.count({
        where: {
          email: null
        }
      })

      const orphanedAttachments = await prisma.emailAttachment.count({
        where: {
          email: null
        }
      })

      if (orphanedRecipients === 0 && orphanedAttachments === 0) {
        console.log('✅ Database integrity test passed')
      } else {
        testResults.errors.push(`Found ${orphanedRecipients} orphaned recipients and ${orphanedAttachments} orphaned attachments`)
      }
    } catch (error) {
      testResults.errors.push(`Database integrity test failed: ${error}`)
    }

    console.log('🎯 Email System Test Complete!')
    console.log('Results:', testResults)

    return testResults

  } catch (error) {
    console.error('❌ Email system test failed:', error)
    testResults.errors.push(`System test failed: ${error}`)
    return testResults
  }
}

/**
 * Test database connection and basic operations
 */
export async function testDatabaseConnection() {
  try {
    console.log('🔌 Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful')

    // Test basic query
    const userCount = await prisma.user.count()
    console.log(`📊 Found ${userCount} users in database`)

    // Test email account query
    const emailAccountCount = await prisma.emailAccount.count()
    console.log(`📧 Found ${emailAccountCount} email accounts in database`)

    // Test folder query
    const folderCount = await prisma.emailFolder.count()
    console.log(`📁 Found ${folderCount} email folders in database`)

    return true
  } catch (error) {
    console.error('❌ Database connection test failed:', error)
    return false
  } finally {
    await prisma.$disconnect()
  }
}
