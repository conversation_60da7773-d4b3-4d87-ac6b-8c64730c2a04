#!/usr/bin/env node

/**
 * System Verification Script
 * Verifies critical system components without starting the full server
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Starting System Verification...\n');

// Test 1: Environment Configuration
console.log('📋 Testing Environment Configuration...');
try {
  const envExists = fs.existsSync('.env');
  if (envExists) {
    console.log('✅ Environment file exists');
    const envContent = fs.readFileSync('.env', 'utf8');
    const requiredVars = [
      'DATABASE_URL',
      'NEXTAUTH_SECRET',
      'JWT_SECRET',
      'EMAIL_DOMAIN'
    ];
    
    let missingVars = [];
    requiredVars.forEach(varName => {
      if (!envContent.includes(varName)) {
        missingVars.push(varName);
      }
    });
    
    if (missingVars.length === 0) {
      console.log('✅ All required environment variables present');
    } else {
      console.log('⚠️  Missing environment variables:', missingVars.join(', '));
    }
  } else {
    console.log('❌ Environment file missing');
  }
} catch (error) {
  console.log('❌ Environment check failed:', error.message);
}

// Test 2: Critical Files Verification
console.log('\n📁 Testing Critical Files...');
const criticalFiles = [
  'package.json',
  'next.config.ts',
  'tsconfig.json',
  'prisma/schema.prisma',
  'src/app/layout.tsx',
  'src/lib/prisma.ts',
  'src/lib/auth.ts',
  'src/lib/email-server.ts'
];

let missingFiles = [];
criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file}`);
    missingFiles.push(file);
  }
});

if (missingFiles.length === 0) {
  console.log('✅ All critical files present');
} else {
  console.log('❌ Missing critical files:', missingFiles.length);
}

// Test 3: Student Portal Pages
console.log('\n🎓 Testing Student Portal Pages...');
const studentPages = [
  'src/app/student/login/page.tsx',
  'src/app/student/dashboard/page.tsx',
  'src/app/student/email/inbox/page.tsx',
  'src/app/student/email/compose/page.tsx',
  'src/app/student/email/sent/page.tsx',
  'src/app/student/email/drafts/page.tsx',
  'src/app/student/email/starred/page.tsx',
  'src/app/student/email/archive/page.tsx',
  'src/app/student/email/trash/page.tsx',
  'src/app/student/email/message/[id]/page.tsx',
  'src/app/student/payments/page.tsx',
  'src/app/student/settings/page.tsx'
];

let missingStudentPages = [];
studentPages.forEach(page => {
  if (fs.existsSync(page)) {
    console.log(`✅ ${page.split('/').pop()}`);
  } else {
    console.log(`❌ ${page.split('/').pop()}`);
    missingStudentPages.push(page);
  }
});

if (missingStudentPages.length === 0) {
  console.log('✅ All student portal pages present');
} else {
  console.log('❌ Missing student pages:', missingStudentPages.length);
}

// Test 4: Admin Portal Pages
console.log('\n👨‍💼 Testing Admin Portal Pages...');
const adminPages = [
  'src/app/admin/page.tsx',
  'src/app/admin/login/page.tsx',
  'src/app/admin/users/page.tsx',
  'src/app/admin/email/accounts/page.tsx',
  'src/app/admin/email/oversight/page.tsx',
  'src/app/admin/email/system/page.tsx',
  'src/app/admin/payments/page.tsx',
  'src/app/admin/pages/page.tsx',
  'src/app/admin/navigation/page.tsx',
  'src/app/admin/settings/page.tsx'
];

let missingAdminPages = [];
adminPages.forEach(page => {
  if (fs.existsSync(page)) {
    console.log(`✅ ${page.split('/').pop()}`);
  } else {
    console.log(`❌ ${page.split('/').pop()}`);
    missingAdminPages.push(page);
  }
});

if (missingAdminPages.length === 0) {
  console.log('✅ All admin portal pages present');
} else {
  console.log('❌ Missing admin pages:', missingAdminPages.length);
}

// Test 5: API Endpoints
console.log('\n📡 Testing API Endpoints...');
const apiEndpoints = [
  'src/app/api/auth/[...nextauth]/route.ts',
  'src/app/api/auth/student/route.ts',
  'src/app/api/student/email/send/route.ts',
  'src/app/api/student/email/messages/route.ts',
  'src/app/api/student/email/drafts/route.ts',
  'src/app/api/student/email/message/[id]/route.ts',
  'src/app/api/student/email/attachments/[id]/route.ts',
  'src/app/api/student/payments/route.ts',
  'src/app/api/admin/email/accounts/route.ts',
  'src/app/api/payments/callback/payu/route.ts'
];

let missingEndpoints = [];
apiEndpoints.forEach(endpoint => {
  if (fs.existsSync(endpoint)) {
    console.log(`✅ ${endpoint.split('/').slice(-2).join('/')}`);
  } else {
    console.log(`❌ ${endpoint.split('/').slice(-2).join('/')}`);
    missingEndpoints.push(endpoint);
  }
});

if (missingEndpoints.length === 0) {
  console.log('✅ All critical API endpoints present');
} else {
  console.log('❌ Missing API endpoints:', missingEndpoints.length);
}

// Test 6: UI Components
console.log('\n🎨 Testing UI Components...');
const uiComponents = [
  'src/components/ui/button.tsx',
  'src/components/ui/input.tsx',
  'src/components/ui/card.tsx',
  'src/components/ui/checkbox.tsx',
  'src/components/ui/badge.tsx',
  'src/components/ui/dropdown-menu.tsx',
  'src/components/ui/alert-dialog.tsx',
  'src/components/ui/separator.tsx'
];

let missingComponents = [];
uiComponents.forEach(component => {
  if (fs.existsSync(component)) {
    console.log(`✅ ${component.split('/').pop()}`);
  } else {
    console.log(`❌ ${component.split('/').pop()}`);
    missingComponents.push(component);
  }
});

if (missingComponents.length === 0) {
  console.log('✅ All UI components present');
} else {
  console.log('❌ Missing UI components:', missingComponents.length);
}

// Test 7: Payment Gateways
console.log('\n💳 Testing Payment Gateway Integration...');
const paymentFiles = [
  'src/lib/payment-gateways/index.ts',
  'src/lib/payment-gateways/payu.ts',
  'src/lib/payment-gateways/phonepe.ts',
  'src/lib/payment-gateways/cashfree.ts'
];

let missingPaymentFiles = [];
paymentFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file.split('/').pop()}`);
  } else {
    console.log(`❌ ${file.split('/').pop()}`);
    missingPaymentFiles.push(file);
  }
});

if (missingPaymentFiles.length === 0) {
  console.log('✅ All payment gateways implemented');
} else {
  console.log('❌ Missing payment files:', missingPaymentFiles.length);
}

// Final Summary
console.log('\n🎯 VERIFICATION SUMMARY');
console.log('========================');

const totalIssues = missingFiles.length + missingStudentPages.length + 
                   missingAdminPages.length + missingEndpoints.length + 
                   missingComponents.length + missingPaymentFiles.length;

if (totalIssues === 0) {
  console.log('🎉 ALL SYSTEMS VERIFIED - READY FOR DEPLOYMENT!');
  console.log('✅ Environment: Configured');
  console.log('✅ Student Portal: Complete');
  console.log('✅ Admin Portal: Complete');
  console.log('✅ API Endpoints: All present');
  console.log('✅ UI Components: All implemented');
  console.log('✅ Payment Gateways: All integrated');
  console.log('\n🚀 System is production-ready!');
} else {
  console.log(`⚠️  Found ${totalIssues} issues that need attention:`);
  if (missingFiles.length > 0) console.log(`   - ${missingFiles.length} missing critical files`);
  if (missingStudentPages.length > 0) console.log(`   - ${missingStudentPages.length} missing student pages`);
  if (missingAdminPages.length > 0) console.log(`   - ${missingAdminPages.length} missing admin pages`);
  if (missingEndpoints.length > 0) console.log(`   - ${missingEndpoints.length} missing API endpoints`);
  if (missingComponents.length > 0) console.log(`   - ${missingComponents.length} missing UI components`);
  if (missingPaymentFiles.length > 0) console.log(`   - ${missingPaymentFiles.length} missing payment files`);
}

console.log('\n✨ Verification Complete!');
