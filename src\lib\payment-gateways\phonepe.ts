import crypto from 'crypto'

interface PhonePeConfig {
  merchantId: string
  apiKey: string
  saltKey: string
  saltIndex: number
  isTestMode: boolean
}

interface PhonePePaymentRequest {
  transactionId: string
  amount: number
  studentEmail: string
  studentName: string
  studentPhone?: string
  description: string
  callbackUrl: string
  redirectUrl: string
}

interface PhonePePaymentResponse {
  paymentUrl: string
  transactionId: string
  checksum: string
}

export class PhonePeGateway {
  private config: PhonePeConfig
  private baseUrl: string

  constructor(config: PhonePeConfig) {
    this.config = config
    this.baseUrl = config.isTestMode 
      ? 'https://api-preprod.phonepe.com/apis/pg-sandbox'
      : 'https://api.phonepe.com/apis/hermes'
  }

  /**
   * Create PhonePe payment request
   */
  async createPaymentRequest(request: PhonePePaymentRequest): Promise<PhonePePaymentResponse> {
    const {
      transactionId,
      amount,
      studentEmail,
      studentName,
      studentPhone,
      description,
      callbackUrl,
      redirectUrl
    } = request

    // PhonePe requires amount in paisa (multiply by 100)
    const amountInPaisa = Math.round(amount * 100)

    // Create payment payload
    const paymentPayload = {
      merchantId: this.config.merchantId,
      merchantTransactionId: transactionId,
      merchantUserId: `USER_${transactionId}`,
      amount: amountInPaisa,
      redirectUrl: redirectUrl,
      redirectMode: 'POST',
      callbackUrl: callbackUrl,
      mobileNumber: studentPhone,
      paymentInstrument: {
        type: 'PAY_PAGE'
      }
    }

    // Encode payload to base64
    const base64Payload = Buffer.from(JSON.stringify(paymentPayload)).toString('base64')

    // Generate checksum
    const checksumString = `${base64Payload}/pg/v1/pay${this.config.saltKey}`
    const checksum = crypto.createHash('sha256').update(checksumString).digest('hex') + '###' + this.config.saltIndex

    // Make API request to PhonePe
    const response = await fetch(`${this.baseUrl}/pg/v1/pay`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum
      },
      body: JSON.stringify({
        request: base64Payload
      })
    })

    const result = await response.json()

    if (!result.success) {
      throw new Error(`PhonePe payment initiation failed: ${result.message}`)
    }

    return {
      paymentUrl: result.data.instrumentResponse.redirectInfo.url,
      transactionId,
      checksum
    }
  }

  /**
   * Check payment status
   */
  async checkPaymentStatus(transactionId: string) {
    const statusUrl = `/pg/v1/status/${this.config.merchantId}/${transactionId}`
    const checksumString = `${statusUrl}${this.config.saltKey}`
    const checksum = crypto.createHash('sha256').update(checksumString).digest('hex') + '###' + this.config.saltIndex

    const response = await fetch(`${this.baseUrl}${statusUrl}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
        'X-MERCHANT-ID': this.config.merchantId
      }
    })

    const result = await response.json()
    return result
  }

  /**
   * Verify PhonePe callback
   */
  verifyCallback(callbackData: any, receivedChecksum: string): boolean {
    const base64Response = callbackData.response
    const checksumString = `${base64Response}${this.config.saltKey}`
    const expectedChecksum = crypto.createHash('sha256').update(checksumString).digest('hex') + '###' + this.config.saltIndex

    return receivedChecksum === expectedChecksum
  }

  /**
   * Process PhonePe callback
   */
  async processCallback(callbackData: any, receivedChecksum: string) {
    const isValid = this.verifyCallback(callbackData, receivedChecksum)
    
    if (!isValid) {
      throw new Error('Invalid PhonePe callback checksum')
    }

    // Decode the response
    const decodedResponse = JSON.parse(Buffer.from(callbackData.response, 'base64').toString())

    const {
      merchantTransactionId: transactionId,
      transactionId: gatewayTransactionId,
      amount,
      state,
      responseCode,
      paymentInstrument
    } = decodedResponse

    return {
      transactionId,
      status: this.mapPhonePeStatus(state),
      amount: amount / 100, // Convert back from paisa
      gatewayTransactionId,
      responseCode,
      paymentMethod: paymentInstrument?.type,
      isSuccess: state === 'COMPLETED',
      rawResponse: decodedResponse
    }
  }

  /**
   * Map PhonePe status to our internal status
   */
  private mapPhonePeStatus(phonePeState: string): string {
    switch (phonePeState) {
      case 'COMPLETED':
        return 'PAID'
      case 'FAILED':
        return 'FAILED'
      case 'PENDING':
        return 'PROCESSING'
      default:
        return 'FAILED'
    }
  }

  /**
   * Initiate refund
   */
  async initiateRefund(originalTransactionId: string, refundTransactionId: string, amount: number, reason?: string) {
    const amountInPaisa = Math.round(amount * 100)

    const refundPayload = {
      merchantId: this.config.merchantId,
      merchantTransactionId: refundTransactionId,
      originalTransactionId: originalTransactionId,
      amount: amountInPaisa,
      callbackUrl: `${process.env.NEXTAUTH_URL}/api/payments/phonepe/refund-callback`
    }

    const base64Payload = Buffer.from(JSON.stringify(refundPayload)).toString('base64')
    const checksumString = `${base64Payload}/pg/v1/refund${this.config.saltKey}`
    const checksum = crypto.createHash('sha256').update(checksumString).digest('hex') + '###' + this.config.saltIndex

    const response = await fetch(`${this.baseUrl}/pg/v1/refund`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum
      },
      body: JSON.stringify({
        request: base64Payload
      })
    })

    const result = await response.json()
    return result
  }

  /**
   * Check refund status
   */
  async checkRefundStatus(refundTransactionId: string) {
    const statusUrl = `/pg/v1/refund/status/${this.config.merchantId}/${refundTransactionId}`
    const checksumString = `${statusUrl}${this.config.saltKey}`
    const checksum = crypto.createHash('sha256').update(checksumString).digest('hex') + '###' + this.config.saltIndex

    const response = await fetch(`${this.baseUrl}${statusUrl}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
        'X-MERCHANT-ID': this.config.merchantId
      }
    })

    const result = await response.json()
    return result
  }
}

/**
 * Factory function to create PhonePe gateway instance
 */
export function createPhonePeGateway(): PhonePeGateway {
  const config: PhonePeConfig = {
    merchantId: process.env.PHONEPE_MERCHANT_ID || '',
    apiKey: process.env.PHONEPE_API_KEY || '',
    saltKey: process.env.PHONEPE_SALT_KEY || '',
    saltIndex: parseInt(process.env.PHONEPE_SALT_INDEX || '1'),
    isTestMode: process.env.PHONEPE_TEST_MODE === 'true'
  }

  if (!config.merchantId || !config.apiKey || !config.saltKey) {
    throw new Error('PhonePe configuration is incomplete. Please check environment variables.')
  }

  return new PhonePeGateway(config)
}
