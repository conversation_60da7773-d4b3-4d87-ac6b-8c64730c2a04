# 🔧 DATABASE CONFIGURATION TROUBLESHOOTING GUIDE

## 🚨 **ISSUE: P1001 - Can't reach database server at localhost:5432**

This error indicates that <PERSON><PERSON><PERSON> is trying to connect to PostgreSQL instead of SQLite. Here's how to fix it:

---

## 🔍 **QUICK DIAGNOSIS**

Run the diagnostic script to identify the exact issue:
```bash
node scripts/diagnose-database.js
```

---

## 🛠️ **AUTOMATIC FIX (Recommended)**

Run the automatic fix script:
```bash
node scripts/fix-database-config.js
```

Then reset the database:
```bash
# Windows
scripts\reset-database.bat

# Unix/Linux/macOS
chmod +x scripts/reset-database.sh
./scripts/reset-database.sh
```

---

## 🔧 **MANUAL FIX STEPS**

### **Step 1: Fix Prisma Schema**

Edit `prisma/schema.prisma` and change:

**FROM:**
```prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}
```

**TO:**
```prisma
datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}
```

### **Step 2: Fix Environment Variables**

Edit `.env` file and ensure:

**Correct SQLite Configuration:**
```env
DATABASE_URL="file:./dev.db"
```

**Remove PostgreSQL-specific variables:**
- Remove any `DIRECT_URL` entries
- Ensure `DATABASE_URL` starts with `file:`

### **Step 3: Fix PostgreSQL-specific Types**

In `prisma/schema.prisma`, change:
```prisma
gatewayResponse Json?
```

To:
```prisma
gatewayResponse String? // JSON stored as string for SQLite compatibility
```

### **Step 4: Clear Cache and Regenerate**

```bash
# Remove Prisma cache
rm -rf node_modules/.prisma

# Remove old database files
rm -f dev.db

# Regenerate Prisma client
npx prisma generate

# Create database schema
npx prisma db push --force-reset

# Seed database (optional)
npx prisma db seed
```

---

## 🧪 **VERIFICATION STEPS**

### **1. Check Configuration**
```bash
node scripts/diagnose-database.js
```

### **2. Test Database Connection**
```bash
npx prisma db push --force-reset
```

### **3. Verify Database File**
Check that `dev.db` file is created in the root directory.

### **4. Test Application**
```bash
npm run dev
```

Visit: http://localhost:3000/api/system/health

---

## 🔍 **COMMON ISSUES AND SOLUTIONS**

### **Issue: "Environment variable not found: DATABASE_URL"**
**Solution:**
1. Ensure `.env` file exists in root directory
2. Check `.env` file has `DATABASE_URL="file:./dev.db"`
3. Restart the development server

### **Issue: "Prisma Client not found"**
**Solution:**
```bash
npx prisma generate
```

### **Issue: "Database file not created"**
**Solution:**
```bash
npx prisma db push --force-reset
```

### **Issue: "Still connecting to PostgreSQL"**
**Solution:**
1. Clear browser cache
2. Restart development server
3. Check for multiple `.env` files
4. Verify no environment variables are overridden in scripts

### **Issue: "Permission denied on database file"**
**Solution:**
```bash
# Windows
del dev.db

# Unix/Linux/macOS
rm dev.db
chmod 755 .
npx prisma db push --force-reset
```

---

## 📊 **VERIFICATION CHECKLIST**

After applying fixes, verify:

- [ ] **Prisma Schema**: Provider is "sqlite", no directUrl
- [ ] **Environment**: DATABASE_URL starts with "file:"
- [ ] **Database File**: `dev.db` exists in root directory
- [ ] **Prisma Client**: Generated for SQLite (run `npx prisma generate`)
- [ ] **Application**: Starts without database connection errors
- [ ] **Health Check**: `/api/system/health` returns healthy database status

---

## 🎯 **SUCCESS INDICATORS**

### **Correct Startup Output:**
```
✅ Environment file found
✅ Prisma client generated successfully
✅ Database schema created successfully
✅ Dependencies verified
✅ Server starting on http://localhost:3000

Ready - started server on 0.0.0.0:3000
```

### **Correct Health Check Response:**
```json
{
  "overall": "healthy",
  "services": [
    {"service": "database", "status": "healthy"}
  ]
}
```

### **Database File Created:**
- File `dev.db` exists in root directory
- File size > 0 bytes
- No connection errors in console

---

## 🆘 **EMERGENCY RESET**

If all else fails, perform a complete reset:

```bash
# 1. Stop development server (Ctrl+C)

# 2. Remove all database-related files
rm -f dev.db
rm -rf node_modules/.prisma
rm -rf prisma/migrations

# 3. Fix configuration (run automatic fix)
node scripts/fix-database-config.js

# 4. Complete database reset
# Windows: scripts\reset-database.bat
# Unix/Linux: ./scripts/reset-database.sh

# 5. Start development server
npm run dev
```

---

## 📞 **ADDITIONAL SUPPORT**

If issues persist:

1. **Check Node.js Version**: Ensure Node.js 18+ is installed
2. **Check npm Version**: Ensure npm is up to date
3. **Check File Permissions**: Ensure write permissions in project directory
4. **Check Disk Space**: Ensure sufficient disk space for database file
5. **Check Antivirus**: Ensure antivirus isn't blocking file creation

---

## ✅ **FINAL VERIFICATION**

Once fixed, the system should:
- Start without database connection errors
- Create `dev.db` file in root directory
- Respond to health checks with "healthy" database status
- Allow student and admin portal access
- Support all email functionality

**🎉 Database configuration should now be working correctly!**
