import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/slideshow - Get slideshow images
export async function GET(request: NextRequest) {
  try {
    // Get slideshow configuration from settings
    const slideshowSetting = await prisma.setting.findUnique({
      where: {
        key: 'homepage_slideshow'
      }
    })

    let slideshowImages = []

    if (slideshowSetting) {
      try {
        const imageIds = JSON.parse(slideshowSetting.value)
        
        // Get the actual media files
        const mediaFiles = await prisma.media.findMany({
          where: {
            id: {
              in: imageIds
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        })

        slideshowImages = mediaFiles.map(media => ({
          id: media.id,
          url: media.url,
          alt: media.alt || media.originalName,
          caption: media.caption
        }))
      } catch (error) {
        console.error('Error parsing slideshow setting:', error)
      }
    }

    // If no slideshow images configured, return default images
    if (slideshowImages.length === 0) {
      slideshowImages = [
        {
          id: 'default-1',
          url: '/images/slideshow1.jpg',
          alt: 'Institute Building',
          caption: 'S.N. Private Industrial Training Institute'
        },
        {
          id: 'default-2',
          url: '/images/slideshow2.jpg',
          alt: 'Workshop Area',
          caption: 'Modern Workshop Facilities'
        },
        {
          id: 'default-3',
          url: '/images/slideshow3.jpg',
          alt: 'Students in Training',
          caption: 'Hands-on Technical Training'
        }
      ]
    }

    return NextResponse.json({ images: slideshowImages })
  } catch (error) {
    console.error('Error fetching slideshow:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/slideshow - Update slideshow images (admin only)
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { imageIds } = await request.json()

    if (!Array.isArray(imageIds)) {
      return NextResponse.json({ error: 'imageIds must be an array' }, { status: 400 })
    }

    // Verify all image IDs exist
    const existingMedia = await prisma.media.findMany({
      where: {
        id: {
          in: imageIds
        }
      }
    })

    if (existingMedia.length !== imageIds.length) {
      return NextResponse.json({ error: 'Some image IDs do not exist' }, { status: 400 })
    }

    // Update or create slideshow setting
    await prisma.setting.upsert({
      where: {
        key: 'homepage_slideshow'
      },
      update: {
        value: JSON.stringify(imageIds)
      },
      create: {
        key: 'homepage_slideshow',
        value: JSON.stringify(imageIds),
        type: 'JSON'
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating slideshow:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
