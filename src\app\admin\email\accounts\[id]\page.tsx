'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import AdminLayout from '@/components/admin/admin-layout'
import { ArrowLeft, Save, User, Building, Mail, Settings, Activity, HardDrive } from 'lucide-react'
import Link from 'next/link'

interface EmailAccount {
  id: string
  email: string
  displayName?: string
  accountType: 'STUDENT_ID' | 'INSTITUTE_ID'
  isActive: boolean
  studentId?: string
  rollNumber?: string
  course?: string
  batch?: string
  department?: string
  designation?: string
  storageUsed: number
  storageLimit: number
  imapEnabled: boolean
  pop3Enabled: boolean
  smtpEnabled: boolean
  createdAt: string
  updatedAt: string
  createdBy: {
    name?: string
    email: string
  }
  folders: Array<{
    id: string
    name: string
    folderType: string
    isSystem: boolean
    order: number
  }>
  statistics: {
    totalEmails: number
    unreadEmails: number
    readEmails: number
    storageUsedPercent: number
  }
  recentActivity: Array<{
    subject: string
    fromEmail: string
    receivedAt: string
    isRead: boolean
  }>
}

interface FormData {
  displayName: string
  isActive: boolean
  rollNumber: string
  course: string
  batch: string
  department: string
  designation: string
  storageLimit: number
  imapEnabled: boolean
  pop3Enabled: boolean
  smtpEnabled: boolean
  password?: string
  confirmPassword?: string
}

export default function EditEmailAccountPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { data: session } = useSession()
  const [account, setAccount] = useState<EmailAccount | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [formData, setFormData] = useState<FormData>({
    displayName: '',
    isActive: true,
    rollNumber: '',
    course: '',
    batch: '',
    department: '',
    designation: '',
    storageLimit: **********,
    imapEnabled: true,
    pop3Enabled: true,
    smtpEnabled: true
  })

  useEffect(() => {
    if (session) {
      fetchAccount()
    }
  }, [session, params.id])

  const fetchAccount = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/email/accounts/${params.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch account')
      }

      const data = await response.json()
      setAccount(data.account)
      
      // Populate form data
      setFormData({
        displayName: data.account.displayName || '',
        isActive: data.account.isActive,
        rollNumber: data.account.rollNumber || '',
        course: data.account.course || '',
        batch: data.account.batch || '',
        department: data.account.department || '',
        designation: data.account.designation || '',
        storageLimit: data.account.storageLimit,
        imapEnabled: data.account.imapEnabled,
        pop3Enabled: data.account.pop3Enabled,
        smtpEnabled: data.account.smtpEnabled
      })
    } catch (error) {
      setError('Failed to load account')
      console.error('Error fetching account:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              type === 'number' ? parseInt(value) || 0 : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setSuccess('')

    // Validation
    if (formData.password && formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return
    }

    if (formData.password && formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return
    }

    try {
      setSaving(true)

      const updateData = { ...formData }
      if (!updateData.password) {
        delete updateData.password
        delete updateData.confirmPassword
      }

      const response = await fetch(`/api/email/accounts/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update account')
      }

      setSuccess('Account updated successfully')
      await fetchAccount() // Refresh account data
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update account')
    } finally {
      setSaving(false)
    }
  }

  const formatStorageSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading...</div>
        </div>
      </AdminLayout>
    )
  }

  if (!account) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">Account not found</h3>
          <p className="text-gray-500">The email account you're looking for doesn't exist.</p>
          <Link
            href="/admin/email/accounts"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Back to Accounts
          </Link>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Link
              href="/admin/email/accounts"
              className="text-gray-400 hover:text-gray-600"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Edit Email Account</h1>
          </div>
          <p className="text-sm text-gray-600">
            Manage settings for {account.email}
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
            {success}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Account Statistics */}
          <div className="lg:col-span-1 space-y-6">
            {/* Account Info Card */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 flex items-center">
                  {account.accountType === 'STUDENT_ID' ? (
                    <User className="h-5 w-5 mr-2 text-blue-600" />
                  ) : (
                    <Building className="h-5 w-5 mr-2 text-green-600" />
                  )}
                  Account Information
                </h2>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Email Address</dt>
                  <dd className="text-sm text-gray-900">{account.email}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Account Type</dt>
                  <dd className="text-sm text-gray-900">
                    {account.accountType === 'STUDENT_ID' ? 'Student Account' : 'Institute Account'}
                  </dd>
                </div>
                {account.studentId && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Student ID</dt>
                    <dd className="text-sm text-gray-900">{account.studentId}</dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="text-sm text-gray-900">
                    {new Date(account.createdAt).toLocaleDateString()}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Created By</dt>
                  <dd className="text-sm text-gray-900">
                    {account.createdBy.name || account.createdBy.email}
                  </dd>
                </div>
              </div>
            </div>

            {/* Statistics Card */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Statistics
                </h2>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Total Emails</span>
                  <span className="text-sm font-medium text-gray-900">{account.statistics.totalEmails}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Unread</span>
                  <span className="text-sm font-medium text-gray-900">{account.statistics.unreadEmails}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Storage Used</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatStorageSize(account.storageUsed)}
                  </span>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-500">Storage Usage</span>
                    <span className="text-gray-900">{account.statistics.storageUsedPercent.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        account.statistics.storageUsedPercent > 80 
                          ? 'bg-red-500' 
                          : account.statistics.storageUsedPercent > 60 
                          ? 'bg-yellow-500' 
                          : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(account.statistics.storageUsedPercent, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
              </div>
              <div className="p-6">
                {account.recentActivity.length > 0 ? (
                  <div className="space-y-3">
                    {account.recentActivity.map((activity, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className={`w-2 h-2 rounded-full mt-2 ${activity.isRead ? 'bg-gray-300' : 'bg-blue-500'}`} />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-gray-900 truncate">{activity.subject}</p>
                          <p className="text-xs text-gray-500">From: {activity.fromEmail}</p>
                          <p className="text-xs text-gray-500">{new Date(activity.receivedAt).toLocaleDateString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No recent activity</p>
                )}
              </div>
            </div>
          </div>

          {/* Edit Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900 flex items-center">
                    <Mail className="h-5 w-5 mr-2" />
                    Basic Information
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="displayName" className="block text-sm font-medium text-gray-700">
                        Display Name
                      </label>
                      <input
                        type="text"
                        id="displayName"
                        name="displayName"
                        value={formData.displayName}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Full Name"
                      />
                    </div>

                    <div className="flex items-center">
                      <input
                        id="isActive"
                        name="isActive"
                        type="checkbox"
                        checked={formData.isActive}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                        Account Active
                      </label>
                    </div>

                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        New Password (optional)
                      </label>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        value={formData.password || ''}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Leave blank to keep current password"
                      />
                    </div>

                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                        Confirm New Password
                      </label>
                      <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword || ''}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Confirm new password"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Account-Specific Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900">
                    {account.accountType === 'STUDENT_ID' ? 'Student Information' : 'Institute Information'}
                  </h2>
                </div>
                <div className="p-6">
                  {account.accountType === 'STUDENT_ID' ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="rollNumber" className="block text-sm font-medium text-gray-700">
                          Roll Number
                        </label>
                        <input
                          type="text"
                          id="rollNumber"
                          name="rollNumber"
                          value={formData.rollNumber}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="e.g., *********"
                        />
                      </div>

                      <div>
                        <label htmlFor="course" className="block text-sm font-medium text-gray-700">
                          Course
                        </label>
                        <input
                          type="text"
                          id="course"
                          name="course"
                          value={formData.course}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="e.g., Computer Science"
                        />
                      </div>

                      <div>
                        <label htmlFor="batch" className="block text-sm font-medium text-gray-700">
                          Batch
                        </label>
                        <input
                          type="text"
                          id="batch"
                          name="batch"
                          value={formData.batch}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="e.g., 2024-2028"
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="department" className="block text-sm font-medium text-gray-700">
                          Department
                        </label>
                        <input
                          type="text"
                          id="department"
                          name="department"
                          value={formData.department}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="e.g., Computer Science"
                        />
                      </div>

                      <div>
                        <label htmlFor="designation" className="block text-sm font-medium text-gray-700">
                          Designation
                        </label>
                        <input
                          type="text"
                          id="designation"
                          name="designation"
                          value={formData.designation}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="e.g., Professor, Assistant Professor"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Account Settings */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900 flex items-center">
                    <Settings className="h-5 w-5 mr-2" />
                    Account Settings
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="storageLimit" className="block text-sm font-medium text-gray-700">
                        Storage Limit
                      </label>
                      <select
                        id="storageLimit"
                        name="storageLimit"
                        value={formData.storageLimit}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value={*********}>512 MB</option>
                        <option value={**********}>1 GB</option>
                        <option value={**********}>2 GB</option>
                        <option value={*********0}>5 GB</option>
                      </select>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          id="imapEnabled"
                          name="imapEnabled"
                          type="checkbox"
                          checked={formData.imapEnabled}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="imapEnabled" className="ml-2 block text-sm text-gray-900">
                          Enable IMAP Access
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          id="pop3Enabled"
                          name="pop3Enabled"
                          type="checkbox"
                          checked={formData.pop3Enabled}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="pop3Enabled" className="ml-2 block text-sm text-gray-900">
                          Enable POP3 Access
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          id="smtpEnabled"
                          name="smtpEnabled"
                          type="checkbox"
                          checked={formData.smtpEnabled}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="smtpEnabled" className="ml-2 block text-sm text-gray-900">
                          Enable SMTP Access
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3">
                <Link
                  href="/admin/email/accounts"
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={saving}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
