import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/profile - Get student profile
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const accountId = decoded.accountId

      // Get student account details
      const student = await prisma.emailAccount.findUnique({
        where: { id: accountId },
        select: {
          id: true,
          email: true,
          displayName: true,
          studentId: true,
          rollNumber: true,
          course: true,
          batch: true,
          storageUsed: true,
          storageLimit: true,
          imapEnabled: true,
          pop3Enabled: true,
          smtpEnabled: true,
          isActive: true,
          createdAt: true
        }
      })

      if (!student) {
        return NextResponse.json(
          { error: 'Student not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        student
      })

    } catch (error) {
      console.error('Student profile error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve profile' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)

// PATCH /api/student/profile - Update student profile
export const PATCH = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const accountId = decoded.accountId
      const body = await context.request.json()

      // Validate and filter allowed fields
      const allowedFields = ['displayName', 'rollNumber', 'course', 'batch']
      const updateData: any = {}

      for (const field of allowedFields) {
        if (body[field] !== undefined) {
          updateData[field] = body[field]
        }
      }

      if (Object.keys(updateData).length === 0) {
        return NextResponse.json(
          { error: 'No valid fields to update' },
          { status: 400 }
        )
      }

      // Update the student profile
      const updatedStudent = await prisma.emailAccount.update({
        where: { id: accountId },
        data: updateData,
        select: {
          id: true,
          email: true,
          displayName: true,
          studentId: true,
          rollNumber: true,
          course: true,
          batch: true,
          storageUsed: true,
          storageLimit: true,
          imapEnabled: true,
          pop3Enabled: true,
          smtpEnabled: true,
          isActive: true,
          updatedAt: true
        }
      })

      return NextResponse.json({
        success: true,
        student: updatedStudent,
        message: 'Profile updated successfully'
      })

    } catch (error) {
      console.error('Student profile update error:', error)
      return NextResponse.json(
        { error: 'Failed to update profile' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)
