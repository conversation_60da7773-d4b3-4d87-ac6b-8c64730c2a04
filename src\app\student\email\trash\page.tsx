'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import StudentLayout from '@/components/student/student-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Search, 
  Trash2, 
  Star, 
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  RotateCcw,
  X
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

interface TrashedEmail {
  id: string
  subject: string
  fromEmail: string
  fromName: string
  body: string
  priority: string
  isRead: boolean
  isStarred: boolean
  sentAt: string
  attachments: any[]
}

export default function TrashPage() {
  const router = useRouter()
  const [emails, setEmails] = useState<TrashedEmail[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)

  const fetchTrashedEmails = async (page = 1, search = '') => {
    try {
      setLoading(true)
      const token = localStorage.getItem('studentToken')
      
      if (!token) {
        router.push('/student/login')
        return
      }

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        folder: 'trash'
      })

      if (search) {
        params.append('search', search)
      }

      const response = await fetch(`/api/student/email/messages?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setEmails(data.emails || [])
        setTotalPages(data.pagination?.totalPages || 1)
        setTotalCount(data.pagination?.totalCount || 0)
      } else if (response.status === 401) {
        localStorage.removeItem('studentToken')
        router.push('/student/login')
      } else {
        console.error('Failed to fetch trashed emails')
      }
    } catch (error) {
      console.error('Error fetching trashed emails:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTrashedEmails(currentPage, searchTerm)
  }, [currentPage])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchTrashedEmails(1, searchTerm)
  }

  const handleEmailClick = (emailId: string) => {
    router.push(`/student/email/message/${emailId}`)
  }

  const handleSelectEmail = (emailId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmails([...selectedEmails, emailId])
    } else {
      setSelectedEmails(selectedEmails.filter(id => id !== emailId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEmails(emails.map(email => email.id))
    } else {
      setSelectedEmails([])
    }
  }

  const handleRestore = async (emailIds: string[]) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      // Get inbox folder ID first
      const foldersResponse = await fetch('/api/student/email/folders', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (foldersResponse.ok) {
        const foldersData = await foldersResponse.json()
        const inboxFolder = foldersData.folders.find((f: any) => f.folderType === 'INBOX')
        
        if (inboxFolder) {
          for (const emailId of emailIds) {
            await fetch(`/api/student/email/message/${emailId}`, {
              method: 'PATCH',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ folderId: inboxFolder.id })
            })
          }

          setSelectedEmails([])
          fetchTrashedEmails(currentPage, searchTerm)
        }
      }
    } catch (error) {
      console.error('Restore error:', error)
    }
  }

  const handlePermanentDelete = async (emailIds: string[]) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      for (const emailId of emailIds) {
        await fetch(`/api/student/email/message/${emailId}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ action: 'permanent_delete' })
        })
      }

      setSelectedEmails([])
      fetchTrashedEmails(currentPage, searchTerm)
    } catch (error) {
      console.error('Permanent delete error:', error)
    }
  }

  const handleEmptyTrash = async () => {
    try {
      const token = localStorage.getItem('studentToken')
      
      for (const email of emails) {
        await fetch(`/api/student/email/message/${email.id}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ action: 'permanent_delete' })
        })
      }

      fetchTrashedEmails(currentPage, searchTerm)
    } catch (error) {
      console.error('Empty trash error:', error)
    }
  }

  const handleToggleStar = async (emailId: string, isStarred: boolean) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      await fetch(`/api/student/email/message/${emailId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isStarred: !isStarred })
      })

      fetchTrashedEmails(currentPage, searchTerm)
    } catch (error) {
      console.error('Toggle star error:', error)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } else if (diffDays <= 7) {
      return date.toLocaleDateString('en-US', { weekday: 'short' })
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'LOW': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <StudentLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Trash</h1>
          <p className="text-sm text-gray-600">
            {totalCount} {totalCount === 1 ? 'email' : 'emails'} in trash
          </p>
        </div>
        <div className="flex items-center gap-2">
          {emails.length > 0 && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Empty Trash
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Empty Trash</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently delete all emails in trash. This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleEmptyTrash}>
                    Empty Trash
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
          <Button
            onClick={() => fetchTrashedEmails(currentPage, searchTerm)}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between gap-4">
        <form onSubmit={handleSearch} className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search trash..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </form>

        {selectedEmails.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {selectedEmails.length} selected
            </span>
            <Button
              onClick={() => handleRestore(selectedEmails)}
              variant="outline"
              size="sm"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Restore
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <X className="h-4 w-4 mr-2" />
                  Delete Forever
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Forever</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently delete the selected emails. This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={() => handlePermanentDelete(selectedEmails)}>
                    Delete Forever
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </div>

      {/* Email List */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={selectedEmails.length === emails.length && emails.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <CardTitle className="text-sm font-medium">
              Select All
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">Loading trash...</p>
            </div>
          ) : emails.length === 0 ? (
            <div className="p-8 text-center">
              <Trash2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Trash is empty</h3>
              <p className="text-gray-600">
                {searchTerm ? 'No emails in trash match your search.' : 'No emails in trash.'}
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {emails.map((email) => (
                <div
                  key={email.id}
                  className="flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer opacity-75"
                  onClick={() => handleEmailClick(email.id)}
                >
                  <Checkbox
                    checked={selectedEmails.includes(email.id)}
                    onCheckedChange={(checked) => handleSelectEmail(email.id, checked as boolean)}
                    onClick={(e) => e.stopPropagation()}
                  />
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`p-1 h-auto ${email.isStarred ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-400 hover:text-yellow-500'}`}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleToggleStar(email.id, email.isStarred)
                    }}
                  >
                    <Star className={`h-4 w-4 ${email.isStarred ? 'fill-current' : ''}`} />
                  </Button>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-gray-700 truncate">
                        {email.fromName || email.fromEmail}
                      </span>
                      {email.priority !== 'NORMAL' && (
                        <Badge variant="secondary" className={getPriorityColor(email.priority)}>
                          {email.priority}
                        </Badge>
                      )}
                      {email.attachments.length > 0 && (
                        <Badge variant="outline">
                          📎 {email.attachments.length}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-900 truncate mb-1">
                      {email.subject || '(No subject)'}
                    </p>
                    <p className="text-sm text-gray-600 truncate">
                      {email.body.replace(/<[^>]*>/g, '').substring(0, 100)}...
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">
                      {formatDate(email.sentAt)}
                    </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleRestore([email.id])}>
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Restore
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleStar(email.id, email.isStarred)}>
                          <Star className="h-4 w-4 mr-2" />
                          {email.isStarred ? 'Remove Star' : 'Add Star'}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handlePermanentDelete([email.id])}
                          className="text-red-600"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Delete Forever
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
      </div>
    </StudentLayout>
  )
}
