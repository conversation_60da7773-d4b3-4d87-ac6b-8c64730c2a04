import { NextRequest, NextResponse } from 'next/server'
import { createS<PERSON>ure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/dashboard/stats - Get dashboard statistics for student
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const accountId = decoded.accountId

      // Get email statistics for the student
      const [
        totalEmails,
        unreadEmails,
        sentEmails,
        starredEmails,
        todayEmails,
        account
      ] = await Promise.all([
        // Total emails received
        prisma.emailRecipient.count({
          where: {
            accountId,
            isDeleted: false
          }
        }),

        // Unread emails
        prisma.emailRecipient.count({
          where: {
            accountId,
            isDeleted: false,
            isRead: false
          }
        }),

        // Sent emails
        prisma.email.count({
          where: {
            fromAccountId: accountId,
            isDeleted: false
          }
        }),

        // Starred emails
        prisma.emailRecipient.count({
          where: {
            accountId,
            isDeleted: false,
            email: {
              isStarred: true
            }
          }
        }),

        // Emails received today
        prisma.emailRecipient.count({
          where: {
            accountId,
            isDeleted: false,
            email: {
              sentAt: {
                gte: new Date(new Date().setHours(0, 0, 0, 0))
              }
            }
          }
        }),

        // Account storage info
        prisma.emailAccount.findUnique({
          where: { id: accountId },
          select: {
            storageUsed: true,
            storageLimit: true
          }
        })
      ])

      return NextResponse.json({
        success: true,
        statistics: {
          totalEmails,
          unreadEmails,
          sentEmails,
          starredEmails,
          storageUsed: account?.storageUsed || 0,
          storageLimit: account?.storageLimit || **********,
          todayEmails
        }
      })

    } catch (error) {
      console.error('Student dashboard stats error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve dashboard statistics' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)
