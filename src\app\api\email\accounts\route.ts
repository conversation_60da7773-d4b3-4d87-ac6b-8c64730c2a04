import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { createDefaultFolders } from '@/lib/email-server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// GET /api/email/accounts - Get email accounts (admin only)
export const GET = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100)
      const search = searchParams.get('search')
      const accountType = searchParams.get('accountType')
      const isActive = searchParams.get('isActive')

      const skip = (page - 1) * limit

      // Build where clause
      const whereClause: any = {}

      if (search) {
        whereClause.OR = [
          { email: { contains: search, mode: 'insensitive' } },
          { displayName: { contains: search, mode: 'insensitive' } },
          { studentId: { contains: search, mode: 'insensitive' } },
          { department: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (accountType) {
        whereClause.accountType = accountType
      }

      if (isActive !== null) {
        whereClause.isActive = isActive === 'true'
      }

      // Get accounts
      const accounts = await prisma.emailAccount.findMany({
        where: whereClause,
        select: {
          id: true,
          email: true,
          displayName: true,
          accountType: true,
          isActive: true,
          studentId: true,
          rollNumber: true,
          course: true,
          batch: true,
          department: true,
          designation: true,
          storageUsed: true,
          storageLimit: true,
          imapEnabled: true,
          pop3Enabled: true,
          smtpEnabled: true,
          createdAt: true,
          updatedAt: true,
          createdBy: {
            select: {
              name: true,
              email: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      })

      // Get total count
      const totalCount = await prisma.emailAccount.count({
        where: whereClause
      })

      // Calculate storage usage statistics
      const storageStats = await prisma.emailAccount.aggregate({
        where: whereClause,
        _sum: {
          storageUsed: true,
          storageLimit: true
        },
        _avg: {
          storageUsed: true
        }
      })

      return NextResponse.json({
        success: true,
        accounts,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasMore: skip + accounts.length < totalCount
        },
        statistics: {
          totalStorageUsed: storageStats._sum.storageUsed || 0,
          totalStorageLimit: storageStats._sum.storageLimit || 0,
          averageStorageUsed: storageStats._avg.storageUsed || 0,
          accountCount: totalCount
        }
      })

    } catch (error) {
      console.error('Get email accounts error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve email accounts' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: false
  }
)

// POST /api/email/accounts - Create new email account (admin only)
export const POST = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      const {
        email,
        password,
        displayName,
        accountType,
        studentId,
        rollNumber,
        course,
        batch,
        department,
        designation,
        storageLimit,
        imapEnabled = true,
        pop3Enabled = true,
        smtpEnabled = true
      } = body

      // Validate required fields
      if (!email || !password || !accountType) {
        return NextResponse.json(
          { error: 'Email, password, and account type are required' },
          { status: 400 }
        )
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { error: 'Invalid email format' },
          { status: 400 }
        )
      }

      // Check if email already exists
      const existingAccount = await prisma.emailAccount.findUnique({
        where: { email }
      })

      if (existingAccount) {
        return NextResponse.json(
          { error: 'Email account already exists' },
          { status: 409 }
        )
      }

      // Validate account type specific fields
      if (accountType === 'STUDENT_ID') {
        if (!studentId) {
          return NextResponse.json(
            { error: 'Student ID is required for student accounts' },
            { status: 400 }
          )
        }

        // Check if student ID already exists
        const existingStudentId = await prisma.emailAccount.findUnique({
          where: { studentId }
        })

        if (existingStudentId) {
          return NextResponse.json(
            { error: 'Student ID already exists' },
            { status: 409 }
          )
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12)

      // Create email account
      const account = await prisma.emailAccount.create({
        data: {
          email,
          password: hashedPassword,
          displayName,
          accountType,
          studentId: accountType === 'STUDENT_ID' ? studentId : null,
          rollNumber: accountType === 'STUDENT_ID' ? rollNumber : null,
          course: accountType === 'STUDENT_ID' ? course : null,
          batch: accountType === 'STUDENT_ID' ? batch : null,
          department: accountType === 'INSTITUTE_ID' ? department : null,
          designation: accountType === 'INSTITUTE_ID' ? designation : null,
          storageLimit: storageLimit || **********, // 1GB default
          imapEnabled,
          pop3Enabled,
          smtpEnabled,
          createdById: context.user.id
        }
      })

      // Create default folders
      await createDefaultFolders(account.id)

      // Return created account (without password)
      const { password: _, ...accountData } = account

      return NextResponse.json({
        success: true,
        account: accountData,
        message: 'Email account created successfully'
      })

    } catch (error) {
      console.error('Create email account error:', error)
      return NextResponse.json(
        { error: 'Failed to create email account' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: true,
    sanitizeInput: true
  }
)
