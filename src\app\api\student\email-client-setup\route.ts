import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { createEmailClientCompatibilityService } from '@/lib/email-client-compatibility'
import jwt from 'jsonwebtoken'

// GET /api/student/email-client-setup - Get email client setup information
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const email = decoded.email

      const compatibilityService = createEmailClientCompatibilityService()
      
      // Get client compatibility information
      const clientInfo = await compatibilityService.getClientCompatibilityInfo(studentId)
      
      // Get setup instructions
      const setupInstructions = compatibilityService.generateSetupInstructions(email)
      
      // Check which protocols are enabled
      const protocolStatus = {
        imap: await compatibilityService.validateClientAccess(email, 'IMAP'),
        pop3: await compatibilityService.validateClientAccess(email, 'POP3'),
        smtp: await compatibilityService.validateClientAccess(email, 'SMTP')
      }

      return NextResponse.json({
        success: true,
        email,
        protocolStatus,
        serverSettings: clientInfo.outlook.manualConfig,
        autoConfigUrls: {
          outlook: clientInfo.outlook.autoconfig,
          thunderbird: clientInfo.thunderbird.autoconfig,
          appleMobileConfig: clientInfo.apple.mobileconfig
        },
        setupInstructions,
        quickSetupLinks: {
          outlook: `${process.env.NEXTAUTH_URL}/student/email-setup/outlook`,
          thunderbird: `${process.env.NEXTAUTH_URL}/student/email-setup/thunderbird`,
          apple: `${process.env.NEXTAUTH_URL}/student/email-setup/apple`,
          android: `${process.env.NEXTAUTH_URL}/student/email-setup/android`
        }
      })

    } catch (error) {
      console.error('Email client setup error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve setup information' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)
