import { NextRequest, NextResponse } from 'next/server'
import { createS<PERSON>ure<PERSON><PERSON> } from '@/lib/secure-api'
import { getFolders, createFolder } from '@/lib/email-retrieval'
import { prisma } from '@/lib/prisma'

// GET /api/email/imap/folders - Get all folders for an account
export const GET = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const accountId = searchParams.get('accountId')

      if (!accountId) {
        return NextResponse.json(
          { error: 'Account ID is required' },
          { status: 400 }
        )
      }

      // Verify account exists and user has access
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or inactive' },
          { status: 404 }
        )
      }

      // Get folders with counts
      const folders = await getFolders(accountId)

      // Format for IMAP-like response
      const imapFolders = folders.map(folder => ({
        id: folder.id,
        name: folder.name,
        fullName: folder.name, // In a real IMAP implementation, this would include hierarchy
        type: folder.folderType,
        attributes: [
          folder.isSystem ? '\\System' : '\\Custom',
          folder.folderType === 'INBOX' ? '\\Inbox' : '',
          folder.folderType === 'SENT' ? '\\Sent' : '',
          folder.folderType === 'DRAFTS' ? '\\Drafts' : '',
          folder.folderType === 'TRASH' ? '\\Trash' : '',
          folder.folderType === 'SPAM' ? '\\Junk' : ''
        ].filter(Boolean),
        delimiter: '/',
        exists: folder.totalCount,
        recent: 0, // Would need to track recent messages
        unseen: folder.unreadCount,
        uidNext: folder.totalCount + 1,
        uidValidity: Date.now(), // Simplified - should be persistent
        permanentFlags: ['\\Answered', '\\Flagged', '\\Deleted', '\\Seen', '\\Draft'],
        flags: []
      }))

      return NextResponse.json({
        success: true,
        folders: imapFolders
      })

    } catch (error) {
      console.error('IMAP folders error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve folders' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: false
  }
)

// POST /api/email/imap/folders - Create new folder
export const POST = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      const { accountId, name, parentId } = body

      if (!accountId || !name) {
        return NextResponse.json(
          { error: 'Account ID and folder name are required' },
          { status: 400 }
        )
      }

      // Verify account exists and user has access
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or inactive' },
          { status: 404 }
        )
      }

      // Create folder
      const result = await createFolder(accountId, name, parentId)

      if (result.success) {
        return NextResponse.json({
          success: true,
          folderId: result.folderId,
          message: 'Folder created successfully'
        })
      } else {
        return NextResponse.json(
          { error: result.error || 'Failed to create folder' },
          { status: 400 }
        )
      }

    } catch (error) {
      console.error('IMAP create folder error:', error)
      return NextResponse.json(
        { error: 'Failed to create folder' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: true,
    sanitizeInput: true
  }
)

// DELETE /api/email/imap/folders/[id] - Delete folder
export const DELETE = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const folderId = searchParams.get('folderId')
      const accountId = searchParams.get('accountId')

      if (!folderId || !accountId) {
        return NextResponse.json(
          { error: 'Folder ID and Account ID are required' },
          { status: 400 }
        )
      }

      // Verify folder exists and belongs to account
      const folder = await prisma.emailFolder.findFirst({
        where: {
          id: folderId,
          accountId,
          isSystem: false // Can't delete system folders
        }
      })

      if (!folder) {
        return NextResponse.json(
          { error: 'Folder not found or cannot be deleted' },
          { status: 404 }
        )
      }

      // Check if folder has emails
      const emailCount = await prisma.emailRecipient.count({
        where: {
          folderId,
          isDeleted: false
        }
      })

      if (emailCount > 0) {
        return NextResponse.json(
          { error: 'Cannot delete folder with emails. Move emails first.' },
          { status: 400 }
        )
      }

      // Delete folder
      await prisma.emailFolder.delete({
        where: { id: folderId }
      })

      return NextResponse.json({
        success: true,
        message: 'Folder deleted successfully'
      })

    } catch (error) {
      console.error('IMAP delete folder error:', error)
      return NextResponse.json(
        { error: 'Failed to delete folder' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: true
  }
)
