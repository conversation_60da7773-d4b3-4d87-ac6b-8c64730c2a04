import { jsPDF } from 'jspdf'

interface ReceiptData {
  transactionId: string
  studentName: string
  studentId: string
  studentEmail: string
  amount: number
  gatewayFee: number
  totalAmount: number
  paymentDate: Date
  paymentMethod: string
  gateway: string
  gatewayTransactionId: string
  description: string
  instituteName: string
  instituteAddress: string
  instituteEmail: string
  institutePhone: string
}

export class ReceiptGenerator {
  private instituteLogo?: string

  constructor(instituteLogo?: string) {
    this.instituteLogo = instituteLogo
  }

  /**
   * Generate PDF receipt
   */
  generatePDFReceipt(data: ReceiptData): Buffer {
    const doc = new jsPDF()
    
    // Set font
    doc.setFont('helvetica')

    // Header
    this.addHeader(doc, data)
    
    // Receipt details
    this.addReceiptDetails(doc, data)
    
    // Payment details
    this.addPaymentDetails(doc, data)
    
    // Footer
    this.addFooter(doc, data)

    // Return as buffer
    return Buffer.from(doc.output('arraybuffer'))
  }

  /**
   * Generate HTML receipt
   */
  generateHTMLReceipt(data: ReceiptData): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt - ${data.transactionId}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .institute-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .receipt-title {
            font-size: 20px;
            color: #28a745;
            margin-top: 20px;
        }
        .details-section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dotted #dee2e6;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .amount-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .total-amount {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        .success-badge {
            background-color: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            display: inline-block;
            margin: 10px 0;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="institute-name">${data.instituteName}</div>
        <div>${data.instituteAddress}</div>
        <div>Email: ${data.instituteEmail} | Phone: ${data.institutePhone}</div>
        <div class="receipt-title">PAYMENT RECEIPT</div>
        <div class="success-badge">✓ PAYMENT SUCCESSFUL</div>
    </div>

    <div class="details-section">
        <div class="section-title">Student Information</div>
        <div class="detail-row">
            <span class="detail-label">Student Name:</span>
            <span class="detail-value">${data.studentName}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Student ID:</span>
            <span class="detail-value">${data.studentId}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Email:</span>
            <span class="detail-value">${data.studentEmail}</span>
        </div>
    </div>

    <div class="details-section">
        <div class="section-title">Payment Information</div>
        <div class="detail-row">
            <span class="detail-label">Transaction ID:</span>
            <span class="detail-value">${data.transactionId}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Gateway Transaction ID:</span>
            <span class="detail-value">${data.gatewayTransactionId}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Payment Date:</span>
            <span class="detail-value">${data.paymentDate.toLocaleDateString('en-IN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Payment Method:</span>
            <span class="detail-value">${data.paymentMethod} (${data.gateway})</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Description:</span>
            <span class="detail-value">${data.description}</span>
        </div>
    </div>

    <div class="amount-section">
        <div class="section-title">Amount Details</div>
        <div class="detail-row">
            <span class="detail-label">Base Amount:</span>
            <span class="detail-value">₹${data.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Gateway Fee:</span>
            <span class="detail-value">₹${data.gatewayFee.toLocaleString('en-IN', { minimumFractionDigits: 2 })}</span>
        </div>
        <div class="detail-row total-amount">
            <span class="detail-label">Total Amount Paid:</span>
            <span class="detail-value">₹${data.totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}</span>
        </div>
    </div>

    <div class="footer">
        <p><strong>Important Notes:</strong></p>
        <p>• This is a computer-generated receipt and does not require a signature.</p>
        <p>• Please save this receipt for your records.</p>
        <p>• For any queries, contact the institute at ${data.instituteEmail}</p>
        <p>• Transaction processed securely through ${data.gateway}</p>
        <br>
        <p>Generated on: ${new Date().toLocaleString('en-IN')}</p>
    </div>
</body>
</html>
    `
  }

  private addHeader(doc: jsPDF, data: ReceiptData) {
    // Institute name
    doc.setFontSize(20)
    doc.setFont('helvetica', 'bold')
    doc.text(data.instituteName, 105, 30, { align: 'center' })

    // Institute details
    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(data.instituteAddress, 105, 40, { align: 'center' })
    doc.text(`Email: ${data.instituteEmail} | Phone: ${data.institutePhone}`, 105, 47, { align: 'center' })

    // Receipt title
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.text('PAYMENT RECEIPT', 105, 65, { align: 'center' })

    // Success indicator
    doc.setFontSize(12)
    doc.setTextColor(0, 128, 0)
    doc.text('✓ PAYMENT SUCCESSFUL', 105, 75, { align: 'center' })
    doc.setTextColor(0, 0, 0)

    // Line separator
    doc.line(20, 85, 190, 85)
  }

  private addReceiptDetails(doc: jsPDF, data: ReceiptData) {
    let yPos = 100

    // Student Information
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text('Student Information', 20, yPos)
    yPos += 10

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(`Student Name: ${data.studentName}`, 20, yPos)
    yPos += 7
    doc.text(`Student ID: ${data.studentId}`, 20, yPos)
    yPos += 7
    doc.text(`Email: ${data.studentEmail}`, 20, yPos)
    yPos += 15

    // Payment Information
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text('Payment Information', 20, yPos)
    yPos += 10

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(`Transaction ID: ${data.transactionId}`, 20, yPos)
    yPos += 7
    doc.text(`Gateway Transaction ID: ${data.gatewayTransactionId}`, 20, yPos)
    yPos += 7
    doc.text(`Payment Date: ${data.paymentDate.toLocaleDateString('en-IN')} ${data.paymentDate.toLocaleTimeString('en-IN')}`, 20, yPos)
    yPos += 7
    doc.text(`Payment Method: ${data.paymentMethod} (${data.gateway})`, 20, yPos)
    yPos += 7
    doc.text(`Description: ${data.description}`, 20, yPos)
  }

  private addPaymentDetails(doc: jsPDF, data: ReceiptData) {
    const yPos = 200

    // Amount details box
    doc.setDrawColor(0, 123, 255)
    doc.setFillColor(248, 249, 250)
    doc.rect(20, yPos, 170, 40, 'FD')

    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text('Amount Details', 25, yPos + 12)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(`Base Amount:`, 25, yPos + 22)
    doc.text(`₹${data.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`, 160, yPos + 22, { align: 'right' })

    doc.text(`Gateway Fee:`, 25, yPos + 29)
    doc.text(`₹${data.gatewayFee.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`, 160, yPos + 29, { align: 'right' })

    // Total amount
    doc.setFont('helvetica', 'bold')
    doc.text(`Total Amount Paid:`, 25, yPos + 36)
    doc.text(`₹${data.totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`, 160, yPos + 36, { align: 'right' })
  }

  private addFooter(doc: jsPDF, data: ReceiptData) {
    const yPos = 260

    doc.setFontSize(8)
    doc.setFont('helvetica', 'normal')
    doc.text('Important Notes:', 20, yPos)
    doc.text('• This is a computer-generated receipt and does not require a signature.', 20, yPos + 7)
    doc.text('• Please save this receipt for your records.', 20, yPos + 14)
    doc.text(`• For any queries, contact the institute at ${data.instituteEmail}`, 20, yPos + 21)
    doc.text(`• Transaction processed securely through ${data.gateway}`, 20, yPos + 28)

    doc.text(`Generated on: ${new Date().toLocaleString('en-IN')}`, 105, yPos + 40, { align: 'center' })
  }
}

/**
 * Factory function to create receipt generator
 */
export function createReceiptGenerator(instituteLogo?: string): ReceiptGenerator {
  return new ReceiptGenerator(instituteLogo)
}
