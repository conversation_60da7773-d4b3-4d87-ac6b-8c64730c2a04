'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import StudentLayout from '@/components/student/student-layout'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft,
  Star, 
  Archive, 
  Trash2, 
  Reply,
  ReplyAll,
  Forward,
  Download,
  MoreVertical,
  Clock,
  User,
  Mail
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface EmailMessage {
  id: string
  messageId: string
  subject: string
  body: string
  bodyText: string
  fromEmail: string
  fromName: string
  toEmails: string[]
  ccEmails: string[]
  bccEmails: string[]
  priority: string
  isStarred: boolean
  isRead: boolean
  folder: {
    id: string
    name: string
    folderType: string
  } | null
  attachments: Array<{
    id: string
    filename: string
    originalName: string
    size: number
    mimeType: string
    path: string
  }>
  sentAt: string
  createdAt: string
  updatedAt: string
}

export default function EmailMessagePage() {
  const router = useRouter()
  const params = useParams()
  const messageId = params.id as string

  const [email, setEmail] = useState<EmailMessage | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchEmail = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('studentToken')
      
      if (!token) {
        router.push('/student/login')
        return
      }

      const response = await fetch(`/api/student/email/message/${messageId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setEmail(data.email)
      } else if (response.status === 401) {
        localStorage.removeItem('studentToken')
        router.push('/student/login')
      } else if (response.status === 404) {
        setError('Email not found')
      } else {
        setError('Failed to load email')
      }
    } catch (error) {
      console.error('Error fetching email:', error)
      setError('Failed to load email')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (messageId) {
      fetchEmail()
    }
  }, [messageId])

  const handleAction = async (action: string, value?: any) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      const response = await fetch(`/api/student/email/message/${messageId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(
          action === 'star' ? { isStarred: value } :
          action === 'archive' ? { action: 'archive' } :
          action === 'delete' ? { action: 'delete' } :
          {}
        )
      })

      if (response.ok) {
        fetchEmail() // Refresh email data
      }
    } catch (error) {
      console.error('Action error:', error)
    }
  }

  const handleDownloadAttachment = async (attachmentId: string, filename: string) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      const response = await fetch(`/api/student/email/attachments/${attachmentId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Download error:', error)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'LOW': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <StudentLayout>
        <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading email...</p>
        </div>
        </div>
      </StudentLayout>
    )
  }

  if (error || !email) {
    return (
      <StudentLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Mail className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Email not found</h3>
            <p className="text-gray-600 mb-4">{error || 'The email you\'re looking for doesn\'t exist.'}</p>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </StudentLayout>
    )
  }

  return (
    <StudentLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          onClick={() => router.back()}
          variant="outline"
          size="sm"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        <div className="flex items-center gap-2">
          <Button
            onClick={() => handleAction('star', !email.isStarred)}
            variant="outline"
            size="sm"
            className={email.isStarred ? 'text-yellow-500' : ''}
          >
            <Star className={`h-4 w-4 mr-2 ${email.isStarred ? 'fill-current' : ''}`} />
            {email.isStarred ? 'Starred' : 'Star'}
          </Button>

          <Button
            onClick={() => handleAction('archive')}
            variant="outline"
            size="sm"
          >
            <Archive className="h-4 w-4 mr-2" />
            Archive
          </Button>

          <Button
            onClick={() => handleAction('delete')}
            variant="outline"
            size="sm"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => router.push(`/student/email/compose?reply=${email.id}`)}>
                <Reply className="h-4 w-4 mr-2" />
                Reply
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/student/email/compose?replyAll=${email.id}`)}>
                <ReplyAll className="h-4 w-4 mr-2" />
                Reply All
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/student/email/compose?forward=${email.id}`)}>
                <Forward className="h-4 w-4 mr-2" />
                Forward
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Email Content */}
      <Card>
        <CardHeader className="space-y-4">
          {/* Subject and Priority */}
          <div className="flex items-start justify-between gap-4">
            <h1 className="text-2xl font-bold text-gray-900 flex-1">
              {email.subject || '(No subject)'}
            </h1>
            {email.priority !== 'NORMAL' && (
              <Badge className={getPriorityColor(email.priority)}>
                {email.priority}
              </Badge>
            )}
          </div>

          {/* Sender Info */}
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
              <User className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <p className="font-medium text-gray-900">
                {email.fromName || email.fromEmail}
              </p>
              <p className="text-sm text-gray-600">
                {email.fromEmail}
              </p>
            </div>
            <div className="text-right">
              <div className="flex items-center text-sm text-gray-600 mb-1">
                <Clock className="h-4 w-4 mr-1" />
                {formatDate(email.sentAt)}
              </div>
              {email.folder && (
                <Badge variant="outline" className="text-xs">
                  {email.folder.name}
                </Badge>
              )}
            </div>
          </div>

          {/* Recipients */}
          <div className="space-y-2 text-sm">
            <div className="flex">
              <span className="font-medium text-gray-700 w-12">To:</span>
              <span className="text-gray-900">{email.toEmails.join(', ')}</span>
            </div>
            {email.ccEmails.length > 0 && (
              <div className="flex">
                <span className="font-medium text-gray-700 w-12">CC:</span>
                <span className="text-gray-900">{email.ccEmails.join(', ')}</span>
              </div>
            )}
            {email.bccEmails.length > 0 && (
              <div className="flex">
                <span className="font-medium text-gray-700 w-12">BCC:</span>
                <span className="text-gray-900">{email.bccEmails.join(', ')}</span>
              </div>
            )}
          </div>

          <Separator />
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Attachments */}
          {email.attachments.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900">
                Attachments ({email.attachments.length})
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {email.attachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 truncate">
                        {attachment.originalName}
                      </p>
                      <p className="text-sm text-gray-600">
                        {formatFileSize(attachment.size)}
                      </p>
                    </div>
                    <Button
                      onClick={() => handleDownloadAttachment(attachment.id, attachment.originalName)}
                      variant="outline"
                      size="sm"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              <Separator />
            </div>
          )}

          {/* Email Body */}
          <div className="prose max-w-none">
            <div 
              dangerouslySetInnerHTML={{ __html: email.body }}
              className="text-gray-900 leading-relaxed"
            />
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center gap-3">
        <Button onClick={() => router.push(`/student/email/compose?reply=${email.id}`)}>
          <Reply className="h-4 w-4 mr-2" />
          Reply
        </Button>
        <Button 
          variant="outline"
          onClick={() => router.push(`/student/email/compose?replyAll=${email.id}`)}
        >
          <ReplyAll className="h-4 w-4 mr-2" />
          Reply All
        </Button>
        <Button 
          variant="outline"
          onClick={() => router.push(`/student/email/compose?forward=${email.id}`)}
        >
          <Forward className="h-4 w-4 mr-2" />
          Forward
        </Button>
      </div>
      </div>
    </StudentLayout>
  )
}
