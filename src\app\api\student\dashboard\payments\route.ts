import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/dashboard/payments - Get pending payments for student dashboard
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId

      // For demo purposes, we'll create some sample payment data
      // In a real implementation, this would come from a payments table
      const currentDate = new Date()
      const academicYear = `${currentDate.getFullYear()}-${currentDate.getFullYear() + 1}`

      const samplePayments = [
        {
          id: 'pay_001',
          description: 'Tuition Fee - Semester 1',
          amount: 50000,
          dueDate: new Date(currentDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'PENDING' as const,
          category: 'TUITION',
          academicYear
        },
        {
          id: 'pay_002',
          description: 'Library Fee',
          amount: 2000,
          dueDate: new Date(currentDate.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'PENDING' as const,
          category: 'LIBRARY',
          academicYear
        },
        {
          id: 'pay_003',
          description: 'Hostel Fee - Semester 1',
          amount: 25000,
          dueDate: new Date(currentDate.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'OVERDUE' as const,
          category: 'HOSTEL',
          academicYear
        }
      ]

      // Filter to show only pending/overdue payments for dashboard
      const pendingPayments = samplePayments.filter(p => 
        p.status === 'PENDING' || p.status === 'OVERDUE'
      )

      return NextResponse.json({
        success: true,
        payments: pendingPayments
      })

    } catch (error) {
      console.error('Student dashboard payments error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve payments' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)
