import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { getEmailById, getEmailThread, deleteEmail } from '@/lib/email-retrieval'
import { prisma } from '@/lib/prisma'

// GET /api/email/imap/messages/[id] - Get single message details
export const GET = createSecureApi(
  async (context, body, params) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const accountId = searchParams.get('accountId')
      const emailId = params?.id

      if (!accountId || !emailId) {
        return NextResponse.json(
          { error: 'Account ID and email ID are required' },
          { status: 400 }
        )
      }

      // Verify account exists and user has access
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or inactive' },
          { status: 404 }
        )
      }

      // Get email details
      const email = await getEmailById(accountId, emailId)

      if (!email) {
        return NextResponse.json(
          { error: 'Email not found' },
          { status: 404 }
        )
      }

      // Format for IMAP-like response with full details
      const imapMessage = {
        uid: parseInt(email.id.slice(-8), 16),
        messageId: email.messageId,
        subject: email.subject,
        body: email.body,
        bodyText: email.bodyText,
        from: {
          email: email.fromEmail,
          name: email.fromName
        },
        to: email.toEmails.map(e => ({ email: e })),
        cc: email.ccEmails.map(e => ({ email: e })),
        bcc: email.bccEmails.map(e => ({ email: e })),
        date: email.sentAt || email.createdAt,
        size: Buffer.byteLength(email.body, 'utf8'),
        flags: [
          email.isRead ? '\\Seen' : '',
          email.isStarred ? '\\Flagged' : '',
          email.isSpam ? '\\Junk' : ''
        ].filter(Boolean),
        priority: email.priority,
        threadId: email.threadId,
        folder: email.folder,
        attachments: email.attachments.map(att => ({
          id: att.id,
          filename: att.filename,
          originalName: att.originalName,
          contentType: att.mimeType,
          size: att.size,
          downloadUrl: `/api/email/attachments/${att.id}/download?accountId=${accountId}`
        })),
        headers: {
          'Message-ID': email.messageId,
          'Date': (email.sentAt || email.createdAt).toUTCString(),
          'From': email.fromName ? `${email.fromName} <${email.fromEmail}>` : email.fromEmail,
          'To': email.toEmails.join(', '),
          'Subject': email.subject,
          'MIME-Version': '1.0',
          'Content-Type': 'multipart/mixed'
        }
      }

      // Automatically mark as read when retrieving full message
      if (!email.isRead) {
        await markEmailAsRead(accountId, emailId, true)
        imapMessage.flags.push('\\Seen')
      }

      return NextResponse.json({
        success: true,
        message: imapMessage
      })

    } catch (error) {
      console.error('IMAP message details error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve message details' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: false
  }
)

// DELETE /api/email/imap/messages/[id] - Delete message
export const DELETE = createSecureApi(
  async (context, body, params) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const accountId = searchParams.get('accountId')
      const emailId = params?.id
      const permanent = searchParams.get('permanent') === 'true'

      if (!accountId || !emailId) {
        return NextResponse.json(
          { error: 'Account ID and email ID are required' },
          { status: 400 }
        )
      }

      // Verify account exists and user has access
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or inactive' },
          { status: 404 }
        )
      }

      // Delete email
      const success = await deleteEmail(accountId, emailId, permanent)

      if (success) {
        return NextResponse.json({
          success: true,
          message: permanent ? 'Email permanently deleted' : 'Email moved to trash'
        })
      } else {
        return NextResponse.json(
          { error: 'Failed to delete email' },
          { status: 500 }
        )
      }

    } catch (error) {
      console.error('IMAP delete message error:', error)
      return NextResponse.json(
        { error: 'Failed to delete message' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: true
  }
)

// GET /api/email/imap/messages/[id]/thread - Get email thread
export const GET_THREAD = createSecureApi(
  async (context, body, params) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const accountId = searchParams.get('accountId')
      const emailId = params?.id

      if (!accountId || !emailId) {
        return NextResponse.json(
          { error: 'Account ID and email ID are required' },
          { status: 400 }
        )
      }

      // Get the email to find its thread ID
      const email = await getEmailById(accountId, emailId)
      if (!email || !email.threadId) {
        return NextResponse.json(
          { error: 'Email or thread not found' },
          { status: 404 }
        )
      }

      // Get thread emails
      const threadEmails = await getEmailThread(accountId, email.threadId)

      // Format for IMAP-like response
      const imapThread = threadEmails.map((email, index) => ({
        uid: parseInt(email.id.slice(-8), 16),
        seq: index + 1,
        messageId: email.messageId,
        subject: email.subject,
        from: {
          email: email.fromEmail,
          name: email.fromName
        },
        date: email.sentAt || email.createdAt,
        size: Buffer.byteLength(email.body, 'utf8'),
        flags: [
          email.isRead ? '\\Seen' : '',
          email.isStarred ? '\\Flagged' : '',
          email.isSpam ? '\\Junk' : ''
        ].filter(Boolean),
        hasAttachments: email.attachments.length > 0,
        preview: email.bodyText ? 
          email.bodyText.substring(0, 200) + (email.bodyText.length > 200 ? '...' : '') :
          email.body.replace(/<[^>]*>/g, '').substring(0, 200) + '...'
      }))

      return NextResponse.json({
        success: true,
        threadId: email.threadId,
        messages: imapThread,
        count: threadEmails.length
      })

    } catch (error) {
      console.error('IMAP thread error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve thread' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: false
  }
)

// Handle different HTTP methods for the same route
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const url = new URL(request.url)
  
  if (url.pathname.endsWith('/thread')) {
    return GET_THREAD({ request, clientIP: '', user: null }, null, params)
  } else {
    return GET({ request, clientIP: '', user: null }, null, params)
  }
}
