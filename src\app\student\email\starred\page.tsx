'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import StudentLayout from '@/components/student/student-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Search, 
  Star, 
  Archive, 
  Trash2, 
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  MoreVertical
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface StarredEmail {
  id: string
  subject: string
  fromEmail: string
  fromName: string
  body: string
  priority: string
  isRead: boolean
  sentAt: string
  attachments: any[]
}

export default function StarredEmailsPage() {
  const router = useRouter()
  const [emails, setEmails] = useState<StarredEmail[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)

  const fetchStarredEmails = async (page = 1, search = '') => {
    try {
      setLoading(true)
      const token = localStorage.getItem('studentToken')
      
      if (!token) {
        router.push('/student/login')
        return
      }

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        starred: 'true'
      })

      if (search) {
        params.append('search', search)
      }

      const response = await fetch(`/api/student/email/messages?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setEmails(data.emails || [])
        setTotalPages(data.pagination?.totalPages || 1)
        setTotalCount(data.pagination?.totalCount || 0)
      } else if (response.status === 401) {
        localStorage.removeItem('studentToken')
        router.push('/student/login')
      } else {
        console.error('Failed to fetch starred emails')
      }
    } catch (error) {
      console.error('Error fetching starred emails:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStarredEmails(currentPage, searchTerm)
  }, [currentPage])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchStarredEmails(1, searchTerm)
  }

  const handleEmailClick = (emailId: string) => {
    router.push(`/student/email/message/${emailId}`)
  }

  const handleSelectEmail = (emailId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmails([...selectedEmails, emailId])
    } else {
      setSelectedEmails(selectedEmails.filter(id => id !== emailId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEmails(emails.map(email => email.id))
    } else {
      setSelectedEmails([])
    }
  }

  const handleBulkAction = async (action: string) => {
    if (selectedEmails.length === 0) return

    try {
      const token = localStorage.getItem('studentToken')
      
      for (const emailId of selectedEmails) {
        await fetch(`/api/student/email/message/${emailId}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ action })
        })
      }

      setSelectedEmails([])
      fetchStarredEmails(currentPage, searchTerm)
    } catch (error) {
      console.error('Bulk action error:', error)
    }
  }

  const handleUnstar = async (emailId: string) => {
    try {
      const token = localStorage.getItem('studentToken')
      
      await fetch(`/api/student/email/message/${emailId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isStarred: false })
      })

      fetchStarredEmails(currentPage, searchTerm)
    } catch (error) {
      console.error('Unstar error:', error)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } else if (diffDays <= 7) {
      return date.toLocaleDateString('en-US', { weekday: 'short' })
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'LOW': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <StudentLayout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Starred</h1>
          <p className="text-sm text-gray-600">
            {totalCount} starred {totalCount === 1 ? 'email' : 'emails'}
          </p>
        </div>
        <Button
          onClick={() => fetchStarredEmails(currentPage, searchTerm)}
          variant="outline"
          size="sm"
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between gap-4">
        <form onSubmit={handleSearch} className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search starred emails..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </form>

        {selectedEmails.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {selectedEmails.length} selected
            </span>
            <Button
              onClick={() => handleBulkAction('archive')}
              variant="outline"
              size="sm"
            >
              <Archive className="h-4 w-4 mr-2" />
              Archive
            </Button>
            <Button
              onClick={() => handleBulkAction('delete')}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        )}
      </div>

      {/* Email List */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={selectedEmails.length === emails.length && emails.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <CardTitle className="text-sm font-medium">
              Select All
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">Loading starred emails...</p>
            </div>
          ) : emails.length === 0 ? (
            <div className="p-8 text-center">
              <Star className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No starred emails</h3>
              <p className="text-gray-600">
                {searchTerm ? 'No starred emails match your search.' : 'You haven\'t starred any emails yet.'}
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {emails.map((email) => (
                <div
                  key={email.id}
                  className={`flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer ${
                    !email.isRead ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => handleEmailClick(email.id)}
                >
                  <Checkbox
                    checked={selectedEmails.includes(email.id)}
                    onCheckedChange={(checked) => handleSelectEmail(email.id, checked as boolean)}
                    onClick={(e) => e.stopPropagation()}
                  />
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto text-yellow-500 hover:text-yellow-600"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleUnstar(email.id)
                    }}
                  >
                    <Star className="h-4 w-4 fill-current" />
                  </Button>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`text-sm truncate ${!email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`}>
                        {email.fromName || email.fromEmail}
                      </span>
                      {email.priority !== 'NORMAL' && (
                        <Badge variant="secondary" className={getPriorityColor(email.priority)}>
                          {email.priority}
                        </Badge>
                      )}
                      {email.attachments.length > 0 && (
                        <Badge variant="outline">
                          📎 {email.attachments.length}
                        </Badge>
                      )}
                    </div>
                    <p className={`text-sm truncate mb-1 ${!email.isRead ? 'font-semibold text-gray-900' : 'text-gray-900'}`}>
                      {email.subject || '(No subject)'}
                    </p>
                    <p className="text-sm text-gray-600 truncate">
                      {email.body.replace(/<[^>]*>/g, '').substring(0, 100)}...
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">
                      {formatDate(email.sentAt)}
                    </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleUnstar(email.id)}>
                          <Star className="h-4 w-4 mr-2" />
                          Remove Star
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkAction('archive')}>
                          <Archive className="h-4 w-4 mr-2" />
                          Archive
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkAction('delete')}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </p>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
      </div>
    </StudentLayout>
  )
}
