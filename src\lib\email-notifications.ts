import { prisma } from './prisma'

interface PaymentNotificationData {
  studentName: string
  studentEmail: string
  transactionId: string
  amount: number
  description: string
  paymentDate: Date
  receiptUrl?: string
}

interface EmailTemplate {
  subject: string
  htmlBody: string
  textBody: string
}

export class EmailNotificationService {
  private instituteName: string
  private instituteEmail: string

  constructor(instituteName: string = 'Institute', instituteEmail: string = '<EMAIL>') {
    this.instituteName = instituteName
    this.instituteEmail = instituteEmail
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmation(data: PaymentNotificationData): Promise<boolean> {
    try {
      const template = this.generatePaymentConfirmationTemplate(data)
      
      // Create email record in database
      const emailId = await this.createEmailRecord(
        data.studentEmail,
        template.subject,
        template.htmlBody,
        template.textBody,
        data.receiptUrl
      )

      // In a real implementation, you would send the email here
      // For now, we'll just log it and mark as sent
      console.log(`Payment confirmation email created with ID: ${emailId}`)
      
      return true
    } catch (error) {
      console.error('Failed to send payment confirmation email:', error)
      return false
    }
  }

  /**
   * Send payment failure notification
   */
  async sendPaymentFailureNotification(data: Omit<PaymentNotificationData, 'receiptUrl'>): Promise<boolean> {
    try {
      const template = this.generatePaymentFailureTemplate(data)
      
      const emailId = await this.createEmailRecord(
        data.studentEmail,
        template.subject,
        template.htmlBody,
        template.textBody
      )

      console.log(`Payment failure notification created with ID: ${emailId}`)
      
      return true
    } catch (error) {
      console.error('Failed to send payment failure notification:', error)
      return false
    }
  }

  /**
   * Send payment reminder
   */
  async sendPaymentReminder(data: {
    studentName: string
    studentEmail: string
    dueAmount: number
    dueDate: Date
    description: string
  }): Promise<boolean> {
    try {
      const template = this.generatePaymentReminderTemplate(data)
      
      const emailId = await this.createEmailRecord(
        data.studentEmail,
        template.subject,
        template.htmlBody,
        template.textBody
      )

      console.log(`Payment reminder created with ID: ${emailId}`)
      
      return true
    } catch (error) {
      console.error('Failed to send payment reminder:', error)
      return false
    }
  }

  /**
   * Create email record in database
   */
  private async createEmailRecord(
    toEmail: string,
    subject: string,
    htmlBody: string,
    textBody: string,
    attachmentUrl?: string
  ): Promise<string> {
    // Find system email account (or create one if needed)
    let systemAccount = await prisma.emailAccount.findFirst({
      where: {
        email: this.instituteEmail,
        accountType: 'INSTITUTE_ID'
      }
    })

    if (!systemAccount) {
      // Create system account if it doesn't exist
      systemAccount = await prisma.emailAccount.create({
        data: {
          email: this.instituteEmail,
          password: 'system-generated', // This won't be used for sending
          accountType: 'INSTITUTE_ID',
          displayName: this.instituteName,
          isActive: true,
          storageLimit: **********, // 5GB
          imapEnabled: false,
          pop3Enabled: false,
          smtpEnabled: true
        }
      })
    }

    // Find recipient account
    const recipientAccount = await prisma.emailAccount.findFirst({
      where: { email: toEmail }
    })

    // Create email record
    const email = await prisma.email.create({
      data: {
        messageId: `<${Date.now()}.${Math.random().toString(36).substr(2, 9)}@${this.instituteEmail.split('@')[1]}>`,
        subject,
        body: htmlBody,
        bodyText: textBody,
        fromEmail: this.instituteEmail,
        fromName: this.instituteName,
        fromAccountId: systemAccount.id,
        sentAt: new Date(),
        priority: 'NORMAL',
        isStarred: false,
        isDeleted: false
      }
    })

    // Create recipient record if recipient account exists
    if (recipientAccount) {
      // Find inbox folder
      let inboxFolder = await prisma.emailFolder.findFirst({
        where: {
          accountId: recipientAccount.id,
          folderType: 'INBOX'
        }
      })

      if (!inboxFolder) {
        // Create inbox folder if it doesn't exist
        inboxFolder = await prisma.emailFolder.create({
          data: {
            accountId: recipientAccount.id,
            name: 'Inbox',
            folderType: 'INBOX',
            isSystem: true,
            order: 1
          }
        })
      }

      await prisma.emailRecipient.create({
        data: {
          emailId: email.id,
          accountId: recipientAccount.id,
          folderId: inboxFolder.id,
          isRead: false,
          isDeleted: false
        }
      })
    }

    // Add attachment if provided
    if (attachmentUrl) {
      await prisma.emailAttachment.create({
        data: {
          emailId: email.id,
          filename: 'payment-receipt.pdf',
          originalName: 'payment-receipt.pdf',
          mimeType: 'application/pdf',
          size: 0, // Size would be determined when file is actually created
          path: attachmentUrl
        }
      })
    }

    return email.id
  }

  /**
   * Generate payment confirmation email template
   */
  private generatePaymentConfirmationTemplate(data: PaymentNotificationData): EmailTemplate {
    const subject = `Payment Confirmation - ${data.transactionId}`
    
    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f8f9fa; }
        .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .success-icon { font-size: 48px; color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">✓</div>
            <h1>Payment Successful!</h1>
            <p>Your payment has been processed successfully</p>
        </div>
        
        <div class="content">
            <p>Dear ${data.studentName},</p>
            
            <p>We have successfully received your payment. Here are the details:</p>
            
            <div class="details">
                <h3>Payment Details</h3>
                <p><strong>Transaction ID:</strong> ${data.transactionId}</p>
                <p><strong>Amount Paid:</strong> ₹${data.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}</p>
                <p><strong>Description:</strong> ${data.description}</p>
                <p><strong>Payment Date:</strong> ${data.paymentDate.toLocaleDateString('en-IN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</p>
            </div>
            
            <p>Your payment receipt has been attached to this email. Please save it for your records.</p>
            
            <p>If you have any questions about this payment, please contact us at ${this.instituteEmail}</p>
            
            <p>Thank you for your payment!</p>
        </div>
        
        <div class="footer">
            <p>This is an automated message from ${this.instituteName}</p>
            <p>Please do not reply to this email</p>
        </div>
    </div>
</body>
</html>
    `

    const textBody = `
Payment Successful!

Dear ${data.studentName},

We have successfully received your payment. Here are the details:

Transaction ID: ${data.transactionId}
Amount Paid: ₹${data.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
Description: ${data.description}
Payment Date: ${data.paymentDate.toLocaleDateString('en-IN')} ${data.paymentDate.toLocaleTimeString('en-IN')}

Your payment receipt has been attached to this email. Please save it for your records.

If you have any questions about this payment, please contact us at ${this.instituteEmail}

Thank you for your payment!

---
This is an automated message from ${this.instituteName}
Please do not reply to this email
    `

    return { subject, htmlBody, textBody }
  }

  /**
   * Generate payment failure email template
   */
  private generatePaymentFailureTemplate(data: Omit<PaymentNotificationData, 'receiptUrl'>): EmailTemplate {
    const subject = `Payment Failed - ${data.transactionId}`
    
    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc3545; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f8f9fa; }
        .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .error-icon { font-size: 48px; color: #dc3545; }
        .retry-button { background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="error-icon">✗</div>
            <h1>Payment Failed</h1>
            <p>Your payment could not be processed</p>
        </div>
        
        <div class="content">
            <p>Dear ${data.studentName},</p>
            
            <p>We were unable to process your payment. Please try again or contact us for assistance.</p>
            
            <div class="details">
                <h3>Payment Details</h3>
                <p><strong>Transaction ID:</strong> ${data.transactionId}</p>
                <p><strong>Amount:</strong> ₹${data.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}</p>
                <p><strong>Description:</strong> ${data.description}</p>
                <p><strong>Attempted Date:</strong> ${data.paymentDate.toLocaleDateString('en-IN')}</p>
            </div>
            
            <p>You can try making the payment again by logging into your student portal.</p>
            
            <a href="${process.env.NEXTAUTH_URL}/student/payments" class="retry-button">Retry Payment</a>
            
            <p>If you continue to experience issues, please contact us at ${this.instituteEmail}</p>
        </div>
        
        <div class="footer">
            <p>This is an automated message from ${this.instituteName}</p>
            <p>Please do not reply to this email</p>
        </div>
    </div>
</body>
</html>
    `

    const textBody = `
Payment Failed

Dear ${data.studentName},

We were unable to process your payment. Please try again or contact us for assistance.

Payment Details:
Transaction ID: ${data.transactionId}
Amount: ₹${data.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
Description: ${data.description}
Attempted Date: ${data.paymentDate.toLocaleDateString('en-IN')}

You can try making the payment again by logging into your student portal at:
${process.env.NEXTAUTH_URL}/student/payments

If you continue to experience issues, please contact us at ${this.instituteEmail}

---
This is an automated message from ${this.instituteName}
Please do not reply to this email
    `

    return { subject, htmlBody, textBody }
  }

  /**
   * Generate payment reminder email template
   */
  private generatePaymentReminderTemplate(data: {
    studentName: string
    studentEmail: string
    dueAmount: number
    dueDate: Date
    description: string
  }): EmailTemplate {
    const subject = `Payment Reminder - ${data.description}`
    
    const daysUntilDue = Math.ceil((data.dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    const isOverdue = daysUntilDue < 0
    
    const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: ${isOverdue ? '#dc3545' : '#ffc107'}; color: ${isOverdue ? 'white' : '#212529'}; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f8f9fa; }
        .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .warning-icon { font-size: 48px; }
        .pay-button { background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="warning-icon">${isOverdue ? '⚠️' : '⏰'}</div>
            <h1>${isOverdue ? 'Payment Overdue' : 'Payment Reminder'}</h1>
            <p>${isOverdue ? 'Your payment is overdue' : 'You have a pending payment'}</p>
        </div>
        
        <div class="content">
            <p>Dear ${data.studentName},</p>
            
            <p>${isOverdue 
              ? 'This is a reminder that your payment is overdue. Please make the payment as soon as possible to avoid any late fees.'
              : `This is a friendly reminder that you have a payment due ${daysUntilDue === 0 ? 'today' : `in ${daysUntilDue} day${daysUntilDue === 1 ? '' : 's'}`}.`
            }</p>
            
            <div class="details">
                <h3>Payment Details</h3>
                <p><strong>Description:</strong> ${data.description}</p>
                <p><strong>Amount Due:</strong> ₹${data.dueAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}</p>
                <p><strong>Due Date:</strong> ${data.dueDate.toLocaleDateString('en-IN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</p>
                ${isOverdue ? `<p><strong>Days Overdue:</strong> ${Math.abs(daysUntilDue)} day${Math.abs(daysUntilDue) === 1 ? '' : 's'}</p>` : ''}
            </div>
            
            <p>You can make the payment online through your student portal:</p>
            
            <a href="${process.env.NEXTAUTH_URL}/student/payments" class="pay-button">Pay Now</a>
            
            <p>If you have already made this payment, please ignore this reminder. If you have any questions, please contact us at ${this.instituteEmail}</p>
        </div>
        
        <div class="footer">
            <p>This is an automated message from ${this.instituteName}</p>
            <p>Please do not reply to this email</p>
        </div>
    </div>
</body>
</html>
    `

    const textBody = `
${isOverdue ? 'Payment Overdue' : 'Payment Reminder'}

Dear ${data.studentName},

${isOverdue 
  ? 'This is a reminder that your payment is overdue. Please make the payment as soon as possible to avoid any late fees.'
  : `This is a friendly reminder that you have a payment due ${daysUntilDue === 0 ? 'today' : `in ${daysUntilDue} day${daysUntilDue === 1 ? '' : 's'}`}.`
}

Payment Details:
Description: ${data.description}
Amount Due: ₹${data.dueAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
Due Date: ${data.dueDate.toLocaleDateString('en-IN')}
${isOverdue ? `Days Overdue: ${Math.abs(daysUntilDue)} day${Math.abs(daysUntilDue) === 1 ? '' : 's'}` : ''}

You can make the payment online through your student portal at:
${process.env.NEXTAUTH_URL}/student/payments

If you have already made this payment, please ignore this reminder. If you have any questions, please contact us at ${this.instituteEmail}

---
This is an automated message from ${this.instituteName}
Please do not reply to this email
    `

    return { subject, htmlBody, textBody }
  }
}

/**
 * Factory function to create email notification service
 */
export function createEmailNotificationService(
  instituteName?: string,
  instituteEmail?: string
): EmailNotificationService {
  return new EmailNotificationService(
    instituteName || process.env.INSTITUTE_NAME || 'Institute',
    instituteEmail || process.env.INSTITUTE_EMAIL || '<EMAIL>'
  )
}
