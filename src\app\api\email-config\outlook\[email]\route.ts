import { NextRequest, NextResponse } from 'next/server'
import { createEmailClientCompatibilityService } from '@/lib/email-client-compatibility'

// GET /api/email-config/outlook/[email] - Outlook autodiscover configuration
export async function GET(
  request: NextRequest,
  { params }: { params: { email: string } }
) {
  try {
    const email = decodeURIComponent(params.email)
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    const compatibilityService = createEmailClientCompatibilityService()
    
    // Validate that this email has client access enabled
    const hasImapAccess = await compatibilityService.validateClientAccess(email, 'IMAP')
    const hasSmtpAccess = await compatibilityService.validateClientAccess(email, 'SMTP')
    
    if (!hasImapAccess || !hasSmtpAccess) {
      return NextResponse.json(
        { error: 'Email client access not enabled for this account' },
        { status: 403 }
      )
    }

    // Generate Outlook autodiscover XML
    const autoconfigXml = compatibilityService.generateOutlookAutoconfig(email)

    return new NextResponse(autoconfigXml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
      }
    })

  } catch (error) {
    console.error('Outlook autoconfig error:', error)
    return NextResponse.json(
      { error: 'Failed to generate configuration' },
      { status: 500 }
    )
  }
}

// POST /api/email-config/outlook/[email] - Handle Outlook autodiscover POST requests
export async function POST(
  request: NextRequest,
  { params }: { params: { email: string } }
) {
  // Outlook sometimes sends POST requests for autodiscover
  return GET(request, { params })
}
