import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'

// GET /api/admin/payment-gateways - Get all payment gateway configurations
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get all payment gateway configurations
      const configs = await prisma.paymentGatewayConfig.findMany({
        orderBy: { gateway: 'asc' }
      })

      // Get credentials (without sensitive data for security)
      const credentials: Record<string, any> = {}
      
      for (const config of configs) {
        // Return masked credentials for display
        switch (config.gateway) {
          case 'PAYU':
            credentials[config.gateway] = {
              merchantId: process.env.PAYU_MERCHANT_ID ? '****' + process.env.PAYU_MERCHANT_ID.slice(-4) : '',
              merchantKey: process.env.PAYU_MERCHANT_KEY ? '****' + process.env.PAYU_MERCHANT_KEY.slice(-4) : '',
              salt: process.env.PAYU_SALT ? '****' + process.env.PAYU_SALT.slice(-4) : ''
            }
            break
          case 'PHONEPE':
            credentials[config.gateway] = {
              merchantId: process.env.PHONEPE_MERCHANT_ID ? '****' + process.env.PHONEPE_MERCHANT_ID.slice(-4) : '',
              apiKey: process.env.PHONEPE_API_KEY ? '****' + process.env.PHONEPE_API_KEY.slice(-4) : '',
              saltKey: process.env.PHONEPE_SALT_KEY ? '****' + process.env.PHONEPE_SALT_KEY.slice(-4) : ''
            }
            break
          case 'CASHFREE':
            credentials[config.gateway] = {
              appId: process.env.CASHFREE_APP_ID ? '****' + process.env.CASHFREE_APP_ID.slice(-4) : '',
              secretKey: process.env.CASHFREE_SECRET_KEY ? '****' + process.env.CASHFREE_SECRET_KEY.slice(-4) : ''
            }
            break
        }
      }

      return NextResponse.json({
        success: true,
        configs,
        credentials
      })

    } catch (error) {
      console.error('Payment gateway config error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve payment gateway configurations' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: false
  }
)
