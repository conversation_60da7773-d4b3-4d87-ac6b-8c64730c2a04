'use client'

import { useState, useEffect } from 'react'
import StudentLayout from '@/components/student/student-layout'
import { 
  Search, 
  Filter, 
  Star, 
  Paperclip, 
  MoreVertical,
  Archive,
  Trash2,
  MailOpen,
  Mail,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Plus
} from 'lucide-react'
import Link from 'next/link'

interface Email {
  id: string
  messageId: string
  subject: string
  fromEmail: string
  fromName?: string
  receivedAt: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  attachmentCount: number
  priority: string
  preview: string
  folder: string
}

interface EmailFilters {
  search: string
  unreadOnly: boolean
  starredOnly: boolean
  hasAttachments: boolean
}

export default function StudentInboxPage() {
  const [emails, setEmails] = useState<Email[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [filters, setFilters] = useState<EmailFilters>({
    search: '',
    unreadOnly: false,
    starredOnly: false,
    hasAttachments: false
  })

  useEffect(() => {
    fetchEmails()
  }, [currentPage, filters])

  const fetchEmails = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('student_token')
      if (!token) return

      const params = new URLSearchParams({
        folderType: 'INBOX',
        page: currentPage.toString(),
        limit: '20',
        ...(filters.search && { search: filters.search }),
        ...(filters.unreadOnly && { unreadOnly: 'true' }),
        ...(filters.starredOnly && { starredOnly: 'true' }),
        ...(filters.hasAttachments && { hasAttachments: 'true' })
      })

      const response = await fetch(`/api/student/email/messages?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch emails')
      }

      const data = await response.json()
      setEmails(data.messages)
      setTotalPages(data.pagination.totalPages)
      setTotalCount(data.pagination.totalCount)
    } catch (error) {
      setError('Failed to load emails')
      console.error('Error fetching emails:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEmailSelect = (emailId: string) => {
    setSelectedEmails(prev => 
      prev.includes(emailId) 
        ? prev.filter(id => id !== emailId)
        : [...prev, emailId]
    )
  }

  const handleSelectAll = () => {
    if (selectedEmails.length === emails.length) {
      setSelectedEmails([])
    } else {
      setSelectedEmails(emails.map(email => email.id))
    }
  }

  const handleMarkAsRead = async (emailIds: string[], isRead: boolean) => {
    try {
      const token = localStorage.getItem('student_token')
      if (!token) return

      await Promise.all(emailIds.map(emailId => 
        fetch('/api/student/email/messages', {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            emailId,
            action: 'markRead',
            value: isRead
          })
        })
      ))

      await fetchEmails()
      setSelectedEmails([])
    } catch (error) {
      console.error('Error marking emails:', error)
    }
  }

  const handleStarEmail = async (emailId: string, isStarred: boolean) => {
    try {
      const token = localStorage.getItem('student_token')
      if (!token) return

      await fetch('/api/student/email/messages', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          emailId,
          action: 'star',
          value: isStarred
        })
      })

      await fetchEmails()
    } catch (error) {
      console.error('Error starring email:', error)
    }
  }

  const handleArchiveEmails = async (emailIds: string[]) => {
    try {
      const token = localStorage.getItem('student_token')
      if (!token) return

      // Get archive folder ID first
      const foldersResponse = await fetch('/api/student/email/folders', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      
      if (!foldersResponse.ok) return
      
      const foldersData = await foldersResponse.json()
      const archiveFolder = foldersData.folders.find((f: any) => f.type === 'ARCHIVE')
      
      if (!archiveFolder) return

      await Promise.all(emailIds.map(emailId => 
        fetch('/api/student/email/messages', {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            emailId,
            action: 'move',
            value: archiveFolder.id
          })
        })
      ))

      await fetchEmails()
      setSelectedEmails([])
    } catch (error) {
      console.error('Error archiving emails:', error)
    }
  }

  const handleDeleteEmails = async (emailIds: string[]) => {
    if (!confirm('Are you sure you want to delete the selected emails?')) {
      return
    }

    try {
      const token = localStorage.getItem('student_token')
      if (!token) return

      await Promise.all(emailIds.map(emailId => 
        fetch(`/api/student/email/messages/${emailId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
      ))

      await fetchEmails()
      setSelectedEmails([])
    } catch (error) {
      console.error('Error deleting emails:', error)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffDays === 2) {
      return 'Yesterday'
    } else if (diffDays <= 7) {
      return date.toLocaleDateString([], { weekday: 'short' })
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'border-l-red-500'
      case 'HIGH': return 'border-l-orange-500'
      case 'LOW': return 'border-l-gray-300'
      default: return 'border-l-transparent'
    }
  }

  return (
    <StudentLayout>
      <div>
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Inbox</h1>
            <p className="text-sm text-gray-600">
              {totalCount} email{totalCount !== 1 ? 's' : ''}
              {filters.unreadOnly && ' (unread only)'}
              {filters.starredOnly && ' (starred only)'}
            </p>
          </div>
          <div className="flex space-x-3">
            <Link
              href="/student/email/compose"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium inline-flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Compose
            </Link>
            <button
              onClick={fetchEmails}
              className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium inline-flex items-center hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {/* Search and Filters */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex-1 min-w-0">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search emails..."
                    value={filters.search}
                    onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setFilters(prev => ({ ...prev, unreadOnly: !prev.unreadOnly }))}
                  className={`px-3 py-2 text-sm font-medium rounded-md ${
                    filters.unreadOnly 
                      ? 'bg-blue-100 text-blue-700 border border-blue-200' 
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Unread Only
                </button>

                <button
                  onClick={() => setFilters(prev => ({ ...prev, starredOnly: !prev.starredOnly }))}
                  className={`px-3 py-2 text-sm font-medium rounded-md ${
                    filters.starredOnly 
                      ? 'bg-yellow-100 text-yellow-700 border border-yellow-200' 
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Starred Only
                </button>

                <button
                  onClick={() => setFilters(prev => ({ ...prev, hasAttachments: !prev.hasAttachments }))}
                  className={`px-3 py-2 text-sm font-medium rounded-md ${
                    filters.hasAttachments 
                      ? 'bg-purple-100 text-purple-700 border border-purple-200' 
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Has Attachments
                </button>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedEmails.length > 0 && (
            <div className="px-6 py-3 bg-blue-50 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-700">
                  {selectedEmails.length} email{selectedEmails.length !== 1 ? 's' : ''} selected
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleMarkAsRead(selectedEmails, true)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Mark as Read
                  </button>
                  <button
                    onClick={() => handleMarkAsRead(selectedEmails, false)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Mark as Unread
                  </button>
                  <button
                    onClick={() => handleArchiveEmails(selectedEmails)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Archive
                  </button>
                  <button
                    onClick={() => handleDeleteEmails(selectedEmails)}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Email List */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          {/* Select All Header */}
          <div className="px-6 py-3 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedEmails.length === emails.length && emails.length > 0}
                onChange={handleSelectAll}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-3 text-sm font-medium text-gray-700">
                Select All
              </span>
            </div>
          </div>

          {/* Email Items */}
          <ul className="divide-y divide-gray-200">
            {loading ? (
              <li className="px-6 py-8 text-center text-gray-500">
                Loading emails...
              </li>
            ) : emails.length === 0 ? (
              <li className="px-6 py-8 text-center text-gray-500">
                <Mail className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>No emails found</p>
                {Object.values(filters).some(Boolean) && (
                  <button
                    onClick={() => setFilters({ search: '', unreadOnly: false, starredOnly: false, hasAttachments: false })}
                    className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Clear filters
                  </button>
                )}
              </li>
            ) : (
              emails.map((email) => (
                <li key={email.id} className={`border-l-4 ${getPriorityColor(email.priority)}`}>
                  <div className="px-6 py-4 flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedEmails.includes(email.id)}
                      onChange={() => handleEmailSelect(email.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    
                    <div className="ml-4 flex-1 min-w-0">
                      <Link
                        href={`/student/email/message/${email.id}`}
                        className="block hover:bg-gray-50 -mx-4 px-4 py-2 rounded"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3 flex-1 min-w-0">
                            <div className={`w-2 h-2 rounded-full ${email.isRead ? 'bg-gray-300' : 'bg-blue-500'}`} />
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                <p className={`text-sm truncate ${!email.isRead ? 'font-semibold text-gray-900' : 'text-gray-700'}`}>
                                  {email.subject || '(No Subject)'}
                                </p>
                                {email.hasAttachments && <Paperclip className="h-4 w-4 text-gray-400" />}
                              </div>
                              
                              <p className="text-sm text-gray-500 mt-1">
                                {email.fromName ? `${email.fromName} <${email.fromEmail}>` : email.fromEmail}
                              </p>
                              
                              <p className="text-sm text-gray-500 mt-1 truncate">
                                {email.preview}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2 ml-4">
                            <span className="text-sm text-gray-500">
                              {formatDate(email.receivedAt)}
                            </span>
                            
                            <button
                              onClick={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                                handleStarEmail(email.id, !email.isStarred)
                              }}
                              className="p-1 text-gray-400 hover:text-yellow-500"
                            >
                              <Star className={`h-4 w-4 ${email.isStarred ? 'text-yellow-400 fill-current' : ''}`} />
                            </button>
                          </div>
                        </div>
                      </Link>
                    </div>
                  </div>
                </li>
              ))
            )}
          </ul>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{currentPage}</span> of{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </StudentLayout>
  )
}
