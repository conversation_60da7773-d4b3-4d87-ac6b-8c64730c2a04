#!/usr/bin/env node

/**
 * Database Diagnostic Script
 * Diagnoses database configuration issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 DATABASE CONFIGURATION DIAGNOSTIC\n');

// Test 1: Check .env file
console.log('1. Checking .env file...');
try {
  if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');
    console.log('✅ .env file exists');
    
    // Check DATABASE_URL
    const dbUrlMatch = envContent.match(/DATABASE_URL\s*=\s*["']?([^"'\n]+)["']?/);
    if (dbUrlMatch) {
      const dbUrl = dbUrlMatch[1];
      console.log(`   DATABASE_URL: ${dbUrl}`);
      
      if (dbUrl.startsWith('file:')) {
        console.log('✅ SQLite configuration detected');
      } else if (dbUrl.includes('postgresql://') || dbUrl.includes('postgres://')) {
        console.log('❌ PostgreSQL configuration detected - this is the problem!');
        console.log('   Expected: file:./dev.db');
        console.log('   Found: PostgreSQL connection string');
      } else {
        console.log('⚠️  Unknown database configuration');
      }
    } else {
      console.log('❌ DATABASE_URL not found in .env file');
    }
    
    // Check for DIRECT_URL (PostgreSQL specific)
    if (envContent.includes('DIRECT_URL')) {
      console.log('⚠️  DIRECT_URL found - this is PostgreSQL specific and should be removed for SQLite');
    }
  } else {
    console.log('❌ .env file not found');
  }
} catch (error) {
  console.log(`❌ Error reading .env file: ${error.message}`);
}

// Test 2: Check Prisma schema
console.log('\n2. Checking Prisma schema...');
try {
  if (fs.existsSync('prisma/schema.prisma')) {
    const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
    console.log('✅ Prisma schema file exists');
    
    // Check provider
    const providerMatch = schemaContent.match(/provider\s*=\s*["']([^"']+)["']/);
    if (providerMatch) {
      const provider = providerMatch[1];
      console.log(`   Provider: ${provider}`);
      
      if (provider === 'sqlite') {
        console.log('✅ SQLite provider configured correctly');
      } else if (provider === 'postgresql') {
        console.log('❌ PostgreSQL provider detected - this is the problem!');
        console.log('   Expected: sqlite');
        console.log('   Found: postgresql');
      } else {
        console.log(`⚠️  Unknown provider: ${provider}`);
      }
    } else {
      console.log('❌ Database provider not found in schema');
    }
    
    // Check for PostgreSQL-specific features
    if (schemaContent.includes('Json')) {
      console.log('⚠️  Json type found - this is PostgreSQL specific');
      console.log('   For SQLite, use String type instead');
    }
    
    if (schemaContent.includes('directUrl')) {
      console.log('⚠️  directUrl found - this is PostgreSQL specific and should be removed for SQLite');
    }
  } else {
    console.log('❌ Prisma schema file not found');
  }
} catch (error) {
  console.log(`❌ Error reading Prisma schema: ${error.message}`);
}

// Test 3: Check for existing database files
console.log('\n3. Checking database files...');
const dbFiles = ['dev.db', 'database.db', 'sqlite.db', 'app.db'];
let foundDb = false;

dbFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ Found database file: ${file}`);
    foundDb = true;
    
    // Check file size
    const stats = fs.statSync(file);
    console.log(`   Size: ${stats.size} bytes`);
    console.log(`   Modified: ${stats.mtime.toISOString()}`);
  }
});

if (!foundDb) {
  console.log('ℹ️  No SQLite database files found (this is normal for first setup)');
}

// Test 4: Check Prisma client
console.log('\n4. Checking Prisma client...');
try {
  if (fs.existsSync('node_modules/.prisma/client')) {
    console.log('✅ Prisma client exists');
    
    // Check if it's for the right database
    const clientPath = 'node_modules/.prisma/client/index.js';
    if (fs.existsSync(clientPath)) {
      const clientContent = fs.readFileSync(clientPath, 'utf8');
      if (clientContent.includes('sqlite')) {
        console.log('✅ Prisma client configured for SQLite');
      } else if (clientContent.includes('postgresql')) {
        console.log('❌ Prisma client configured for PostgreSQL');
        console.log('   Run: npx prisma generate');
      }
    }
  } else {
    console.log('❌ Prisma client not found');
    console.log('   Run: npx prisma generate');
  }
} catch (error) {
  console.log(`❌ Error checking Prisma client: ${error.message}`);
}

// Test 5: Check environment loading
console.log('\n5. Testing environment loading...');
try {
  // Try to load environment variables
  require('dotenv').config();
  const dbUrl = process.env.DATABASE_URL;
  
  if (dbUrl) {
    console.log(`✅ DATABASE_URL loaded: ${dbUrl}`);
    
    if (dbUrl.startsWith('file:')) {
      console.log('✅ SQLite URL format correct');
    } else {
      console.log('❌ Non-SQLite URL detected');
    }
  } else {
    console.log('❌ DATABASE_URL not loaded from environment');
  }
} catch (error) {
  console.log(`❌ Error loading environment: ${error.message}`);
}

// Test 6: Provide fix recommendations
console.log('\n🔧 RECOMMENDED FIXES:');

// Check if we need to fix the schema
if (fs.existsSync('prisma/schema.prisma')) {
  const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
  if (schemaContent.includes('provider = "postgresql"')) {
    console.log('1. Fix Prisma schema provider:');
    console.log('   Change: provider = "postgresql"');
    console.log('   To:     provider = "sqlite"');
    console.log('   Remove: directUrl = env("DIRECT_URL")');
  }
  
  if (schemaContent.includes('Json')) {
    console.log('2. Fix PostgreSQL-specific types:');
    console.log('   Change: Json? to String? (for SQLite compatibility)');
  }
}

// Check if we need to fix the .env
if (fs.existsSync('.env')) {
  const envContent = fs.readFileSync('.env', 'utf8');
  const dbUrlMatch = envContent.match(/DATABASE_URL\s*=\s*["']?([^"'\n]+)["']?/);
  if (dbUrlMatch && !dbUrlMatch[1].startsWith('file:')) {
    console.log('3. Fix .env DATABASE_URL:');
    console.log('   Change to: DATABASE_URL="file:./dev.db"');
  }
  
  if (envContent.includes('DIRECT_URL')) {
    console.log('4. Remove DIRECT_URL from .env (SQLite doesn\'t need it)');
  }
}

console.log('\n📋 SETUP COMMANDS:');
console.log('After fixing the configuration:');
console.log('1. npx prisma generate');
console.log('2. npx prisma db push --force-reset');
console.log('3. npx prisma db seed (optional)');
console.log('4. npm run dev');

console.log('\n✨ Diagnostic complete!');
