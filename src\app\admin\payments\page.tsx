'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import AdminLayout from '@/components/admin/admin-layout'
import { 
  CreditCard, 
  Settings, 
  Eye, 
  EyeOff, 
  Save, 
  TestTube, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  DollarSign,
  Percent,
  ToggleLeft,
  ToggleRight
} from 'lucide-react'

interface ExtendedUser {
  role: string
  id: string
  email: string
  name?: string | null
}

interface PaymentGatewayConfig {
  id: string
  gateway: 'PAYU' | 'PHONEPE' | 'CASHFREE'
  isEnabled: boolean
  additionalFeePercent: number
  additionalFeeFixed: number
  isTestMode: boolean
  createdAt: string
  updatedAt: string
}

interface GatewayCredentials {
  [key: string]: string
}

export default function PaymentGatewaysPage() {
  const { data: session, status } = useSession()
  const [configs, setConfigs] = useState<PaymentGatewayConfig[]>([])
  const [credentials, setCredentials] = useState<Record<string, GatewayCredentials>>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showCredentials, setShowCredentials] = useState<Record<string, boolean>>({})
  const [testingGateway, setTestingGateway] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'unauthenticated') {
      redirect('/admin/login')
    }
  }, [status])

  useEffect(() => {
    if (session) {
      fetchConfigs()
    }
  }, [session])

  const fetchConfigs = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/payment-gateways')
      if (!response.ok) {
        throw new Error('Failed to fetch payment gateway configurations')
      }

      const data = await response.json()
      setConfigs(data.configs)
      setCredentials(data.credentials || {})
    } catch (error) {
      setError('Failed to load payment gateway configurations')
      console.error('Error fetching configs:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateConfig = async (gateway: string, updates: Partial<PaymentGatewayConfig>) => {
    try {
      setSaving(true)
      const response = await fetch(`/api/admin/payment-gateways/${gateway}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error('Failed to update configuration')
      }

      await fetchConfigs()
      setSuccess('Configuration updated successfully')
      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      setError('Failed to update configuration')
      console.error('Error updating config:', error)
    } finally {
      setSaving(false)
    }
  }

  const updateCredentials = async (gateway: string, newCredentials: GatewayCredentials) => {
    try {
      setSaving(true)
      const response = await fetch(`/api/admin/payment-gateways/${gateway}/credentials`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newCredentials)
      })

      if (!response.ok) {
        throw new Error('Failed to update credentials')
      }

      await fetchConfigs()
      setSuccess('Credentials updated successfully')
      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      setError('Failed to update credentials')
      console.error('Error updating credentials:', error)
    } finally {
      setSaving(false)
    }
  }

  const testGateway = async (gateway: string) => {
    try {
      setTestingGateway(gateway)
      const response = await fetch(`/api/admin/payment-gateways/${gateway}/test`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Gateway test failed')
      }

      const data = await response.json()
      setSuccess(`${gateway} test successful: ${data.message}`)
      setTimeout(() => setSuccess(''), 5000)
    } catch (error) {
      setError(`${gateway} test failed`)
      console.error('Error testing gateway:', error)
    } finally {
      setTestingGateway(null)
    }
  }

  const toggleCredentialVisibility = (gateway: string) => {
    setShowCredentials(prev => ({
      ...prev,
      [gateway]: !prev[gateway]
    }))
  }

  const getGatewayIcon = (gateway: string) => {
    switch (gateway) {
      case 'PAYU': return '💳'
      case 'PHONEPE': return '📱'
      case 'CASHFREE': return '💰'
      default: return '💳'
    }
  }

  const getGatewayColor = (gateway: string) => {
    switch (gateway) {
      case 'PAYU': return 'bg-blue-50 border-blue-200 text-blue-700'
      case 'PHONEPE': return 'bg-purple-50 border-purple-200 text-purple-700'
      case 'CASHFREE': return 'bg-green-50 border-green-200 text-green-700'
      default: return 'bg-gray-50 border-gray-200 text-gray-700'
    }
  }

  const getCredentialFields = (gateway: string) => {
    switch (gateway) {
      case 'PAYU':
        return [
          { key: 'merchantId', label: 'Merchant ID', type: 'text' },
          { key: 'merchantKey', label: 'Merchant Key', type: 'password' },
          { key: 'salt', label: 'Salt', type: 'password' }
        ]
      case 'PHONEPE':
        return [
          { key: 'merchantId', label: 'Merchant ID', type: 'text' },
          { key: 'apiKey', label: 'API Key', type: 'password' },
          { key: 'saltKey', label: 'Salt Key', type: 'password' }
        ]
      case 'CASHFREE':
        return [
          { key: 'appId', label: 'App ID', type: 'text' },
          { key: 'secretKey', label: 'Secret Key', type: 'password' }
        ]
      default:
        return []
    }
  }

  if (status === 'loading' || loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading...</div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || (session.user as ExtendedUser)?.role !== 'ADMIN') {
    redirect('/admin/login')
  }

  return (
    <AdminLayout>
      <div>
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payment Gateways</h1>
            <p className="text-sm text-gray-600">Configure payment gateways for student fee collection</p>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
            {success}
          </div>
        )}

        {/* Gateway Configuration Cards */}
        <div className="space-y-6">
          {configs.map((config) => (
            <div key={config.gateway} className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{getGatewayIcon(config.gateway)}</span>
                    <div>
                      <h2 className="text-lg font-medium text-gray-900">{config.gateway}</h2>
                      <p className="text-sm text-gray-500">
                        {config.gateway === 'PAYU' && 'PayU Money - Indian payment gateway'}
                        {config.gateway === 'PHONEPE' && 'PhonePe - UPI and digital payments'}
                        {config.gateway === 'CASHFREE' && 'Cashfree - Payment gateway and banking'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 mr-2">Test Mode</span>
                      <button
                        onClick={() => updateConfig(config.gateway, { isTestMode: !config.isTestMode })}
                        className="relative inline-flex items-center"
                      >
                        {config.isTestMode ? (
                          <ToggleRight className="h-6 w-6 text-blue-600" />
                        ) : (
                          <ToggleLeft className="h-6 w-6 text-gray-400" />
                        )}
                      </button>
                    </div>
                    
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 mr-2">Enabled</span>
                      <button
                        onClick={() => updateConfig(config.gateway, { isEnabled: !config.isEnabled })}
                        className="relative inline-flex items-center"
                      >
                        {config.isEnabled ? (
                          <ToggleRight className="h-6 w-6 text-green-600" />
                        ) : (
                          <ToggleLeft className="h-6 w-6 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Configuration Settings */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 mb-4">Configuration</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Additional Fee (Percentage)
                        </label>
                        <div className="mt-1 relative rounded-md shadow-sm">
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            max="100"
                            value={config.additionalFeePercent}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0
                              updateConfig(config.gateway, { additionalFeePercent: value })
                            }}
                            className="block w-full pr-12 border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="0.00"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <Percent className="h-4 w-4 text-gray-400" />
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Additional Fee (Fixed Amount)
                        </label>
                        <div className="mt-1 relative rounded-md shadow-sm">
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            value={config.additionalFeeFixed}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0
                              updateConfig(config.gateway, { additionalFeeFixed: value })
                            }}
                            className="block w-full pr-12 border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="0.00"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <DollarSign className="h-4 w-4 text-gray-400" />
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between pt-4">
                        <div className="flex items-center">
                          {config.isEnabled ? (
                            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500 mr-2" />
                          )}
                          <span className={`text-sm font-medium ${
                            config.isEnabled ? 'text-green-700' : 'text-red-700'
                          }`}>
                            {config.isEnabled ? 'Active' : 'Inactive'}
                          </span>
                        </div>

                        <button
                          onClick={() => testGateway(config.gateway)}
                          disabled={testingGateway === config.gateway || !config.isEnabled}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {testingGateway === config.gateway ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                              Testing...
                            </>
                          ) : (
                            <>
                              <TestTube className="h-4 w-4 mr-2" />
                              Test Gateway
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Credentials */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium text-gray-900">Credentials</h3>
                      <button
                        onClick={() => toggleCredentialVisibility(config.gateway)}
                        className="text-sm text-blue-600 hover:text-blue-800 inline-flex items-center"
                      >
                        {showCredentials[config.gateway] ? (
                          <>
                            <EyeOff className="h-4 w-4 mr-1" />
                            Hide
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 mr-1" />
                            Show
                          </>
                        )}
                      </button>
                    </div>

                    <div className="space-y-4">
                      {getCredentialFields(config.gateway).map((field) => (
                        <div key={field.key}>
                          <label className="block text-sm font-medium text-gray-700">
                            {field.label}
                          </label>
                          <input
                            type={showCredentials[config.gateway] ? 'text' : field.type}
                            value={credentials[config.gateway]?.[field.key] || ''}
                            onChange={(e) => {
                              const newCredentials = {
                                ...credentials[config.gateway],
                                [field.key]: e.target.value
                              }
                              setCredentials(prev => ({
                                ...prev,
                                [config.gateway]: newCredentials
                              }))
                            }}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder={`Enter ${field.label.toLowerCase()}`}
                          />
                        </div>
                      ))}

                      <button
                        onClick={() => updateCredentials(config.gateway, credentials[config.gateway] || {})}
                        disabled={saving}
                        className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {saving ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Credentials
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Status and Information */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className={`p-3 rounded-md border ${getGatewayColor(config.gateway)}`}>
                      <div className="text-sm font-medium">Status</div>
                      <div className="text-xs mt-1">
                        {config.isEnabled ? 'Accepting payments' : 'Disabled'}
                        {config.isTestMode && ' (Test Mode)'}
                      </div>
                    </div>

                    <div className="p-3 rounded-md border border-gray-200 bg-gray-50">
                      <div className="text-sm font-medium text-gray-700">Total Fee</div>
                      <div className="text-xs text-gray-600 mt-1">
                        {config.additionalFeePercent}% + ₹{config.additionalFeeFixed}
                      </div>
                    </div>

                    <div className="p-3 rounded-md border border-gray-200 bg-gray-50">
                      <div className="text-sm font-medium text-gray-700">Last Updated</div>
                      <div className="text-xs text-gray-600 mt-1">
                        {new Date(config.updatedAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Information Panel */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">Payment Gateway Information</h3>
              <div className="mt-2 text-sm text-blue-700">
                <ul className="list-disc pl-5 space-y-1">
                  <li>Enable test mode for development and testing purposes</li>
                  <li>Additional fees are added to the base payment amount</li>
                  <li>Percentage fees are calculated on the total amount</li>
                  <li>Fixed fees are added as a flat amount</li>
                  <li>Test gateway functionality before enabling for production</li>
                  <li>Keep credentials secure and update them regularly</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
