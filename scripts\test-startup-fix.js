#!/usr/bin/env node

/**
 * Startup Fix Verification Script
 * Tests that all startup errors have been resolved
 */

const http = require('http');
const fs = require('fs');

const BASE_URL = 'http://localhost:3000';

console.log('🧪 TESTING STARTUP ERROR FIXES\n');

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = http.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });
    
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Test functions
async function testPrismaClient() {
  console.log('1. Testing Prisma Client...');
  try {
    if (fs.existsSync('node_modules/.prisma/client')) {
      console.log('✅ Prisma client directory exists');
      
      // Try to require the Prisma client
      try {
        const { PrismaClient } = require('@prisma/client');
        console.log('✅ Prisma client can be imported');
        
        // Try to create instance (don't connect yet)
        const prisma = new PrismaClient();
        console.log('✅ Prisma client instance created');
        
        return true;
      } catch (error) {
        console.log(`❌ Prisma client import failed: ${error.message}`);
        return false;
      }
    } else {
      console.log('❌ Prisma client directory missing');
      return false;
    }
  } catch (error) {
    console.log(`❌ Prisma client test failed: ${error.message}`);
    return false;
  }
}

async function testDatabase() {
  console.log('\n2. Testing Database...');
  try {
    if (fs.existsSync('dev.db')) {
      const stats = fs.statSync('dev.db');
      console.log(`✅ Database file exists (${stats.size} bytes)`);
      
      if (stats.size > 0) {
        console.log('✅ Database file has content');
        return true;
      } else {
        console.log('❌ Database file is empty');
        return false;
      }
    } else {
      console.log('❌ Database file not found');
      return false;
    }
  } catch (error) {
    console.log(`❌ Database test failed: ${error.message}`);
    return false;
  }
}

async function testEnvironment() {
  console.log('\n3. Testing Environment Variables...');
  try {
    require('dotenv').config();
    
    const requiredVars = ['DATABASE_URL', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL'];
    let allPresent = true;
    
    requiredVars.forEach(varName => {
      if (process.env[varName]) {
        console.log(`✅ ${varName} is set`);
      } else {
        console.log(`❌ ${varName} is missing`);
        allPresent = false;
      }
    });
    
    return allPresent;
  } catch (error) {
    console.log(`❌ Environment test failed: ${error.message}`);
    return false;
  }
}

async function testServerRunning() {
  console.log('\n4. Testing Server Connection...');
  try {
    const response = await makeRequest(BASE_URL);
    console.log(`✅ Server responding (HTTP ${response.statusCode})`);
    return true;
  } catch (error) {
    console.log(`❌ Server not responding: ${error.message}`);
    return false;
  }
}

async function testNextAuthAPI() {
  console.log('\n5. Testing NextAuth API...');
  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/signin`);
    
    if (response.statusCode === 200) {
      console.log('✅ NextAuth signin endpoint responding');
      
      // Check if it's returning HTML (correct) or JSON error
      if (response.body.includes('<!DOCTYPE') || response.body.includes('<html')) {
        console.log('✅ NextAuth returning HTML page (correct)');
        return true;
      } else if (response.body.includes('CLIENT_FETCH_ERROR')) {
        console.log('❌ NextAuth still has CLIENT_FETCH_ERROR');
        return false;
      } else {
        console.log('⚠️  NextAuth returning unexpected content');
        return false;
      }
    } else {
      console.log(`❌ NextAuth signin failed: HTTP ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ NextAuth API test failed: ${error.message}`);
    return false;
  }
}

async function testHealthEndpoint() {
  console.log('\n6. Testing Health Endpoint...');
  try {
    const response = await makeRequest(`${BASE_URL}/api/system/health`);
    
    if (response.statusCode === 200) {
      console.log('✅ Health endpoint responding');
      
      try {
        const health = JSON.parse(response.body);
        if (health.overall === 'healthy') {
          console.log('✅ System health is good');
          return true;
        } else {
          console.log(`⚠️  System health: ${health.overall}`);
          return false;
        }
      } catch (parseError) {
        console.log('❌ Health endpoint returning invalid JSON');
        return false;
      }
    } else {
      console.log(`❌ Health endpoint failed: HTTP ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Health endpoint test failed: ${error.message}`);
    return false;
  }
}

async function testPortalPages() {
  console.log('\n7. Testing Portal Pages...');
  
  const pages = [
    { name: 'Admin Login', url: '/admin/login' },
    { name: 'Student Login', url: '/student/login' }
  ];
  
  let allWorking = true;
  
  for (const page of pages) {
    try {
      const response = await makeRequest(`${BASE_URL}${page.url}`);
      if (response.statusCode === 200) {
        console.log(`✅ ${page.name} page loads`);
      } else {
        console.log(`❌ ${page.name} page failed: HTTP ${response.statusCode}`);
        allWorking = false;
      }
    } catch (error) {
      console.log(`❌ ${page.name} page error: ${error.message}`);
      allWorking = false;
    }
  }
  
  return allWorking;
}

// Main test runner
async function runTests() {
  console.log('Testing startup error fixes...');
  console.log('=' .repeat(50));
  
  // Pre-server tests
  const prismaOk = await testPrismaClient();
  const dbOk = await testDatabase();
  const envOk = await testEnvironment();
  
  if (!prismaOk || !dbOk || !envOk) {
    console.log('\n❌ Pre-server tests failed. Please run the fix script first:');
    console.log('   Windows: scripts\\fix-startup-errors.bat');
    console.log('   Unix/Linux: ./scripts/fix-startup-errors.sh');
    return;
  }
  
  // Server tests (require running server)
  const serverOk = await testServerRunning();
  
  if (!serverOk) {
    console.log('\n⚠️  Server not running. Please start the development server:');
    console.log('   npm run dev');
    console.log('\nThen run this test again.');
    return;
  }
  
  const nextAuthOk = await testNextAuthAPI();
  const healthOk = await testHealthEndpoint();
  const portalsOk = await testPortalPages();
  
  // Summary
  console.log('\n🎯 TEST SUMMARY');
  console.log('=' .repeat(30));
  
  const tests = [
    { name: 'Prisma Client', result: prismaOk },
    { name: 'Database', result: dbOk },
    { name: 'Environment', result: envOk },
    { name: 'Server Connection', result: serverOk },
    { name: 'NextAuth API', result: nextAuthOk },
    { name: 'Health Endpoint', result: healthOk },
    { name: 'Portal Pages', result: portalsOk }
  ];
  
  const passed = tests.filter(t => t.result).length;
  const total = tests.length;
  
  tests.forEach(test => {
    console.log(`${test.result ? '✅' : '❌'} ${test.name}`);
  });
  
  console.log(`\nOverall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('\n🎉 ALL STARTUP ERRORS FIXED!');
    console.log('\n📋 System is ready:');
    console.log(`   Homepage: ${BASE_URL}`);
    console.log(`   Admin Portal: ${BASE_URL}/admin/login`);
    console.log(`   Student Portal: ${BASE_URL}/student/login`);
    console.log(`   Health Check: ${BASE_URL}/api/system/health`);
  } else {
    console.log('\n⚠️  Some issues remain. Check the output above for details.');
  }
}

// Run tests
runTests().catch(error => {
  console.error('Test runner error:', error);
  process.exit(1);
});
