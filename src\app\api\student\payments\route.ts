import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/payments - Get all payments for student
export const GET = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const { searchParams } = new URL(context.request.url)
      
      const status = searchParams.get('status')
      const category = searchParams.get('category')
      const search = searchParams.get('search')

      // For demo purposes, we'll create comprehensive sample payment data
      // In a real implementation, this would come from a payments table
      const currentDate = new Date()
      const academicYear = `${currentDate.getFullYear()}-${currentDate.getFullYear() + 1}`

      let samplePayments = [
        {
          id: 'pay_001',
          description: 'Tuition Fee - Semester 1',
          amount: 50000,
          dueDate: new Date(currentDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'PENDING' as const,
          category: 'TUITION',
          academicYear
        },
        {
          id: 'pay_002',
          description: 'Library Fee',
          amount: 2000,
          dueDate: new Date(currentDate.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'PENDING' as const,
          category: 'LIBRARY',
          academicYear
        },
        {
          id: 'pay_003',
          description: 'Hostel Fee - Semester 1',
          amount: 25000,
          dueDate: new Date(currentDate.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'OVERDUE' as const,
          category: 'HOSTEL',
          academicYear
        },
        {
          id: 'pay_004',
          description: 'Exam Fee - Mid Term',
          amount: 1500,
          dueDate: new Date(currentDate.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'PAID' as const,
          paymentDate: new Date(currentDate.getTime() - 35 * 24 * 60 * 60 * 1000).toISOString(),
          transactionId: 'TXN_001',
          gateway: 'PAYU',
          receiptUrl: '/receipts/pay_004.pdf',
          category: 'EXAM',
          academicYear
        },
        {
          id: 'pay_005',
          description: 'Sports Fee',
          amount: 3000,
          dueDate: new Date(currentDate.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'PAID' as const,
          paymentDate: new Date(currentDate.getTime() - 65 * 24 * 60 * 60 * 1000).toISOString(),
          transactionId: 'TXN_002',
          gateway: 'PHONEPE',
          receiptUrl: '/receipts/pay_005.pdf',
          category: 'OTHER',
          academicYear
        },
        {
          id: 'pay_006',
          description: 'Lab Fee - Computer Science',
          amount: 5000,
          dueDate: new Date(currentDate.getTime() + 45 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'PENDING' as const,
          category: 'OTHER',
          academicYear
        }
      ]

      // Apply filters
      if (status) {
        samplePayments = samplePayments.filter(p => p.status === status)
      }

      if (category) {
        samplePayments = samplePayments.filter(p => p.category === category)
      }

      if (search) {
        const searchLower = search.toLowerCase()
        samplePayments = samplePayments.filter(p => 
          p.description.toLowerCase().includes(searchLower) ||
          p.category.toLowerCase().includes(searchLower)
        )
      }

      return NextResponse.json({
        success: true,
        payments: samplePayments
      })

    } catch (error) {
      console.error('Student payments error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve payments' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)
