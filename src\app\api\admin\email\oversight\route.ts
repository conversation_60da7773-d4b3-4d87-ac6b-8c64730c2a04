import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'

// GET /api/admin/email/oversight - Get all emails across all accounts for admin oversight
export const GET = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
      const search = searchParams.get('search')
      const account = searchParams.get('account') // account type filter
      const folder = searchParams.get('folder')
      const status = searchParams.get('status')

      const skip = (page - 1) * limit

      // Build where clause for email recipients
      const whereClause: any = {
        isDeleted: false
      }

      // Filter by account type
      if (account) {
        whereClause.account = {
          accountType: account
        }
      }

      // Filter by folder
      if (folder) {
        whereClause.folder = {
          folderType: folder
        }
      }

      // Filter by status
      if (status) {
        switch (status) {
          case 'unread':
            whereClause.isRead = false
            break
          case 'read':
            whereClause.isRead = true
            break
          case 'starred':
            whereClause.email = {
              isStarred: true
            }
            break
          case 'spam':
            whereClause.email = {
              isSpam: true
            }
            break
        }
      }

      // Search functionality
      if (search) {
        whereClause.email = {
          ...whereClause.email,
          OR: [
            { subject: { contains: search, mode: 'insensitive' } },
            { body: { contains: search, mode: 'insensitive' } },
            { fromEmail: { contains: search, mode: 'insensitive' } },
            { fromName: { contains: search, mode: 'insensitive' } }
          ]
        }
      }

      // Get emails with all related data
      const emailRecipients = await prisma.emailRecipient.findMany({
        where: whereClause,
        include: {
          email: {
            include: {
              attachments: {
                select: {
                  id: true,
                  filename: true,
                  size: true
                }
              }
            }
          },
          account: {
            select: {
              email: true,
              accountType: true,
              displayName: true
            }
          },
          folder: {
            select: {
              name: true,
              folderType: true
            }
          }
        },
        orderBy: {
          email: {
            sentAt: 'desc'
          }
        },
        skip,
        take: limit
      })

      // Get total count for pagination
      const totalCount = await prisma.emailRecipient.count({
        where: whereClause
      })

      // Transform to oversight format
      const emails = emailRecipients.map(recipient => {
        const email = recipient.email
        
        // Generate preview text
        const preview = email.bodyText ? 
          email.bodyText.substring(0, 200) + (email.bodyText.length > 200 ? '...' : '') :
          email.body.replace(/<[^>]*>/g, '').substring(0, 200) + '...'

        return {
          id: email.id,
          messageId: email.messageId,
          subject: email.subject,
          fromEmail: email.fromEmail,
          fromName: email.fromName,
          toEmails: [], // Simplified for oversight view
          sentAt: email.sentAt,
          createdAt: email.createdAt,
          isRead: recipient.isRead,
          isStarred: email.isStarred,
          isSpam: email.isSpam,
          priority: email.priority,
          hasAttachments: email.attachments.length > 0,
          attachmentCount: email.attachments.length,
          folder: recipient.folder?.name || 'Unknown',
          accountEmail: recipient.account.email,
          accountType: recipient.account.accountType,
          preview
        }
      })

      return NextResponse.json({
        success: true,
        emails,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasMore: skip + emails.length < totalCount
        }
      })

    } catch (error) {
      console.error('Email oversight error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve emails' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: false
  }
)
