# 🚨 CRITICAL STARTUP ERRORS - FIX GUIDE

## 🔍 **ERROR ANALYSIS**

### **Error 1: NextAuth CLIENT_FETCH_ERROR**
- **Cause**: NextAuth receiving HTML instead of JSON
- **Root Cause**: Prisma client missing, causing API routes to fail
- **Impact**: Authentication system completely broken

### **Error 2 & 3: Prisma Client Module Not Found**
- **Cause**: `.prisma/client/default` module missing
- **Root Cause**: Prisma client not generated after schema changes
- **Impact**: Database operations failing, blocking authentication

---

## 🛠️ **AUTOMATIC FIX (Recommended)**

### **Quick Fix - Run the automated script:**

**Windows:**
```bash
scripts\fix-startup-errors.bat
```

**Unix/Linux/macOS:**
```bash
chmod +x scripts/fix-startup-errors.sh
./scripts/fix-startup-errors.sh
```

---

## 🔧 **MANUAL FIX STEPS**

### **Step 1: Stop All Running Processes**
```bash
# Kill any running development servers
# Windows: Ctrl+C in terminal or close terminal
# Unix/Linux: pkill -f "next dev"
```

### **Step 2: Clear All Caches**
```bash
# Remove Next.js cache
rm -rf .next

# Remove Prisma cache
rm -rf node_modules/.prisma

# Remove old database file
rm -f dev.db
```

### **Step 3: Regenerate Prisma Client**
```bash
# Generate Prisma client
npx prisma generate

# If it fails, install dependencies first
npm install
npx prisma generate
```

### **Step 4: Create Database Schema**
```bash
# Create database with schema
npx prisma db push --force-reset
```

### **Step 5: Seed Database (Optional)**
```bash
# Add initial data
npx prisma db seed
```

### **Step 6: Verify Setup**
```bash
# Check Prisma client exists
ls node_modules/.prisma/client/

# Check database file exists
ls -la dev.db

# Check environment variables
cat .env | grep -E "(DATABASE_URL|NEXTAUTH_SECRET|NEXTAUTH_URL)"
```

### **Step 7: Start Development Server**
```bash
npm run dev
```

---

## 🧪 **VERIFICATION TESTS**

### **Test 1: Server Startup**
- [ ] Server starts without errors
- [ ] No "Cannot find module" errors
- [ ] No NextAuth CLIENT_FETCH_ERROR

### **Test 2: Database Connection**
```bash
# Test health endpoint
curl http://localhost:3000/api/system/health
```
Expected: `{"overall": "healthy", "services": [{"service": "database", "status": "healthy"}]}`

### **Test 3: NextAuth API**
```bash
# Test NextAuth signin endpoint
curl http://localhost:3000/api/auth/signin
```
Expected: HTML signin page (not JSON error)

### **Test 4: Admin Portal**
- [ ] Visit: http://localhost:3000/admin/login
- [ ] Page loads without errors
- [ ] Login form is visible

### **Test 5: Student Portal**
- [ ] Visit: http://localhost:3000/student/login
- [ ] Page loads without errors
- [ ] Login form is visible

---

## 🔍 **TROUBLESHOOTING SPECIFIC ERRORS**

### **Error: "Cannot find module '.prisma/client/default'"**

**Diagnosis:**
```bash
# Check if Prisma client exists
ls node_modules/.prisma/client/
```

**Fix:**
```bash
npx prisma generate
```

### **Error: "Unexpected token '<', \"<!DOCTYPE \"... is not valid JSON"**

**Diagnosis:**
- NextAuth API returning HTML error page instead of JSON
- Usually caused by missing Prisma client

**Fix:**
```bash
# Regenerate Prisma client
npx prisma generate

# Clear Next.js cache
rm -rf .next

# Restart server
npm run dev
```

### **Error: "Database connection failed"**

**Diagnosis:**
```bash
# Check database URL
echo $DATABASE_URL
# Should be: file:./dev.db

# Check if database file exists
ls -la dev.db
```

**Fix:**
```bash
npx prisma db push --force-reset
```

### **Error: "NEXTAUTH_SECRET environment variable is not set"**

**Diagnosis:**
```bash
# Check environment variables
cat .env | grep NEXTAUTH_SECRET
```

**Fix:**
```bash
# Ensure .env file has:
NEXTAUTH_SECRET="development-secret-key-32-chars-long"
NEXTAUTH_URL="http://localhost:3000"
```

---

## 📊 **SUCCESS INDICATORS**

### **✅ Successful Startup Should Show:**
```
✅ Prisma client generated successfully
✅ Database schema created successfully
✅ Server starting on http://localhost:3000

Ready - started server on 0.0.0.0:3000, url: http://localhost:3000
```

### **✅ No Error Messages:**
- No "Cannot find module" errors
- No "CLIENT_FETCH_ERROR" messages
- No database connection errors
- No NextAuth configuration errors

### **✅ Working Endpoints:**
- http://localhost:3000 (Homepage)
- http://localhost:3000/api/system/health (Health check)
- http://localhost:3000/api/auth/signin (NextAuth signin)
- http://localhost:3000/admin/login (Admin portal)
- http://localhost:3000/student/login (Student portal)

---

## 🆘 **EMERGENCY RESET**

If all else fails, perform complete reset:

```bash
# 1. Stop all processes
pkill -f "next dev"

# 2. Remove all generated files
rm -rf .next
rm -rf node_modules/.prisma
rm -f dev.db

# 3. Reinstall dependencies
npm install

# 4. Regenerate everything
npx prisma generate
npx prisma db push --force-reset
npx prisma db seed

# 5. Start fresh
npm run dev
```

---

## 🎯 **FINAL VERIFICATION CHECKLIST**

After applying fixes:

- [ ] **Prisma Client**: `node_modules/.prisma/client/` exists
- [ ] **Database File**: `dev.db` exists and has size > 0
- [ ] **Environment**: All required variables in `.env`
- [ ] **Server Startup**: No errors in console
- [ ] **Health Check**: `/api/system/health` returns healthy
- [ ] **NextAuth**: `/api/auth/signin` returns HTML page
- [ ] **Admin Portal**: `/admin/login` loads correctly
- [ ] **Student Portal**: `/student/login` loads correctly

---

## 🎉 **SUCCESS!**

When all fixes are applied:
- ✅ Development server starts without errors
- ✅ Prisma client is properly generated
- ✅ Database operations work correctly
- ✅ NextAuth authentication system functions
- ✅ Both admin and student portals are accessible
- ✅ All API endpoints respond correctly

**The email server is now ready for development and testing!** 🚀
