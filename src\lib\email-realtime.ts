import { supabase } from './supabase'
import { RealtimeChannel } from '@supabase/supabase-js'

// Real-time email event types
export interface EmailEvent {
  type: 'NEW_EMAIL' | 'EMAIL_READ' | 'EMAIL_DELETED' | 'EMAIL_MOVED' | 'EMAIL_STARRED'
  accountId: string
  emailId: string
  data?: any
  timestamp: Date
}

export interface FolderEvent {
  type: 'FOLDER_CREATED' | 'FOLDER_DELETED' | 'FOLDER_UPDATED'
  accountId: string
  folderId: string
  data?: any
  timestamp: Date
}

// Email real-time manager class
export class EmailRealtimeManager {
  private channels: Map<string, RealtimeChannel> = new Map()
  private eventHandlers: Map<string, Function[]> = new Map()

  // Subscribe to email updates for a specific account
  subscribeToEmailUpdates(
    accountId: string,
    onEmailEvent: (event: EmailEvent) => void,
    onFolderEvent?: (event: FolderEvent) => void
  ): () => void {
    const channelName = `email-updates-${accountId}`
    
    // Remove existing subscription if any
    this.unsubscribe(channelName)

    // Create new channel
    const channel = supabase.channel(channelName)

    // Subscribe to email recipient changes (new emails, read status, etc.)
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'email_recipients',
        filter: `accountId=eq.${accountId}`,
      },
      (payload) => {
        this.handleEmailRecipientChange(payload, onEmailEvent)
      }
    )

    // Subscribe to email changes (starred, etc.)
    channel.on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'emails',
      },
      (payload) => {
        this.handleEmailChange(payload, accountId, onEmailEvent)
      }
    )

    // Subscribe to folder changes if handler provided
    if (onFolderEvent) {
      channel.on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'email_folders',
          filter: `accountId=eq.${accountId}`,
        },
        (payload) => {
          this.handleFolderChange(payload, onFolderEvent)
        }
      )
    }

    // Subscribe to the channel
    channel.subscribe((status) => {
      console.log(`Email realtime subscription status for ${accountId}:`, status)
    })

    // Store channel reference
    this.channels.set(channelName, channel)

    // Return unsubscribe function
    return () => this.unsubscribe(channelName)
  }

  // Subscribe to new emails only (for notifications)
  subscribeToNewEmails(
    accountId: string,
    onNewEmail: (email: any) => void
  ): () => void {
    const channelName = `new-emails-${accountId}`
    
    this.unsubscribe(channelName)

    const channel = supabase.channel(channelName)

    channel.on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'email_recipients',
        filter: `accountId=eq.${accountId}`,
      },
      async (payload) => {
        try {
          // Fetch full email details
          const { data: emailRecipient } = await supabase
            .from('email_recipients')
            .select(`
              *,
              email:emails (
                id,
                messageId,
                subject,
                fromEmail,
                fromName,
                sentAt,
                createdAt,
                priority,
                isStarred
              ),
              folder:email_folders (
                name,
                folderType
              )
            `)
            .eq('id', payload.new.id)
            .single()

          if (emailRecipient && emailRecipient.email) {
            onNewEmail({
              id: emailRecipient.email.id,
              messageId: emailRecipient.email.messageId,
              subject: emailRecipient.email.subject,
              fromEmail: emailRecipient.email.fromEmail,
              fromName: emailRecipient.email.fromName,
              receivedAt: emailRecipient.email.sentAt || emailRecipient.email.createdAt,
              priority: emailRecipient.email.priority,
              isStarred: emailRecipient.email.isStarred,
              folder: emailRecipient.folder?.name || 'INBOX',
              isRead: emailRecipient.isRead
            })
          }
        } catch (error) {
          console.error('Error fetching new email details:', error)
        }
      }
    )

    channel.subscribe()
    this.channels.set(channelName, channel)

    return () => this.unsubscribe(channelName)
  }

  // Subscribe to email queue updates (for admin monitoring)
  subscribeToEmailQueue(
    onQueueUpdate: (update: any) => void
  ): () => void {
    const channelName = 'email-queue-updates'
    
    this.unsubscribe(channelName)

    const channel = supabase.channel(channelName)

    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'email_queue',
      },
      (payload) => {
        onQueueUpdate({
          type: payload.eventType,
          queueItem: payload.new || payload.old,
          timestamp: new Date()
        })
      }
    )

    channel.subscribe()
    this.channels.set(channelName, channel)

    return () => this.unsubscribe(channelName)
  }

  // Handle email recipient changes
  private handleEmailRecipientChange(payload: any, onEmailEvent: (event: EmailEvent) => void) {
    const eventType = payload.eventType
    const newData = payload.new
    const oldData = payload.old

    let emailEventType: EmailEvent['type']
    let eventData: any = {}

    switch (eventType) {
      case 'INSERT':
        emailEventType = 'NEW_EMAIL'
        eventData = { recipientData: newData }
        break

      case 'UPDATE':
        if (oldData.isRead !== newData.isRead) {
          emailEventType = 'EMAIL_READ'
          eventData = { isRead: newData.isRead }
        } else if (oldData.folderId !== newData.folderId) {
          emailEventType = 'EMAIL_MOVED'
          eventData = { 
            fromFolderId: oldData.folderId, 
            toFolderId: newData.folderId 
          }
        } else if (oldData.isDeleted !== newData.isDeleted) {
          emailEventType = 'EMAIL_DELETED'
          eventData = { isDeleted: newData.isDeleted }
        } else {
          return // No relevant change
        }
        break

      case 'DELETE':
        emailEventType = 'EMAIL_DELETED'
        eventData = { recipientData: oldData }
        break

      default:
        return
    }

    const event: EmailEvent = {
      type: emailEventType,
      accountId: newData?.accountId || oldData?.accountId,
      emailId: newData?.emailId || oldData?.emailId,
      data: eventData,
      timestamp: new Date()
    }

    onEmailEvent(event)
  }

  // Handle email changes (starred, etc.)
  private async handleEmailChange(
    payload: any, 
    accountId: string, 
    onEmailEvent: (event: EmailEvent) => void
  ) {
    const newData = payload.new
    const oldData = payload.old

    // Check if this email affects the subscribed account
    const { data: hasAccess } = await supabase
      .from('email_recipients')
      .select('id')
      .eq('emailId', newData.id)
      .eq('accountId', accountId)
      .single()

    if (!hasAccess) return

    // Check for starred status change
    if (oldData.isStarred !== newData.isStarred) {
      const event: EmailEvent = {
        type: 'EMAIL_STARRED',
        accountId,
        emailId: newData.id,
        data: { isStarred: newData.isStarred },
        timestamp: new Date()
      }

      onEmailEvent(event)
    }
  }

  // Handle folder changes
  private handleFolderChange(payload: any, onFolderEvent: (event: FolderEvent) => void) {
    const eventType = payload.eventType
    const newData = payload.new
    const oldData = payload.old

    let folderEventType: FolderEvent['type']

    switch (eventType) {
      case 'INSERT':
        folderEventType = 'FOLDER_CREATED'
        break
      case 'UPDATE':
        folderEventType = 'FOLDER_UPDATED'
        break
      case 'DELETE':
        folderEventType = 'FOLDER_DELETED'
        break
      default:
        return
    }

    const event: FolderEvent = {
      type: folderEventType,
      accountId: newData?.accountId || oldData?.accountId,
      folderId: newData?.id || oldData?.id,
      data: newData || oldData,
      timestamp: new Date()
    }

    onFolderEvent(event)
  }

  // Unsubscribe from a specific channel
  unsubscribe(channelName: string) {
    const channel = this.channels.get(channelName)
    if (channel) {
      channel.unsubscribe()
      this.channels.delete(channelName)
    }
  }

  // Unsubscribe from all channels
  unsubscribeAll() {
    for (const [channelName, channel] of this.channels) {
      channel.unsubscribe()
    }
    this.channels.clear()
  }

  // Get active subscriptions count
  getActiveSubscriptionsCount(): number {
    return this.channels.size
  }

  // Get active subscription names
  getActiveSubscriptions(): string[] {
    return Array.from(this.channels.keys())
  }
}

// Global instance
export const emailRealtimeManager = new EmailRealtimeManager()

// Utility functions for common use cases
export function subscribeToAccountEmails(
  accountId: string,
  callbacks: {
    onNewEmail?: (email: any) => void
    onEmailRead?: (emailId: string, isRead: boolean) => void
    onEmailMoved?: (emailId: string, fromFolder: string, toFolder: string) => void
    onEmailStarred?: (emailId: string, isStarred: boolean) => void
    onEmailDeleted?: (emailId: string) => void
    onFolderChanged?: (event: FolderEvent) => void
  }
): () => void {
  return emailRealtimeManager.subscribeToEmailUpdates(
    accountId,
    (event: EmailEvent) => {
      switch (event.type) {
        case 'NEW_EMAIL':
          callbacks.onNewEmail?.(event.data)
          break
        case 'EMAIL_READ':
          callbacks.onEmailRead?.(event.emailId, event.data.isRead)
          break
        case 'EMAIL_MOVED':
          callbacks.onEmailMoved?.(
            event.emailId, 
            event.data.fromFolderId, 
            event.data.toFolderId
          )
          break
        case 'EMAIL_STARRED':
          callbacks.onEmailStarred?.(event.emailId, event.data.isStarred)
          break
        case 'EMAIL_DELETED':
          callbacks.onEmailDeleted?.(event.emailId)
          break
      }
    },
    callbacks.onFolderChanged
  )
}

// Cleanup function for when component unmounts
export function cleanupEmailSubscriptions() {
  emailRealtimeManager.unsubscribeAll()
}
