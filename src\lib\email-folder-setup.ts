import { prisma } from './prisma'

/**
 * Create default system folders for an email account
 */
export async function createDefaultFolders(accountId: string): Promise<void> {
  const defaultFolders = [
    { name: 'Inbox', folderType: 'INBOX', order: 1 },
    { name: 'Sent', folderType: 'SENT', order: 2 },
    { name: 'Drafts', folderType: 'DRAFTS', order: 3 },
    { name: 'Archive', folderType: 'ARCHIVE', order: 4 },
    { name: 'Spam', folderType: 'SPAM', order: 5 },
    { name: 'Trash', folderType: 'TRASH', order: 6 }
  ]

  for (const folder of defaultFolders) {
    try {
      await prisma.emailFolder.upsert({
        where: {
          accountId_name: {
            accountId,
            name: folder.name
          }
        },
        update: {
          folderType: folder.folderType as any,
          order: folder.order,
          isSystem: true
        },
        create: {
          accountId,
          name: folder.name,
          folderType: folder.folderType as any,
          order: folder.order,
          isSystem: true
        }
      })
    } catch (error) {
      console.error(`Failed to create folder ${folder.name} for account ${accountId}:`, error)
    }
  }
}

/**
 * Get or create a folder by type for an account
 */
export async function getFolderByType(accountId: string, folderType: string) {
  let folder = await prisma.emailFolder.findFirst({
    where: {
      accountId,
      folderType: folderType as any
    }
  })

  if (!folder) {
    // Create the folder if it doesn't exist
    const folderNames = {
      'INBOX': 'Inbox',
      'SENT': 'Sent',
      'DRAFTS': 'Drafts',
      'ARCHIVE': 'Archive',
      'SPAM': 'Spam',
      'TRASH': 'Trash'
    }

    folder = await prisma.emailFolder.create({
      data: {
        accountId,
        name: folderNames[folderType as keyof typeof folderNames] || folderType,
        folderType: folderType as any,
        isSystem: true
      }
    })
  }

  return folder
}

/**
 * Ensure all email accounts have proper folder structure
 */
export async function ensureAllAccountsHaveFolders(): Promise<void> {
  const accounts = await prisma.emailAccount.findMany({
    where: { isActive: true },
    select: { id: true }
  })

  for (const account of accounts) {
    await createDefaultFolders(account.id)
  }
}

/**
 * Move email to a specific folder for a recipient
 */
export async function moveEmailToFolder(
  emailId: string, 
  accountId: string, 
  folderType: string
): Promise<boolean> {
  try {
    const folder = await getFolderByType(accountId, folderType)
    
    await prisma.emailRecipient.updateMany({
      where: {
        emailId,
        accountId
      },
      data: {
        folderId: folder.id,
        isDeleted: folderType === 'TRASH'
      }
    })

    return true
  } catch (error) {
    console.error('Failed to move email to folder:', error)
    return false
  }
}

/**
 * Get folder statistics for an account
 */
export async function getFolderStats(accountId: string) {
  const folders = await prisma.emailFolder.findMany({
    where: { accountId },
    include: {
      _count: {
        select: {
          emails: {
            where: {
              isDeleted: false
            }
          }
        }
      }
    }
  })

  const stats = await Promise.all(
    folders.map(async (folder) => {
      const unreadCount = await prisma.emailRecipient.count({
        where: {
          folderId: folder.id,
          isRead: false,
          isDeleted: false
        }
      })

      return {
        id: folder.id,
        name: folder.name,
        folderType: folder.folderType,
        totalCount: folder._count.emails,
        unreadCount
      }
    })
  )

  return stats
}
