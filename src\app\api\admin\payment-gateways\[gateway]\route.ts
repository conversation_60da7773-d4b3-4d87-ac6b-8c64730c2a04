import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON>pi } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'

// PATCH /api/admin/payment-gateways/[gateway] - Update payment gateway configuration
export const PATCH = createSecureApi(
  async (context, body, params) => {
    try {
      const gateway = params?.gateway?.toUpperCase()
      const updateData = await context.request.json()

      if (!gateway || !['PAYU', 'PHONEPE', 'CASHFREE'].includes(gateway)) {
        return NextResponse.json(
          { error: 'Invalid gateway' },
          { status: 400 }
        )
      }

      // Validate update data
      const allowedFields = ['isEnabled', 'additionalFeePercent', 'additionalFeeFixed', 'isTestMode']
      const filteredData: any = {}

      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          filteredData[field] = updateData[field]
        }
      }

      // Validate fee values
      if (filteredData.additionalFeePercent !== undefined) {
        if (filteredData.additionalFeePercent < 0 || filteredData.additionalFeePercent > 100) {
          return NextResponse.json(
            { error: 'Additional fee percentage must be between 0 and 100' },
            { status: 400 }
          )
        }
      }

      if (filteredData.additionalFeeFixed !== undefined) {
        if (filteredData.additionalFeeFixed < 0) {
          return NextResponse.json(
            { error: 'Additional fee fixed amount must be non-negative' },
            { status: 400 }
          )
        }
      }

      // Update the configuration
      const updatedConfig = await prisma.paymentGatewayConfig.update({
        where: { gateway: gateway as any },
        data: filteredData
      })

      return NextResponse.json({
        success: true,
        config: updatedConfig,
        message: 'Payment gateway configuration updated successfully'
      })

    } catch (error) {
      console.error('Payment gateway update error:', error)
      return NextResponse.json(
        { error: 'Failed to update payment gateway configuration' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    requireAdmin: true,
    logAudit: true,
    sanitizeInput: true
  }
)
