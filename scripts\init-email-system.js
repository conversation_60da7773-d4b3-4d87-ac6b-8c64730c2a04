#!/usr/bin/env node

/**
 * Email System Initialization Script
 * Sets up default email folders, templates, and configurations
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function createDefaultEmailTemplates(adminUserId) {
  log('📧 Creating default email templates...', 'blue');

  const templates = [
    {
      name: 'welcome_student',
      subject: 'Welcome to {{instituteName}} Email System',
      bodyHtml: `
        <h2>Welcome to {{instituteName}}!</h2>
        <p>Dear {{studentName}},</p>
        <p>Your email account has been successfully created. Here are your login details:</p>
        <ul>
          <li><strong>Email:</strong> {{email}}</li>
          <li><strong>Student ID:</strong> {{studentId}}</li>
        </ul>
        <p>You can access your email through:</p>
        <ul>
          <li>Student Portal: <a href="{{portalUrl}}/student">{{portalUrl}}/student</a></li>
          <li>Email Client: Configure with our IMAP/SMTP settings</li>
        </ul>
        <p>Best regards,<br>{{instituteName}} IT Department</p>
      `,
      bodyText: `Welcome to {{instituteName}}!\n\nDear {{studentName}},\n\nYour email account has been successfully created.\n\nEmail: {{email}}\nStudent ID: {{studentId}}\n\nAccess your email at: {{portalUrl}}/student\n\nBest regards,\n{{instituteName}} IT Department`,
      templateType: 'WELCOME',
      variables: JSON.stringify(['instituteName', 'studentName', 'email', 'studentId', 'portalUrl']),
      createdById: adminUserId
    },
    {
      name: 'password_reset',
      subject: 'Password Reset - {{instituteName}} Email',
      bodyHtml: `
        <h2>Password Reset Request</h2>
        <p>Dear {{name}},</p>
        <p>You have requested a password reset for your email account: {{email}}</p>
        <p>Your new temporary password is: <strong>{{tempPassword}}</strong></p>
        <p>Please log in and change your password immediately.</p>
        <p>If you did not request this reset, please contact the IT department immediately.</p>
        <p>Best regards,<br>{{instituteName}} IT Department</p>
      `,
      bodyText: `Password Reset Request\n\nDear {{name}},\n\nYou have requested a password reset for: {{email}}\n\nTemporary password: {{tempPassword}}\n\nPlease log in and change your password immediately.\n\nBest regards,\n{{instituteName}} IT Department`,
      templateType: 'PASSWORD_RESET',
      variables: JSON.stringify(['name', 'email', 'tempPassword', 'instituteName']),
      createdById: adminUserId
    },
    {
      name: 'payment_receipt',
      subject: 'Payment Receipt - {{receiptNumber}}',
      bodyHtml: `
        <h2>Payment Receipt</h2>
        <p>Dear {{studentName}},</p>
        <p>Thank you for your payment. Here are the details:</p>
        <table border="1" style="border-collapse: collapse; width: 100%;">
          <tr><td><strong>Receipt Number:</strong></td><td>{{receiptNumber}}</td></tr>
          <tr><td><strong>Amount:</strong></td><td>₹{{amount}}</td></tr>
          <tr><td><strong>Purpose:</strong></td><td>{{purpose}}</td></tr>
          <tr><td><strong>Payment Date:</strong></td><td>{{paymentDate}}</td></tr>
          <tr><td><strong>Transaction ID:</strong></td><td>{{transactionId}}</td></tr>
          <tr><td><strong>Payment Method:</strong></td><td>{{paymentMethod}}</td></tr>
        </table>
        <p>Please keep this receipt for your records.</p>
        <p>Best regards,<br>{{instituteName}} Accounts Department</p>
      `,
      bodyText: `Payment Receipt\n\nDear {{studentName}},\n\nPayment Details:\nReceipt Number: {{receiptNumber}}\nAmount: ₹{{amount}}\nPurpose: {{purpose}}\nPayment Date: {{paymentDate}}\nTransaction ID: {{transactionId}}\nPayment Method: {{paymentMethod}}\n\nPlease keep this receipt for your records.\n\nBest regards,\n{{instituteName}} Accounts Department`,
      templateType: 'PAYMENT_RECEIPT',
      variables: JSON.stringify(['studentName', 'receiptNumber', 'amount', 'purpose', 'paymentDate', 'transactionId', 'paymentMethod', 'instituteName']),
      createdById: adminUserId
    },
    {
      name: 'account_created',
      subject: 'Email Account Created - {{email}}',
      bodyHtml: `
        <h2>Email Account Created</h2>
        <p>Dear {{name}},</p>
        <p>An email account has been created for you:</p>
        <ul>
          <li><strong>Email:</strong> {{email}}</li>
          <li><strong>Account Type:</strong> {{accountType}}</li>
          <li><strong>Department:</strong> {{department}}</li>
        </ul>
        <p>Please contact the IT department to receive your login credentials.</p>
        <p>Best regards,<br>{{instituteName}} IT Department</p>
      `,
      bodyText: `Email Account Created\n\nDear {{name}},\n\nAn email account has been created for you:\n\nEmail: {{email}}\nAccount Type: {{accountType}}\nDepartment: {{department}}\n\nPlease contact the IT department to receive your login credentials.\n\nBest regards,\n{{instituteName}} IT Department`,
      templateType: 'ACCOUNT_CREATED',
      variables: JSON.stringify(['name', 'email', 'accountType', 'department', 'instituteName']),
      createdById: adminUserId
    }
  ];

  for (const template of templates) {
    try {
      await prisma.emailTemplate.upsert({
        where: { name: template.name },
        update: template,
        create: template
      });
      log(`✅ Created template: ${template.name}`, 'green');
    } catch (error) {
      log(`❌ Failed to create template ${template.name}: ${error.message}`, 'red');
    }
  }
}

async function createDefaultSpamFilters(adminUserId) {
  log('🛡️  Creating default spam filters...', 'blue');

  const filters = [
    {
      name: 'Spam Keywords in Subject',
      description: 'Detects common spam keywords in email subjects',
      filterType: 'SUBJECT',
      pattern: '(?i)(viagra|cialis|lottery|winner|congratulations|urgent|act now|limited time)',
      action: 'MARK_SPAM',
      priority: 1,
      createdById: adminUserId
    },
    {
      name: 'Suspicious Domains',
      description: 'Blocks emails from known spam domains',
      filterType: 'DOMAIN',
      pattern: '(?i)(tempmail|10minutemail|guerrillamail|mailinator)',
      action: 'MARK_SPAM',
      priority: 2,
      createdById: adminUserId
    },
    {
      name: 'Excessive Caps in Subject',
      description: 'Detects subjects with excessive capital letters',
      filterType: 'SUBJECT',
      pattern: '^[A-Z\\s!]{10,}$',
      action: 'MARK_SPAM',
      priority: 3,
      createdById: adminUserId
    },
    {
      name: 'Phishing Attempts',
      description: 'Detects common phishing patterns',
      filterType: 'BODY',
      pattern: '(?i)(verify your account|suspended|click here immediately|update payment)',
      action: 'QUARANTINE',
      priority: 1,
      createdById: adminUserId
    }
  ];

  for (const filter of filters) {
    try {
      await prisma.spamFilter.upsert({
        where: { name: filter.name },
        update: filter,
        create: filter
      });
      log(`✅ Created spam filter: ${filter.name}`, 'green');
    } catch (error) {
      log(`❌ Failed to create spam filter ${filter.name}: ${error.message}`, 'red');
    }
  }
}

async function createDefaultPaymentGatewayConfigs() {
  log('💳 Creating default payment gateway configurations...', 'blue');

  const gateways = [
    {
      gateway: 'PAYU',
      isEnabled: false,
      additionalFeePercent: 2.0,
      additionalFeeFixed: 0,
      isTestMode: true
    },
    {
      gateway: 'PHONEPE',
      isEnabled: false,
      additionalFeePercent: 1.5,
      additionalFeeFixed: 0,
      isTestMode: true
    },
    {
      gateway: 'CASHFREE',
      isEnabled: false,
      additionalFeePercent: 1.8,
      additionalFeeFixed: 0,
      isTestMode: true
    }
  ];

  for (const gateway of gateways) {
    try {
      await prisma.paymentGatewayConfig.upsert({
        where: { gateway: gateway.gateway },
        update: gateway,
        create: gateway
      });
      log(`✅ Created payment gateway config: ${gateway.gateway}`, 'green');
    } catch (error) {
      log(`❌ Failed to create payment gateway config ${gateway.gateway}: ${error.message}`, 'red');
    }
  }
}

async function createDefaultEmailServerConfigs() {
  log('⚙️  Creating default email server configurations...', 'blue');

  const configs = [
    {
      key: 'email_domain',
      value: process.env.EMAIL_DOMAIN || 'institute.edu',
      description: 'Primary email domain for the institution'
    },
    {
      key: 'smtp_host',
      value: process.env.SMTP_HOST || 'localhost',
      description: 'SMTP server hostname for outbound emails'
    },
    {
      key: 'smtp_port',
      value: process.env.SMTP_PORT || '587',
      description: 'SMTP server port'
    },
    {
      key: 'max_attachment_size',
      value: process.env.EMAIL_ATTACHMENT_LIMIT || '********',
      description: 'Maximum attachment size in bytes (10MB)'
    },
    {
      key: 'storage_limit_per_account',
      value: process.env.EMAIL_STORAGE_LIMIT || '**********',
      description: 'Storage limit per email account in bytes (1GB)'
    },
    {
      key: 'rate_limit_per_hour',
      value: process.env.EMAIL_RATE_LIMIT || '100',
      description: 'Maximum emails per hour per account'
    },
    {
      key: 'default_from_name',
      value: process.env.DEFAULT_FROM_NAME || 'Institute Email System',
      description: 'Default sender name for system emails'
    },
    {
      key: 'default_from_email',
      value: process.env.DEFAULT_FROM_EMAIL || '<EMAIL>',
      description: 'Default sender email for system emails'
    }
  ];

  for (const config of configs) {
    try {
      await prisma.emailServerConfig.upsert({
        where: { key: config.key },
        update: config,
        create: config
      });
      log(`✅ Created email server config: ${config.key}`, 'green');
    } catch (error) {
      log(`❌ Failed to create email server config ${config.key}: ${error.message}`, 'red');
    }
  }
}

async function main() {
  log('🚀 Initializing Email System...', 'bright');
  log('=' * 50, 'cyan');

  try {
    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      log('❌ No admin user found. Please create an admin user first.', 'red');
      process.exit(1);
    }

    log(`✅ Found admin user: ${adminUser.email}`, 'green');

    // Initialize email system components
    await createDefaultEmailTemplates(adminUser.id);
    await createDefaultSpamFilters(adminUser.id);
    await createDefaultPaymentGatewayConfigs();
    await createDefaultEmailServerConfigs();

    log('\n🎉 Email system initialization completed successfully!', 'green');
    log('\n📋 Next steps:', 'cyan');
    log('1. Configure payment gateway credentials in admin panel', 'yellow');
    log('2. Update email server settings as needed', 'yellow');
    log('3. Create email accounts for students and staff', 'yellow');
    log('4. Test email functionality', 'yellow');

  } catch (error) {
    log('\n❌ Email system initialization failed:', 'red');
    log(error.message, 'red');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
if (require.main === module) {
  main();
}
