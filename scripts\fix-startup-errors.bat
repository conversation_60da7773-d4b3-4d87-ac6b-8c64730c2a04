@echo off
echo ========================================
echo FIXING CRITICAL STARTUP ERRORS
echo ========================================
echo.

echo [1/8] Stopping any running development servers...
taskkill /f /im node.exe >nul 2>&1
echo ✅ Stopped running processes

echo.
echo [2/8] Clearing Next.js cache...
if exist .next (
    rmdir /s /q .next
    echo ✅ Removed .next cache directory
) else (
    echo ℹ️  No .next cache found
)

echo.
echo [3/8] Clearing Prisma cache...
if exist node_modules\.prisma (
    rmdir /s /q node_modules\.prisma
    echo ✅ Removed Prisma cache
) else (
    echo ℹ️  No Prisma cache found
)

echo.
echo [4/8] Removing old database file...
if exist dev.db (
    del dev.db
    echo ✅ Removed old database file
) else (
    echo ℹ️  No old database file found
)

echo.
echo [5/8] Generating Prisma client...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ ERROR: Prisma client generation failed!
    echo.
    echo Trying to install dependencies first...
    call npm install
    echo.
    echo Retrying Prisma client generation...
    call npx prisma generate
    if %errorlevel% neq 0 (
        echo ❌ ERROR: Prisma client generation still failed!
        pause
        exit /b 1
    )
)
echo ✅ Prisma client generated successfully

echo.
echo [6/8] Creating database schema...
call npx prisma db push --force-reset
if %errorlevel% neq 0 (
    echo ❌ ERROR: Database schema creation failed!
    pause
    exit /b 1
)
echo ✅ Database schema created successfully

echo.
echo [7/8] Seeding database with initial data...
call npx prisma db seed
if %errorlevel% neq 0 (
    echo ⚠️  WARNING: Database seeding failed, but continuing...
    echo This is normal if seed script has issues
) else (
    echo ✅ Database seeded successfully
)

echo.
echo [8/8] Verifying setup...
if exist node_modules\.prisma\client (
    echo ✅ Prisma client exists
) else (
    echo ❌ ERROR: Prisma client still missing!
    pause
    exit /b 1
)

if exist dev.db (
    echo ✅ Database file created
) else (
    echo ❌ ERROR: Database file not created!
    pause
    exit /b 1
)

echo.
echo ========================================
echo STARTUP ERRORS FIXED!
echo ========================================
echo.
echo ✅ Prisma client generated
echo ✅ Database schema created
echo ✅ Cache cleared
echo ✅ Environment verified
echo.
echo Starting development server...
echo.
echo Student Portal: http://localhost:3000/student/login
echo Admin Portal:   http://localhost:3000/admin/login
echo Health Check:   http://localhost:3000/api/system/health
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

call npm run dev
