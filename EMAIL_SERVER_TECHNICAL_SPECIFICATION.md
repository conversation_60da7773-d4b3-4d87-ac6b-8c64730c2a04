# Email Server Technical Specification

## Overview
This document outlines the technical implementation of a comprehensive email server system built entirely on Vercel + Supabase infrastructure, designed to be self-contained and free-tier compatible.

## Architecture Overview

### Core Components
1. **Email Server API Layer** - REST endpoints simulating SMTP/IMAP/POP3 protocols
2. **Internal Message Routing** - Supabase-based email delivery and storage system
3. **Admin Management Panel** - Extended existing admin system for email oversight
4. **Student Portal** - Separate authentication system for student access
5. **Payment Gateway Integration** - Multi-gateway payment processing with receipts
6. **Email Client Compatibility** - Protocol simulation for standard email clients

### Technology Stack
- **Frontend/API**: Next.js 15 (Vercel deployment)
- **Database**: Supabase PostgreSQL (migrated from SQLite)
- **Authentication**: NextAuth + Custom student auth
- **Email Processing**: Node.js nodemailer + custom routing
- **File Storage**: Supabase Storage for attachments
- **Payment Gateways**: PayU, PhonePe, Cashfree APIs

## Database Schema Extensions

### Email System Models (Already Defined)
```prisma
model EmailAccount {
  id          String      @id @default(cuid())
  email       String      @unique
  password    String      // Hashed password
  displayName String?
  accountType EmailAccountType
  isActive    Boolean     @default(true)
  
  // Student-specific fields
  studentId   String?     @unique
  rollNumber  String?
  course      String?
  batch       String?
  
  // Institute-specific fields
  department  String?
  designation String?
  
  // Storage and limits
  storageUsed Int         @default(0)
  storageLimit Int        @default(**********) // 1GB
  
  // Email client settings
  imapEnabled Boolean     @default(true)
  pop3Enabled Boolean     @default(true)
  smtpEnabled Boolean     @default(true)
  
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdById String
  
  // Relations
  createdBy   User        @relation(fields: [createdById], references: [id])
  sentEmails  Email[]     @relation("SentEmails")
  receivedEmails EmailRecipient[]
  folders     EmailFolder[]
}

model Email {
  id          String      @id @default(cuid())
  messageId   String      @unique // RFC 5322 Message-ID
  subject     String
  body        String      // HTML content
  bodyText    String?     // Plain text version
  
  // Sender information
  fromEmail   String
  fromName    String?
  senderId    String
  
  // Email metadata
  priority    EmailPriority @default(NORMAL)
  isRead      Boolean     @default(false)
  isStarred   Boolean     @default(false)
  isSpam      Boolean     @default(false)
  isDeleted   Boolean     @default(false)
  isDraft     Boolean     @default(false)
  
  // Threading
  threadId    String?
  inReplyTo   String?
  references  String?
  
  // Delivery tracking
  sentAt      DateTime?
  deliveredAt DateTime?
  readAt      DateTime?
  
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relations
  sender      EmailAccount @relation("SentEmails", fields: [senderId], references: [id])
  recipients  EmailRecipient[]
  attachments EmailAttachment[]
}
```

### Payment System Models (Already Defined)
```prisma
model Payment {
  id              String        @id @default(cuid())
  studentId       String
  amount          Float
  currency        String        @default("INR")
  
  // Payment details
  purpose         String        // Fee type
  description     String?
  academicYear    String
  semester        String?
  
  // Payment gateway info
  gateway         PaymentGateway
  gatewayTxnId    String?
  gatewayOrderId  String?
  gatewayFee      Float         @default(0)
  gatewayFeePercent Float       @default(0)
  
  // Status tracking
  status          PaymentStatus @default(PENDING)
  paidAt          DateTime?
  failedAt        DateTime?
  refundedAt      DateTime?
  
  // Receipt
  receiptNumber   String?       @unique
  receiptUrl      String?       // PDF receipt URL
  
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  // Relations
  student         StudentAccount @relation(fields: [studentId], references: [id])
}
```

## API Endpoint Structure

### Email Server APIs
```
/api/email/
├── smtp/                    # SMTP simulation endpoints
│   ├── send                 # Send email
│   ├── auth                 # SMTP authentication
│   └── config               # SMTP configuration
├── imap/                    # IMAP simulation endpoints
│   ├── folders              # Folder management
│   ├── messages             # Message retrieval
│   ├── search               # Email search
│   └── sync                 # Synchronization
├── pop3/                    # POP3 simulation endpoints
│   ├── list                 # List messages
│   ├── retrieve             # Retrieve messages
│   └── delete               # Delete messages
├── accounts/                # Email account management
│   ├── create               # Create email account
│   ├── [id]/update          # Update account
│   ├── [id]/delete          # Delete account
│   └── [id]/reset-password  # Reset password
└── routing/                 # Internal email routing
    ├── internal             # Internal email delivery
    ├── external             # External email sending
    └── queue                # Message queue management
```

### Admin Panel APIs (Extended)
```
/api/admin/
├── email/                   # Email management
│   ├── accounts             # Email account CRUD
│   ├── oversight            # Admin oversight features
│   ├── statistics           # Usage statistics
│   └── bulk-operations      # Bulk account operations
├── payments/                # Payment management
│   ├── gateways             # Gateway configuration
│   ├── transactions         # Transaction monitoring
│   └── receipts             # Receipt management
└── students/                # Student management
    ├── accounts             # Student account CRUD
    ├── portal-access        # Portal access management
    └── email-assignments    # Email account assignments
```

### Student Portal APIs
```
/api/student/
├── auth/                    # Student authentication
│   ├── login                # Student login
│   ├── logout               # Student logout
│   └── session              # Session management
├── email/                   # Email access
│   ├── inbox                # Inbox management
│   ├── compose              # Compose emails
│   ├── folders              # Folder management
│   └── search               # Email search
├── payments/                # Fee payments
│   ├── initiate             # Initiate payment
│   ├── verify               # Verify payment
│   ├── history              # Payment history
│   └── receipts             # Download receipts
└── profile/                 # Profile management
    ├── view                 # View profile
    └── update               # Update profile
```

## Email Protocol Simulation

### SMTP Simulation
- **Authentication**: Custom token-based auth compatible with email clients
- **Message Submission**: REST endpoint accepting RFC 5322 formatted messages
- **Delivery Queue**: Supabase-based queue for message processing
- **External Delivery**: Direct SMTP using nodemailer for external recipients

### IMAP Simulation
- **Folder Structure**: Standard IMAP folders (INBOX, SENT, DRAFTS, TRASH)
- **Message Flags**: Read, Starred, Deleted status tracking
- **Search Capabilities**: Full-text search across email content
- **Synchronization**: Real-time updates using Supabase real-time features

### POP3 Simulation
- **Message Listing**: Retrieve message headers and metadata
- **Message Download**: Full message content retrieval
- **Delete Operations**: Mark messages as deleted (soft delete)

## Security Implementation

### Authentication & Authorization
- **Admin Access**: NextAuth with role-based permissions
- **Student Access**: Separate authentication system with session management
- **Email Client Access**: Custom protocol authentication for SMTP/IMAP/POP3
- **API Security**: Rate limiting, CSRF protection, input validation

### Data Protection
- **Password Hashing**: bcrypt for all password storage
- **Email Encryption**: TLS for all email transmissions
- **Database Security**: Supabase RLS (Row Level Security) policies
- **File Security**: Secure attachment storage with access controls

### Spam Protection
- **Content Filtering**: Pattern-based spam detection
- **Sender Reputation**: Internal reputation scoring
- **Rate Limiting**: Per-account sending limits
- **Quarantine System**: Suspicious email isolation

## Deployment Configuration

### Vercel Configuration
```json
{
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "DATABASE_URL": "@database_url",
    "NEXTAUTH_SECRET": "@nextauth_secret",
    "SUPABASE_URL": "@supabase_url",
    "SUPABASE_ANON_KEY": "@supabase_anon_key"
  }
}
```

### Supabase Configuration
- **Database**: PostgreSQL with email-optimized indexes
- **Storage**: File attachments with 1GB free tier limit
- **Real-time**: Email synchronization and notifications
- **Edge Functions**: Optional for email processing optimization

## Performance Optimization

### Database Optimization
- **Indexing Strategy**: Optimized indexes for email queries
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Minimized database calls with proper relations

### Serverless Optimization
- **Cold Start Mitigation**: Optimized bundle size and imports
- **Memory Management**: Efficient memory usage within Vercel limits
- **Caching Strategy**: Strategic caching for frequently accessed data

### Storage Management
- **Attachment Limits**: Per-email and per-account storage limits
- **Cleanup Procedures**: Automated cleanup of deleted emails and attachments
- **Compression**: Email content compression for storage efficiency

## Monitoring and Logging

### Email Delivery Tracking
- **Delivery Status**: Real-time delivery status tracking
- **Error Logging**: Comprehensive error logging and alerting
- **Performance Metrics**: Email processing performance monitoring

### Usage Analytics
- **Account Usage**: Storage and bandwidth usage tracking
- **Payment Analytics**: Payment success rates and gateway performance
- **System Health**: Overall system health and performance metrics

## Next Steps

1. **Database Migration**: Migrate from SQLite to Supabase PostgreSQL
2. **Core Infrastructure**: Implement email server API endpoints
3. **Admin Panel Extension**: Add email management features to existing admin panel
4. **Student Portal**: Develop separate student authentication and portal
5. **Payment Integration**: Implement multi-gateway payment processing
6. **Email Client Compatibility**: Develop protocol simulation layer
7. **Security & Anti-Spam**: Implement comprehensive security measures
8. **Testing & Optimization**: Comprehensive testing and deployment optimization

This specification provides the foundation for building a comprehensive, self-contained email server system that operates entirely within the Vercel + Supabase free tier constraints while providing professional email capabilities.
