#!/usr/bin/env node

/**
 * System Test Script
 * Tests the running email server system
 */

const http = require('http');
const https = require('https');

const BASE_URL = 'http://localhost:3000';

console.log('🧪 Testing Email Server System...\n');

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const req = protocol.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });
    
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

// Test functions
async function testHealthCheck() {
  console.log('🔍 Testing System Health...');
  try {
    const response = await makeRequest(`${BASE_URL}/api/system/health`);
    
    if (response.statusCode === 200) {
      const health = JSON.parse(response.body);
      console.log(`✅ System Health: ${health.overall.toUpperCase()}`);
      console.log(`   Services: ${health.summary.healthy}/${health.summary.total} healthy`);
      
      if (health.overall !== 'healthy') {
        console.log('⚠️  Issues found:');
        health.services.forEach(service => {
          if (service.status !== 'healthy') {
            console.log(`   - ${service.service}: ${service.status} (${service.responseTime}ms)`);
            if (service.error) console.log(`     Error: ${service.error}`);
          }
        });
      }
      return true;
    } else {
      console.log(`❌ Health check failed: HTTP ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Health check error: ${error.message}`);
    return false;
  }
}

async function testStudentPortal() {
  console.log('\n🎓 Testing Student Portal...');
  
  const tests = [
    { name: 'Student Login Page', url: '/student/login' },
    { name: 'Student Dashboard', url: '/student/dashboard' },
    { name: 'Email Inbox', url: '/student/email/inbox' },
    { name: 'Email Compose', url: '/student/email/compose' },
    { name: 'Payment Page', url: '/student/payments' }
  ];
  
  let passed = 0;
  
  for (const test of tests) {
    try {
      const response = await makeRequest(`${BASE_URL}${test.url}`);
      if (response.statusCode === 200 || response.statusCode === 302) {
        console.log(`✅ ${test.name}: OK`);
        passed++;
      } else {
        console.log(`❌ ${test.name}: HTTP ${response.statusCode}`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }
  
  console.log(`   Result: ${passed}/${tests.length} tests passed`);
  return passed === tests.length;
}

async function testAdminPortal() {
  console.log('\n👨‍💼 Testing Admin Portal...');
  
  const tests = [
    { name: 'Admin Login Page', url: '/admin/login' },
    { name: 'Admin Dashboard', url: '/admin' },
    { name: 'User Management', url: '/admin/users' },
    { name: 'Email Accounts', url: '/admin/email/accounts' },
    { name: 'Payment Settings', url: '/admin/payments' }
  ];
  
  let passed = 0;
  
  for (const test of tests) {
    try {
      const response = await makeRequest(`${BASE_URL}${test.url}`);
      if (response.statusCode === 200 || response.statusCode === 302) {
        console.log(`✅ ${test.name}: OK`);
        passed++;
      } else {
        console.log(`❌ ${test.name}: HTTP ${response.statusCode}`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }
  
  console.log(`   Result: ${passed}/${tests.length} tests passed`);
  return passed === tests.length;
}

async function testAPIEndpoints() {
  console.log('\n📡 Testing API Endpoints...');
  
  const tests = [
    { name: 'CSRF Token', url: '/api/csrf' },
    { name: 'System Health', url: '/api/system/health' },
    { name: 'Student Auth', url: '/api/auth/student', method: 'POST', expectError: true },
    { name: 'Payment Gateways', url: '/api/student/payment-gateways' }
  ];
  
  let passed = 0;
  
  for (const test of tests) {
    try {
      const options = {
        method: test.method || 'GET',
        headers: { 'Content-Type': 'application/json' }
      };
      
      if (test.method === 'POST') {
        options.body = JSON.stringify({});
      }
      
      const response = await makeRequest(`${BASE_URL}${test.url}`, options);
      
      if (test.expectError) {
        if (response.statusCode >= 400) {
          console.log(`✅ ${test.name}: Expected error (${response.statusCode})`);
          passed++;
        } else {
          console.log(`❌ ${test.name}: Expected error but got ${response.statusCode}`);
        }
      } else {
        if (response.statusCode === 200) {
          console.log(`✅ ${test.name}: OK`);
          passed++;
        } else {
          console.log(`❌ ${test.name}: HTTP ${response.statusCode}`);
        }
      }
    } catch (error) {
      if (test.expectError) {
        console.log(`✅ ${test.name}: Expected error (${error.message})`);
        passed++;
      } else {
        console.log(`❌ ${test.name}: ${error.message}`);
      }
    }
  }
  
  console.log(`   Result: ${passed}/${tests.length} tests passed`);
  return passed === tests.length;
}

async function testHomepage() {
  console.log('\n🏠 Testing Homepage...');
  try {
    const response = await makeRequest(BASE_URL);
    if (response.statusCode === 200) {
      console.log('✅ Homepage loads successfully');
      return true;
    } else {
      console.log(`❌ Homepage failed: HTTP ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Homepage error: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log(`Testing server at: ${BASE_URL}`);
  console.log('=' .repeat(50));
  
  const results = {
    homepage: await testHomepage(),
    health: await testHealthCheck(),
    studentPortal: await testStudentPortal(),
    adminPortal: await testAdminPortal(),
    apiEndpoints: await testAPIEndpoints()
  };
  
  console.log('\n🎯 TEST SUMMARY');
  console.log('=' .repeat(30));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, result]) => {
    console.log(`${result ? '✅' : '❌'} ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
  });
  
  console.log(`\nOverall: ${passed}/${total} test suites passed`);
  
  if (passed === total) {
    console.log('\n🎉 ALL TESTS PASSED - SYSTEM IS READY!');
    console.log('\n📋 Access Points:');
    console.log(`   Homepage: ${BASE_URL}`);
    console.log(`   Student Portal: ${BASE_URL}/student/login`);
    console.log(`   Admin Portal: ${BASE_URL}/admin/login`);
    console.log(`   Health Check: ${BASE_URL}/api/system/health`);
    console.log('\n🔑 Test Credentials:');
    console.log('   Admin: <EMAIL> / admin123');
    console.log('   Student: TEST001 / student123');
  } else {
    console.log('\n⚠️  Some tests failed. Check the output above for details.');
    process.exit(1);
  }
}

// Check if server is running
async function checkServer() {
  try {
    await makeRequest(BASE_URL);
    return true;
  } catch (error) {
    return false;
  }
}

// Start testing
checkServer().then(isRunning => {
  if (isRunning) {
    runTests();
  } else {
    console.log('❌ Server is not running at ' + BASE_URL);
    console.log('Please start the development server first:');
    console.log('   npm run dev');
    process.exit(1);
  }
});
