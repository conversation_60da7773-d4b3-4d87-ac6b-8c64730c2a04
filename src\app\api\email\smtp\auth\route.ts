import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

// POST /api/email/smtp/auth - Authenticate SMTP client
export const POST = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      const { email, password, clientInfo } = body

      if (!email || !password) {
        return NextResponse.json(
          { error: 'Email and password are required' },
          { status: 400 }
        )
      }

      // Find email account
      const account = await prisma.emailAccount.findUnique({
        where: { email, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        )
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, account.password)
      if (!isValidPassword) {
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        )
      }

      // Check if SMTP is enabled for this account
      if (!account.smtpEnabled) {
        return NextResponse.json(
          { error: 'SMTP access is disabled for this account' },
          { status: 403 }
        )
      }

      // Create email session
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000) // 1 hour
      const session = await prisma.emailSession.create({
        data: {
          accountId: account.id,
          sessionType: 'SMTP',
          clientInfo: clientInfo || 'Unknown client',
          ipAddress: context.clientIP,
          expiresAt,
          isActive: true
        }
      })

      // Generate JWT token for session
      const token = jwt.sign(
        {
          sessionId: session.id,
          accountId: account.id,
          email: account.email,
          type: 'smtp'
        },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '1h' }
      )

      return NextResponse.json({
        success: true,
        token,
        sessionId: session.id,
        account: {
          id: account.id,
          email: account.email,
          displayName: account.displayName,
          accountType: account.accountType
        },
        expiresAt: expiresAt.toISOString()
      })

    } catch (error) {
      console.error('SMTP auth error:', error)
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // This endpoint handles its own authentication
    logAudit: true,
    sanitizeInput: true
  }
)

// DELETE /api/email/smtp/auth - Logout SMTP session
export const DELETE = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const sessionId = searchParams.get('sessionId')

      if (!sessionId) {
        return NextResponse.json(
          { error: 'Session ID is required' },
          { status: 400 }
        )
      }

      // Deactivate session
      await prisma.emailSession.update({
        where: { id: sessionId },
        data: { isActive: false }
      })

      return NextResponse.json({
        success: true,
        message: 'Session terminated successfully'
      })

    } catch (error) {
      console.error('SMTP logout error:', error)
      return NextResponse.json(
        { error: 'Logout failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false,
    logAudit: true
  }
)

// GET /api/email/smtp/auth - Verify SMTP session
export const GET = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const token = searchParams.get('token')

      if (!token) {
        return NextResponse.json(
          { error: 'Token is required' },
          { status: 400 }
        )
      }

      // Verify JWT token
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'smtp') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      // Check if session is still active
      const session = await prisma.emailSession.findUnique({
        where: {
          id: decoded.sessionId,
          isActive: true,
          expiresAt: { gt: new Date() }
        },
        include: {
          account: {
            select: {
              id: true,
              email: true,
              displayName: true,
              accountType: true,
              isActive: true,
              smtpEnabled: true
            }
          }
        }
      })

      if (!session || !session.account.isActive || !session.account.smtpEnabled) {
        return NextResponse.json(
          { error: 'Session expired or invalid' },
          { status: 401 }
        )
      }

      // Update last activity
      await prisma.emailSession.update({
        where: { id: session.id },
        data: { lastActivity: new Date() }
      })

      return NextResponse.json({
        success: true,
        valid: true,
        account: session.account,
        sessionId: session.id,
        expiresAt: session.expiresAt.toISOString()
      })

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        return NextResponse.json(
          { error: 'Invalid token' },
          { status: 401 }
        )
      }

      console.error('SMTP session verification error:', error)
      return NextResponse.json(
        { error: 'Session verification failed' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false,
    logAudit: false
  }
)
