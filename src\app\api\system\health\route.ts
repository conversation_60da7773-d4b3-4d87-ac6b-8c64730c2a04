import { NextRequest, NextResponse } from 'next/server'
import { createSecureA<PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import { getPaymentGatewayManager } from '@/lib/payment-gateways'

interface HealthCheckResult {
  service: string
  status: 'healthy' | 'unhealthy' | 'degraded'
  responseTime: number
  details?: any
  error?: string
}

interface SystemHealth {
  overall: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  services: HealthCheckResult[]
  summary: {
    healthy: number
    unhealthy: number
    degraded: number
    total: number
  }
}

// GET /api/system/health - System health check
export const GET = createSecureApi(
  async (context) => {
    const startTime = Date.now()
    const healthChecks: HealthCheckResult[] = []

    // Database health check
    try {
      const dbStart = Date.now()
      await prisma.$queryRaw`SELECT 1`
      const dbTime = Date.now() - dbStart
      
      healthChecks.push({
        service: 'database',
        status: dbTime < 1000 ? 'healthy' : 'degraded',
        responseTime: dbTime,
        details: { connectionPool: 'active' }
      })
    } catch (error) {
      healthChecks.push({
        service: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Database connection failed'
      })
    }

    // Email system health check
    try {
      const emailStart = Date.now()
      const emailCount = await prisma.email.count({
        where: {
          sentAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      })
      const emailTime = Date.now() - emailStart
      
      healthChecks.push({
        service: 'email_system',
        status: emailTime < 2000 ? 'healthy' : 'degraded',
        responseTime: emailTime,
        details: { emailsLast24h: emailCount }
      })
    } catch (error) {
      healthChecks.push({
        service: 'email_system',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Email system check failed'
      })
    }

    // Payment gateway health checks
    const paymentGatewayManager = getPaymentGatewayManager()
    const availableGateways = paymentGatewayManager.getAvailableGateways()

    for (const gateway of availableGateways) {
      try {
        const gatewayStart = Date.now()
        const testResult = await paymentGatewayManager.testGatewayConnection(gateway)
        const gatewayTime = Date.now() - gatewayStart
        
        healthChecks.push({
          service: `payment_gateway_${gateway.toLowerCase()}`,
          status: testResult.success ? 'healthy' : 'unhealthy',
          responseTime: gatewayTime,
          details: { message: testResult.message }
        })
      } catch (error) {
        healthChecks.push({
          service: `payment_gateway_${gateway.toLowerCase()}`,
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Gateway test failed'
        })
      }
    }

    // Storage health check
    try {
      const storageStart = Date.now()
      const storageStats = await prisma.emailAccount.aggregate({
        _sum: { storageUsed: true },
        _avg: { storageUsed: true },
        _count: true
      })
      const storageTime = Date.now() - storageStart
      
      const totalStorage = storageStats._sum.storageUsed || 0
      const avgStorage = storageStats._avg.storageUsed || 0
      const accountCount = storageStats._count
      
      healthChecks.push({
        service: 'storage_system',
        status: storageTime < 1500 ? 'healthy' : 'degraded',
        responseTime: storageTime,
        details: {
          totalStorageUsed: totalStorage,
          averageStoragePerAccount: Math.round(avgStorage),
          totalAccounts: accountCount
        }
      })
    } catch (error) {
      healthChecks.push({
        service: 'storage_system',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Storage check failed'
      })
    }

    // Authentication system health check
    try {
      const authStart = Date.now()
      const activeSessionsCount = await prisma.emailSession.count({
        where: {
          isActive: true,
          expiresAt: {
            gt: new Date()
          }
        }
      })
      const authTime = Date.now() - authStart
      
      healthChecks.push({
        service: 'authentication_system',
        status: authTime < 1000 ? 'healthy' : 'degraded',
        responseTime: authTime,
        details: { activeSessions: activeSessionsCount }
      })
    } catch (error) {
      healthChecks.push({
        service: 'authentication_system',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Auth system check failed'
      })
    }

    // Calculate summary
    const summary = {
      healthy: healthChecks.filter(h => h.status === 'healthy').length,
      degraded: healthChecks.filter(h => h.status === 'degraded').length,
      unhealthy: healthChecks.filter(h => h.status === 'unhealthy').length,
      total: healthChecks.length
    }

    // Determine overall health
    let overall: 'healthy' | 'unhealthy' | 'degraded' = 'healthy'
    if (summary.unhealthy > 0) {
      overall = 'unhealthy'
    } else if (summary.degraded > 0) {
      overall = 'degraded'
    }

    const systemHealth: SystemHealth = {
      overall,
      timestamp: new Date().toISOString(),
      services: healthChecks,
      summary
    }

    // Set appropriate HTTP status based on health
    const httpStatus = overall === 'healthy' ? 200 : overall === 'degraded' ? 200 : 503

    return NextResponse.json(systemHealth, { status: httpStatus })

  } catch (error) {
    console.error('Health check error:', error)
    return NextResponse.json(
      {
        overall: 'unhealthy',
        timestamp: new Date().toISOString(),
        services: [],
        summary: { healthy: 0, degraded: 0, unhealthy: 1, total: 1 },
        error: 'Health check system failure'
      },
      { status: 503 }
    )
  },
  {
    requireAuth: false, // Health checks should be accessible without auth
    logAudit: false,
    rateLimit: {
      requests: 10,
      windowMs: 60000 // 10 requests per minute
    }
  }
)
