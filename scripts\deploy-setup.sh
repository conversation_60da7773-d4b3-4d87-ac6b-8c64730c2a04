#!/bin/bash

# SNPITC Website Deployment Setup Script
# This script helps automate the deployment preparation process

set -e

echo "🚀 SNPITC Website Deployment Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
check_node() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js is installed: $NODE_VERSION"
        
        # Check if version is 18 or higher
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 18 ]; then
            print_warning "Node.js version 18+ is recommended. Current: $NODE_VERSION"
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
}

# Check if npm is installed
check_npm() {
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_status "npm is installed: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_info "Installing dependencies..."
    npm install
    print_status "Dependencies installed successfully"
}

# Generate Prisma client
setup_prisma() {
    print_info "Setting up Prisma..."
    npx prisma generate
    print_status "Prisma client generated"
}

# Create environment file template
create_env_template() {
    if [ ! -f .env.local ]; then
        print_info "Creating environment file template..."
        cat > .env.local << EOF
# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_SECRET="your-super-secure-secret-key-change-this-to-a-secure-random-string"
NEXTAUTH_URL="http://localhost:3000"

# Admin credentials
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="change-this-secure-password"

# Site configuration
SITE_NAME="S.N. Pvt. Industrial Training Institute"
SITE_URL="http://localhost:3000"

# Email configuration (optional)
SMTP_HOST=""
SMTP_PORT="587"
SMTP_USER=""
SMTP_PASS=""
EOF
        print_status "Environment file created: .env.local"
        print_warning "Please update the environment variables in .env.local"
    else
        print_info ".env.local already exists, skipping creation"
    fi
}

# Setup database
setup_database() {
    print_info "Setting up database..."
    
    # Check if database exists
    if [ -f prisma/dev.db ]; then
        print_warning "Database already exists. Skipping initial setup."
    else
        print_info "Creating database and running migrations..."
        npx prisma db push
        print_status "Database created and schema applied"
        
        print_info "Seeding initial data..."
        npm run db:seed
        print_status "Database seeded with initial data"
    fi
}

# Build the project
build_project() {
    print_info "Building the project..."
    npm run build
    print_status "Project built successfully"
}

# Run tests
run_tests() {
    print_info "Running type check..."
    npm run type-check
    print_status "Type check passed"
    
    print_info "Running linter..."
    npm run lint
    print_status "Linting passed"
}

# Create production environment template
create_production_env() {
    if [ ! -f .env.production.template ]; then
        print_info "Creating production environment template..."
        cat > .env.production.template << EOF
# Production Environment Template
# Copy this to .env.production and update with your production values

# Database (Update with your production database URL)
DATABASE_URL="****************************************/database?sslmode=require"

# NextAuth.js (Generate a secure secret)
NEXTAUTH_SECRET="generate-a-secure-32-character-secret-key"
NEXTAUTH_URL="https://your-domain.com"

# Admin credentials (Use secure passwords)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="secure-production-password"

# Site configuration
SITE_NAME="S.N. Pvt. Industrial Training Institute"
SITE_URL="https://your-domain.com"

# Email configuration (Configure for production)
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-smtp-user"
SMTP_PASS="your-smtp-password"

# Production settings
NODE_ENV="production"
EOF
        print_status "Production environment template created"
    fi
}

# Generate secure secret
generate_secret() {
    if command -v openssl &> /dev/null; then
        SECRET=$(openssl rand -base64 32)
        print_info "Generated secure secret for NEXTAUTH_SECRET:"
        echo -e "${BLUE}$SECRET${NC}"
        print_warning "Save this secret and use it in your production environment"
    else
        print_warning "OpenSSL not found. Please generate a secure 32-character secret manually"
    fi
}

# Main execution
main() {
    echo ""
    print_info "Starting deployment setup..."
    echo ""
    
    # Run checks and setup
    check_node
    check_npm
    install_dependencies
    setup_prisma
    create_env_template
    setup_database
    create_production_env
    
    echo ""
    print_info "Running build and tests..."
    build_project
    run_tests
    
    echo ""
    print_info "Generating secure secret..."
    generate_secret
    
    echo ""
    print_status "🎉 Deployment setup completed successfully!"
    echo ""
    print_info "Next steps:"
    echo "1. Update environment variables in .env.local"
    echo "2. Test the application locally: npm run dev"
    echo "3. Choose a hosting platform from HOSTING_DEPLOYMENT.md"
    echo "4. Follow the deployment guide for your chosen platform"
    echo ""
    print_info "For detailed deployment instructions, see: HOSTING_DEPLOYMENT.md"
}

# Run the main function
main
