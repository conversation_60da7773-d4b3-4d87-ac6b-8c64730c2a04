import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import { sendEmail } from '@/lib/email-server'
import jwt from 'jsonwebtoken'

// POST /api/student/email/send - Send email for student
export const POST = createSecureApi(
  async (context) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const studentEmail = decoded.email

      // Get request body
      const body = await context.request.json()
      const {
        to,
        cc = [],
        bcc = [],
        subject,
        body: emailBody,
        priority = 'NORMAL',
        attachments = []
      } = body

      // Validate required fields
      if (!to || !Array.isArray(to) || to.length === 0) {
        return NextResponse.json(
          { error: 'At least one recipient is required' },
          { status: 400 }
        )
      }

      if (!subject || !subject.trim()) {
        return NextResponse.json(
          { error: 'Subject is required' },
          { status: 400 }
        )
      }

      if (!emailBody || !emailBody.trim()) {
        return NextResponse.json(
          { error: 'Email body is required' },
          { status: 400 }
        )
      }

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Prepare email message
      const emailMessage = {
        subject: subject.trim(),
        body: emailBody,
        bodyText: emailBody.replace(/<[^>]*>/g, ''), // Strip HTML for text version
        fromEmail: account.email,
        fromName: account.displayName || account.studentId,
        toEmails: to.map((email: string) => email.trim()).filter(Boolean),
        ccEmails: cc.map((email: string) => email.trim()).filter(Boolean),
        bccEmails: bcc.map((email: string) => email.trim()).filter(Boolean),
        attachments: attachments || [],
        priority: priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
      }

      // Send email using the email server
      const result = await sendEmail(account.id, emailMessage)

      if (result.success) {
        return NextResponse.json({
          success: true,
          messageId: result.messageId,
          message: 'Email sent successfully'
        })
      } else {
        return NextResponse.json(
          { error: result.error || 'Failed to send email' },
          { status: 500 }
        )
      }

    } catch (error) {
      console.error('Student email send error:', error)
      return NextResponse.json(
        { error: 'Failed to send email' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)
