import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// GET /api/student/email/message/[id] - Get specific email message
export const GET = createSecureApi(
  async (context, { params }: { params: { id: string } }) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const messageId = params.id

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Get the email message
      const email = await prisma.email.findFirst({
        where: {
          id: messageId,
          OR: [
            // Email sent by the student
            { fromAccountId: account.id },
            // Email received by the student
            {
              recipients: {
                some: {
                  accountId: account.id,
                  isDeleted: false
                }
              }
            }
          ],
          isDeleted: false
        },
        include: {
          attachments: {
            select: {
              id: true,
              filename: true,
              originalName: true,
              size: true,
              mimeType: true,
              path: true
            }
          },
          recipients: {
            where: {
              accountId: account.id
            },
            include: {
              folder: {
                select: {
                  id: true,
                  name: true,
                  folderType: true
                }
              }
            }
          }
        }
      })

      if (!email) {
        return NextResponse.json(
          { error: 'Email not found' },
          { status: 404 }
        )
      }

      // Mark as read if it's in the student's inbox and not read yet
      const recipient = email.recipients[0]
      if (recipient && !recipient.isRead) {
        await prisma.emailRecipient.update({
          where: { id: recipient.id },
          data: { 
            isRead: true,
            readAt: new Date()
          }
        })
      }

      // Format email for response
      const formattedEmail = {
        id: email.id,
        messageId: email.messageId,
        subject: email.subject,
        body: email.body,
        bodyText: email.bodyText,
        fromEmail: email.fromEmail,
        fromName: email.fromName,
        toEmails: email.toEmails || [],
        ccEmails: email.ccEmails || [],
        bccEmails: email.bccEmails || [],
        priority: email.priority,
        isStarred: recipient?.isStarred || false,
        isRead: recipient?.isRead || false,
        folder: recipient?.folder || null,
        attachments: email.attachments,
        sentAt: email.sentAt,
        createdAt: email.createdAt,
        updatedAt: email.updatedAt
      }

      return NextResponse.json({
        success: true,
        email: formattedEmail
      })

    } catch (error) {
      console.error('Student email get error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve email' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: false
  }
)

// PATCH /api/student/email/message/[id] - Update email properties (star, read, folder)
export const PATCH = createSecureApi(
  async (context, { params }: { params: { id: string } }) => {
    try {
      // Get student from token
      const authHeader = context.request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }

      const token = authHeader.substring(7)
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'fallback-secret'
      ) as any

      if (decoded.type !== 'student') {
        return NextResponse.json(
          { error: 'Invalid token type' },
          { status: 401 }
        )
      }

      const studentId = decoded.studentId
      const messageId = params.id

      // Get request body
      const body = await context.request.json()
      const { isStarred, isRead, folderId, action } = body

      // Get student's email account
      const account = await prisma.emailAccount.findFirst({
        where: {
          studentId,
          accountType: 'STUDENT_ID',
          isActive: true
        }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Student email account not found' },
          { status: 404 }
        )
      }

      // Get the email recipient record
      const recipient = await prisma.emailRecipient.findFirst({
        where: {
          email: {
            id: messageId,
            isDeleted: false
          },
          accountId: account.id,
          isDeleted: false
        }
      })

      if (!recipient) {
        return NextResponse.json(
          { error: 'Email not found' },
          { status: 404 }
        )
      }

      // Handle different actions
      const updateData: any = {}

      if (typeof isStarred === 'boolean') {
        updateData.isStarred = isStarred
      }

      if (typeof isRead === 'boolean') {
        updateData.isRead = isRead
        if (isRead) {
          updateData.readAt = new Date()
        }
      }

      if (folderId) {
        // Verify the folder belongs to the student
        const folder = await prisma.emailFolder.findFirst({
          where: {
            id: folderId,
            accountId: account.id
          }
        })

        if (!folder) {
          return NextResponse.json(
            { error: 'Folder not found' },
            { status: 404 }
          )
        }

        updateData.folderId = folderId
      }

      if (action === 'delete') {
        // Move to trash folder
        const trashFolder = await prisma.emailFolder.findFirst({
          where: {
            accountId: account.id,
            folderType: 'TRASH'
          }
        })

        if (trashFolder) {
          updateData.folderId = trashFolder.id
        } else {
          updateData.isDeleted = true
        }
      }

      if (action === 'archive') {
        // Move to archive folder
        const archiveFolder = await prisma.emailFolder.findFirst({
          where: {
            accountId: account.id,
            folderType: 'ARCHIVE'
          }
        })

        if (archiveFolder) {
          updateData.folderId = archiveFolder.id
        }
      }

      // Update the recipient record
      const updatedRecipient = await prisma.emailRecipient.update({
        where: { id: recipient.id },
        data: updateData
      })

      return NextResponse.json({
        success: true,
        message: 'Email updated successfully',
        recipient: {
          id: updatedRecipient.id,
          isStarred: updatedRecipient.isStarred,
          isRead: updatedRecipient.isRead,
          folderId: updatedRecipient.folderId
        }
      })

    } catch (error) {
      console.error('Student email update error:', error)
      return NextResponse.json(
        { error: 'Failed to update email' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: false, // We handle auth manually
    logAudit: true,
    sanitizeInput: true
  }
)
