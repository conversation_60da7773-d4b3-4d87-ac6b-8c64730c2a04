import { NextRequest, NextResponse } from 'next/server'
import { createSecure<PERSON><PERSON> } from '@/lib/secure-api'
import { sendEmail, saveDraft } from '@/lib/email-delivery'
import { EmailMessage } from '@/lib/email-server'
import { prisma } from '@/lib/prisma'

// POST /api/email/smtp/send - Send email via SMTP simulation
export const POST = createSecureApi(
  async (context) => {
    try {
      const body = await context.request.json()
      
      const {
        to,
        cc,
        bcc,
        subject,
        body: emailBody,
        bodyText,
        attachments,
        priority = 'NORMAL',
        inReplyTo,
        references,
        isDraft = false,
        senderAccountId
      } = body

      // Validate required fields
      if (!senderAccountId) {
        return NextResponse.json(
          { error: 'Sender account ID is required' },
          { status: 400 }
        )
      }

      if (!subject || !emailBody) {
        return NextResponse.json(
          { error: 'Subject and body are required' },
          { status: 400 }
        )
      }

      if (!to || !Array.isArray(to) || to.length === 0) {
        return NextResponse.json(
          { error: 'At least one recipient is required' },
          { status: 400 }
        )
      }

      // Get sender account details
      const senderAccount = await prisma.emailAccount.findUnique({
        where: { id: senderAccountId, isActive: true }
      })

      if (!senderAccount) {
        return NextResponse.json(
          { error: 'Sender account not found or inactive' },
          { status: 404 }
        )
      }

      // Prepare email message
      const emailMessage: EmailMessage = {
        subject,
        body: emailBody,
        bodyText,
        fromEmail: senderAccount.email,
        fromName: senderAccount.displayName || undefined,
        toEmails: Array.isArray(to) ? to : [to],
        ccEmails: cc && Array.isArray(cc) ? cc : [],
        bccEmails: bcc && Array.isArray(bcc) ? bcc : [],
        priority: priority as 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT',
        inReplyTo,
        references,
        isDraft,
        attachments: attachments || []
      }

      // Send email or save as draft
      const result = isDraft 
        ? await saveDraft(senderAccountId, emailMessage)
        : await sendEmail(senderAccountId, emailMessage)

      if (result.success) {
        return NextResponse.json({
          success: true,
          messageId: result.messageId || (result as any).draftId,
          message: isDraft ? 'Draft saved successfully' : 'Email sent successfully'
        })
      } else {
        return NextResponse.json(
          { error: result.error || 'Failed to process email' },
          { status: 500 }
        )
      }

    } catch (error) {
      console.error('SMTP send error:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true,
    logAudit: true,
    sanitizeInput: true
  }
)

// GET /api/email/smtp/send - Get SMTP configuration for email clients
export const GET = createSecureApi(
  async (context) => {
    try {
      const { searchParams } = new URL(context.request.url)
      const accountId = searchParams.get('accountId')

      if (!accountId) {
        return NextResponse.json(
          { error: 'Account ID is required' },
          { status: 400 }
        )
      }

      // Verify account exists and user has access
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId, isActive: true }
      })

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or inactive' },
          { status: 404 }
        )
      }

      // Return SMTP configuration for email clients
      const smtpConfig = {
        server: process.env.NEXTAUTH_URL || 'localhost:3000',
        port: 587, // Standard SMTP submission port
        security: 'STARTTLS',
        authentication: 'required',
        username: account.email,
        // Note: Password would be provided separately through authentication
        settings: {
          outgoingServer: `${process.env.NEXTAUTH_URL || 'localhost:3000'}/api/email/smtp`,
          port: 587,
          encryption: 'STARTTLS',
          authMethod: 'password'
        },
        limits: {
          maxMessageSize: '25MB',
          maxRecipientsPerMessage: 100,
          maxMessagesPerHour: 100
        }
      }

      return NextResponse.json({
        success: true,
        config: smtpConfig
      })

    } catch (error) {
      console.error('SMTP config error:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  {
    requireAuth: true
  }
)
