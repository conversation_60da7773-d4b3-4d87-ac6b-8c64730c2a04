@echo off
echo ========================================
echo EMAIL SERVER STARTUP VERIFICATION
echo ========================================
echo.

echo [1/5] Checking Environment Setup...
if not exist .env (
    echo ERROR: .env file not found!
    echo Please ensure .env file exists with required variables.
    pause
    exit /b 1
)
echo ✅ Environment file found

echo.
echo [2/5] Generating Prisma Client...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ERROR: Prisma client generation failed!
    pause
    exit /b 1
)
echo ✅ Prisma client generated successfully

echo.
echo [3/5] Setting up Database...
call npx prisma db push --force-reset
if %errorlevel% neq 0 (
    echo ERROR: Database setup failed!
    echo.
    echo Trying to reset database...
    call scripts\reset-database.bat
    if %errorlevel% neq 0 (
        echo ERROR: Database reset also failed!
        pause
        exit /b 1
    )
)
echo ✅ Database schema created successfully

echo.
echo [4/5] Checking Dependencies...
call npm list --depth=0 > nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Some dependencies may be missing
    echo Running npm install...
    call npm install
)
echo ✅ Dependencies verified

echo.
echo [5/5] Starting Development Server...
echo.
echo ========================================
echo STARTING EMAIL SERVER ON PORT 3000
echo ========================================
echo.
echo Student Portal: http://localhost:3000/student/login
echo Admin Portal:   http://localhost:3000/admin/login
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

call npm run dev
