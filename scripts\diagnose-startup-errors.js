#!/usr/bin/env node

/**
 * Startup Error Diagnostic Script
 * Diagnoses NextAuth and Prisma client issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 STARTUP ERROR DIAGNOSTIC\n');

let issuesFound = 0;

// Test 1: Check Prisma Client
console.log('1. Checking Prisma Client...');
try {
  const prismaClientPath = 'node_modules/.prisma/client';
  if (fs.existsSync(prismaClientPath)) {
    console.log('✅ Prisma client directory exists');
    
    // Check for specific files
    const indexPath = path.join(prismaClientPath, 'index.js');
    const defaultPath = path.join(prismaClientPath, 'default.js');
    
    if (fs.existsSync(indexPath)) {
      console.log('✅ Prisma client index.js exists');
    } else {
      console.log('❌ Prisma client index.js missing');
      issuesFound++;
    }
    
    if (fs.existsSync(defaultPath)) {
      console.log('✅ Prisma client default.js exists');
    } else {
      console.log('❌ Prisma client default.js missing');
      issuesFound++;
    }
  } else {
    console.log('❌ Prisma client directory missing');
    console.log('   Expected: node_modules/.prisma/client/');
    issuesFound++;
  }
} catch (error) {
  console.log(`❌ Error checking Prisma client: ${error.message}`);
  issuesFound++;
}

// Test 2: Check Database Configuration
console.log('\n2. Checking Database Configuration...');
try {
  if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');
    
    // Check DATABASE_URL
    const dbUrlMatch = envContent.match(/DATABASE_URL\s*=\s*["']?([^"'\n]+)["']?/);
    if (dbUrlMatch) {
      const dbUrl = dbUrlMatch[1];
      console.log(`✅ DATABASE_URL found: ${dbUrl}`);
      
      if (dbUrl.startsWith('file:')) {
        console.log('✅ SQLite configuration detected');
      } else {
        console.log('⚠️  Non-SQLite database detected');
      }
    } else {
      console.log('❌ DATABASE_URL not found in .env');
      issuesFound++;
    }
    
    // Check NextAuth variables
    const nextAuthSecret = envContent.match(/NEXTAUTH_SECRET\s*=\s*["']?([^"'\n]+)["']?/);
    const nextAuthUrl = envContent.match(/NEXTAUTH_URL\s*=\s*["']?([^"'\n]+)["']?/);
    
    if (nextAuthSecret) {
      console.log('✅ NEXTAUTH_SECRET found');
    } else {
      console.log('❌ NEXTAUTH_SECRET missing');
      issuesFound++;
    }
    
    if (nextAuthUrl) {
      console.log(`✅ NEXTAUTH_URL found: ${nextAuthUrl[1]}`);
    } else {
      console.log('❌ NEXTAUTH_URL missing');
      issuesFound++;
    }
  } else {
    console.log('❌ .env file not found');
    issuesFound++;
  }
} catch (error) {
  console.log(`❌ Error checking environment: ${error.message}`);
  issuesFound++;
}

// Test 3: Check Database File
console.log('\n3. Checking Database File...');
try {
  if (fs.existsSync('dev.db')) {
    const stats = fs.statSync('dev.db');
    console.log(`✅ Database file exists (${stats.size} bytes)`);
    
    if (stats.size === 0) {
      console.log('⚠️  Database file is empty');
      issuesFound++;
    }
  } else {
    console.log('❌ Database file (dev.db) not found');
    issuesFound++;
  }
} catch (error) {
  console.log(`❌ Error checking database file: ${error.message}`);
  issuesFound++;
}

// Test 4: Check NextAuth Configuration
console.log('\n4. Checking NextAuth Configuration...');
try {
  const authRoutePath = 'src/app/api/auth/[...nextauth]/route.ts';
  if (fs.existsSync(authRoutePath)) {
    console.log('✅ NextAuth route file exists');
    
    const authContent = fs.readFileSync(authRoutePath, 'utf8');
    if (authContent.includes('authOptions')) {
      console.log('✅ NextAuth route imports authOptions');
    } else {
      console.log('❌ NextAuth route missing authOptions import');
      issuesFound++;
    }
  } else {
    console.log('❌ NextAuth route file missing');
    issuesFound++;
  }
  
  const authConfigPath = 'src/lib/auth.ts';
  if (fs.existsSync(authConfigPath)) {
    console.log('✅ Auth configuration file exists');
    
    const authConfigContent = fs.readFileSync(authConfigPath, 'utf8');
    if (authConfigContent.includes('prisma')) {
      console.log('✅ Auth configuration uses Prisma');
    } else {
      console.log('⚠️  Auth configuration doesn\'t use Prisma');
    }
  } else {
    console.log('❌ Auth configuration file missing');
    issuesFound++;
  }
} catch (error) {
  console.log(`❌ Error checking NextAuth configuration: ${error.message}`);
  issuesFound++;
}

// Test 5: Check Next.js Cache
console.log('\n5. Checking Next.js Cache...');
try {
  if (fs.existsSync('.next')) {
    console.log('⚠️  Next.js cache exists (may need clearing)');
    
    const cacheStats = fs.statSync('.next');
    console.log(`   Cache modified: ${cacheStats.mtime.toISOString()}`);
  } else {
    console.log('✅ No Next.js cache found');
  }
} catch (error) {
  console.log(`❌ Error checking Next.js cache: ${error.message}`);
}

// Test 6: Check Package Dependencies
console.log('\n6. Checking Package Dependencies...');
try {
  if (fs.existsSync('package.json')) {
    const packageContent = fs.readFileSync('package.json', 'utf8');
    const packageData = JSON.parse(packageContent);
    
    const requiredDeps = ['@prisma/client', 'next-auth', 'prisma'];
    requiredDeps.forEach(dep => {
      if (packageData.dependencies?.[dep] || packageData.devDependencies?.[dep]) {
        console.log(`✅ ${dep} dependency found`);
      } else {
        console.log(`❌ ${dep} dependency missing`);
        issuesFound++;
      }
    });
  } else {
    console.log('❌ package.json not found');
    issuesFound++;
  }
} catch (error) {
  console.log(`❌ Error checking dependencies: ${error.message}`);
  issuesFound++;
}

// Summary and Recommendations
console.log('\n📊 DIAGNOSTIC SUMMARY');
console.log('========================');
console.log(`Issues found: ${issuesFound}`);

if (issuesFound === 0) {
  console.log('✅ No issues detected - configuration appears correct');
  console.log('\nIf you\'re still experiencing errors:');
  console.log('1. Try clearing cache: rm -rf .next');
  console.log('2. Restart development server: npm run dev');
} else {
  console.log('❌ Issues detected that need fixing');
  
  console.log('\n🔧 RECOMMENDED FIXES:');
  
  if (!fs.existsSync('node_modules/.prisma/client')) {
    console.log('1. Generate Prisma client: npx prisma generate');
  }
  
  if (!fs.existsSync('dev.db')) {
    console.log('2. Create database: npx prisma db push --force-reset');
  }
  
  if (fs.existsSync('.next')) {
    console.log('3. Clear Next.js cache: rm -rf .next');
  }
  
  console.log('\n🚀 QUICK FIX:');
  console.log('Run the automated fix script:');
  console.log('  Windows: scripts\\fix-startup-errors.bat');
  console.log('  Unix/Linux: ./scripts/fix-startup-errors.sh');
}

console.log('\n✨ Diagnostic complete!');
