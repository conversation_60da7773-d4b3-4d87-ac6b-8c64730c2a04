import React from 'react'

interface CheckboxProps {
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  className?: string
  onClick?: (e: React.MouseEvent) => void
}

export function Checkbox({ checked = false, onCheckedChange, className = '', onClick }: CheckboxProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onCheckedChange?.(e.target.checked)
  }

  return (
    <input
      type="checkbox"
      checked={checked}
      onChange={handleChange}
      onClick={onClick}
      className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${className}`}
    />
  )
}
